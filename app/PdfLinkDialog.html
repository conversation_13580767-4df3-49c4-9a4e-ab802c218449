<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <style>
    body {
      font-family: sans-serif;
      padding: 20px;
      text-align: center;
    }
    .message {
      font-size: 16px;
      color: #333;
    }
    #formMessage {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #333;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
    }
    #formMessage::before {
        content: '';
        width: 16px;
        height: 16px;
        margin-bottom: 8px;
        border: 2px solid #CCC;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    #formMessage::after {
        content: '';
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.5);
        z-index: -1;
    }
    #formMessage:empty {
        display: none !important;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <p id="formMessage" class="message">PDF生成中...</p>

  <script>
    // ページ読み込み時にPDF URLを取得して別タブで開く
    window.onload = function() {
      google.script.run
        .withSuccessHandler(function(pdfUrl) {
          if (pdfUrl) {
            // 別タブでPDFを開く
            window.open(pdfUrl, '_blank');
            
            // ダイアログを閉じる
            google.script.host.close();
          }
        })
        .withFailureHandler(function(error) {
          document.querySelector('p').textContent = 'エラーが発生しました';
        })
        .getTempPdfUrl();
    };
  </script>
</body>
</html>