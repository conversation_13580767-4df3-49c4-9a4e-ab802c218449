/**
 * 外部APIから銀行一覧を取得
 * @return {Object[]} 銀行一覧配列
 */
function getBanksFromAPI() {
    try {
        const scriptProps = PropertiesService.getScriptProperties();
        const apiUrl = scriptProps.getProperty('BANK_API_URL');
        const apiKey = scriptProps.getProperty('BANK_API_KEY');
        
        const response = UrlFetchApp.fetch(`${apiUrl}?apikey=${apiKey}&page=1&limit=2000`, {
            method: 'GET',
            muteHttpExceptions: true
        });
        
        if (response.getResponseCode() !== 200) {
            throw new Error('銀行API呼び出しエラー: ' + response.getResponseCode());
        }
        
        const data = JSON.parse(response.getContentText());
        return data.banks || [];
        
    } catch (error) {
        console.error('銀行一覧取得エラー:', error);
        return [];
    }
}

/**
 * 外部APIから銀行詳細を取得
 * @param {string} bankCode 銀行コード
 * @return {Object|null} 銀行詳細データ
 */
function getBankDetailFromAPI(bankCode) {
    try {
        if (!bankCode) {
            return null;
        }
        
        const scriptProps = PropertiesService.getScriptProperties();
        const apiUrl = scriptProps.getProperty('BANK_API_URL');
        const apiKey = scriptProps.getProperty('BANK_API_KEY');
        
        const response = UrlFetchApp.fetch(`${apiUrl}/${bankCode}?apikey=${apiKey}`, {
            method: 'GET',
            muteHttpExceptions: true
        });
        
        if (response.getResponseCode() !== 200) {
            throw new Error('銀行詳細API呼び出しエラー: ' + response.getResponseCode());
        }
        
        const data = JSON.parse(response.getContentText());
        return data || null;
        
    } catch (error) {
        console.error('銀行詳細取得エラー:', error);
        return null;
    }
}

/**
 * 外部APIから支店一覧を取得
 * @param {string} bankCode 銀行コード
 * @return {Object[]} 支店一覧配列
 */
function getBranchesFromAPI(bankCode) {
    try {
        if (!bankCode) {
            return [];
        }
        
        const scriptProps = PropertiesService.getScriptProperties();
        const apiUrl = scriptProps.getProperty('BANK_API_URL');
        const apiKey = scriptProps.getProperty('BANK_API_KEY');
        
        const response = UrlFetchApp.fetch(`${apiUrl}/${bankCode}/branches?apikey=${apiKey}&page=1&limit=2000`, {
            method: 'GET',
            muteHttpExceptions: true
        });
        
        if (response.getResponseCode() !== 200) {
        throw new Error('支店一覧API呼び出しエラー: ' + response.getResponseCode());
        }
        
        const data = JSON.parse(response.getContentText());
        return data.branches || [];
        
    } catch (error) {
        console.error('支店一覧取得エラー:', error);
        return [];
    }
}

/**
 * 外部APIから支店詳細を取得
 * @param {string} bankCode 銀行コード
 * @param {string} branchCode 支店コード
 * @return {Object|null} 支店詳細データ
 */
function getBranchDetailFromAPI(bankCode, branchCode) {
    try {
        if (!bankCode || !branchCode) {
            return null;
        }
        
        const scriptProps = PropertiesService.getScriptProperties();
        const apiUrl = scriptProps.getProperty('BANK_API_URL');
        const apiKey = scriptProps.getProperty('BANK_API_KEY');
        
        const response = UrlFetchApp.fetch(`${apiUrl}/${bankCode}/branches/${branchCode}?apikey=${apiKey}`, {
            method: 'GET',
            muteHttpExceptions: true
        });
        
        if (response.getResponseCode() !== 200) {
            throw new Error('支店詳細API呼び出しエラー: ' + response.getResponseCode());
        }
        
        const data = JSON.parse(response.getContentText());
        return data.branch || null;
        
    } catch (error) {
        console.error('支店詳細取得エラー:', error);
        return null;
    }
}