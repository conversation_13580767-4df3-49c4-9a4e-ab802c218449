/**
 * ISO形式の日時文字列をY-m-d H:i:s形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d H:i:s形式の日時文字列
 */
function formatDateTime(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * ISO形式の日時文字列をY-m-d形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d形式の日付文字列
 */
function formatDate(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * 融資情報一覧を取得（loan_informationsテーブルとclientsテーブルを結合）
 * @param {string=} branchName 部分検索する関連する支店名。省略時は絞り込みなし
 * @param {string=} bankName 部分検索する関連する銀行名。省略時は絞り込みなし
 * @param {string=} clientName 部分検索するクライアント名。省略時は絞り込みなし
 * @return {Object[]} Supabase から返ってきた融資情報配列
 */
function fetchMyLoanInformations(branchName, bankName, clientName) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken) {
      const errorMsg = 'SUPABASE_ACCESS_TOKEN が設定されていません。';
      SpreadsheetApp.getUi().alert(
        '設定エラー',
        errorMsg + '\n\n管理者にお問い合わせください。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // PostgreSQL RPC関数を使用して融資情報を取得
    const payload = {
      search_bank_name: bankName || null,
      search_branch_name: branchName || null,
      search_client_name: clientName || null,
      filter_client_id: (clientId && clientId.trim() !== '') ? clientId : null
    };

    const url = `${supabaseUrl}/rest/v1/rpc/get_loan_informations_with_clients`;
    const res = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      const errorMsg = '融資情報取得エラー: ' + (err.message || JSON.stringify(err));
      SpreadsheetApp.getUi().alert(
        '融資情報データ取得エラー',
        'Supabaseからの融資情報データ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    // 既にダイアログ表示済みでない場合のみ表示
    if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('融資情報取得エラー')) {
      SpreadsheetApp.getUi().alert(
        '予期しないエラー',
        '融資情報データの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
    throw error;
  }
}

/**
 * 検索ボタンに割り当てる関数。
 * 銀行名、支店名、クライアント名を部分検索し、結果をシートに出力します
 */
function searchLoanInformations() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const bankName = sheet.getRange('D4').getDisplayValue().trim();
  const branchName = sheet.getRange('D5').getDisplayValue().trim();
  const clientName = sheet.getRange('D6').getDisplayValue().trim();
  const startRow = 16;
  const idCol = 1;   // A 列
  const dataStartCol = 3;  // C 列
  const numCols = 51;  // C〜BC の列数（全融資情報詳細を表示）
  const lastRow = sheet.getMaxRows();

  try {
    const loanInformations = fetchMyLoanInformations(branchName, bankName, clientName);

    // ─── クリア ───
    sheet.getRange(startRow, idCol, lastRow - (startRow - 1), 1).clearContent();
    sheet.getRange(startRow, dataStartCol, lastRow - (startRow - 1), numCols).clearContent();

    if (loanInformations.length === 0) {
      sheet.getRange(startRow, dataStartCol).setValue('該当なし');
      return;
    }

    // ─── 出力用配列を作成 ───
    // A列用：id
    const ids = loanInformations.map(l => [l.id]);

    // C〜BC列用：全融資情報詳細フィールド
    const values = loanInformations.map(l => [
      l.bank_name || '',                        // 銀行名
      l.branch_name || '',                      // 支店名
      l.max_loan_amount || '',                  // 総額いくらまで融資可能か
      l.total_loan_balance || '',               // 借入残高合計
      l.total_collateral_value || '',           // 担保評価合計
      l.total_bank_risk_amount || '',           // 銀行リスク額合計
      l.max_concurrent_loans || '',             // 同時に何件融資可能か
      l.loan_execution_count || '',             // 融資取組件数
      l.avg_response_days || '',                // 平均回答日数
      l.avg_response_loan_ratio || '',          // 平均回答融資割合
      l.branch_manager_limit || '',             // 支店長権限額
      l.officer_approval_limit || '',           // 担当役員の決裁権限
      l.minimum_interest_rate || '',            // 下限金利
      l.minimum_fee || '',                      // 下限手数料
      l.max_project_period || '',               // 最長PJ期間
      l.wooden_useful_life || '',               // 木造耐用年数〇年
      l.steel_useful_life || '',                // 鉄骨耐用年数〇年
      l.rc_useful_life || '',                   // RC耐用年数〇年
      l.target_area || '',                      // エリア
      l.required_equity_ratio || '',            // 自己資金何％必要
      l.no_inspection_cert || '',               // 検査済証なし
      l.old_earthquake_standard || '',          // 旧耐震
      l.floor_area_ratio_over || '',            // 容積率オーバー
      l.building_coverage_over || '',           // 建蔽率オーバー
      l.land_only || '',                        // 底地
      l.leased_land || '',                      // 借地
      l.eviction_case || '',                    // 立ち退き
      l.third_party_for || '',                  // 3為
      l.construction_cost || '',                // 建築費用
      l.demolition_cost || '',                  // 解体費用
      l.working_capital || '',                  // 運転資金
      l.tax_payment || '',                      // 納税費用
      l.bonus_fund || '',                       // 賞与資金
      l.overdraft || '',                        // 当座貸越
      l.back_finance || '',                     // バックファイナンス
      l.joint_registration || '',               // 連棟登記
      l.debt_service_years_ok || '',            // 債務償還年数〇年ならOK
      l.equity_ratio_ok || '',                  // 自己資本比率〇％ならOK
      l.company_rating || '',                   // 自社の格付
      l.other_question1 || '',                  // その他の質問1
      l.other_question2 || '',                  // その他の質問2
      l.other_question3 || '',                  // その他の質問3
      l.other_question4 || '',                  // その他の質問4
      l.other_question5 || '',                  // その他の質問5
      l.other_question6 || '',                  // その他の質問6
      l.other_question7 || '',                  // その他の質問7
      l.other_question8 || '',                  // その他の質問8
      l.other_question9 || '',                  // その他の質問9
      l.other_question10 || '',                 // その他の質問10
      formatDate(l.created_at),                 // 質問日（登録日）
      l.client_name || '',                      // 会社名（RPC関数の戻り値から直接取得）
    ]);

    // ─── 一括書き込み ───
    sheet
      .getRange(startRow, idCol, ids.length, 1)
      .setValues(ids);

    sheet
      .getRange(startRow, dataStartCol, values.length, numCols)
      .setValues(values);

  } catch (e) {
    // エラーダイアログを表示
    SpreadsheetApp.getUi().alert(
      '融資情報検索エラー',
      '融資情報データの取得に失敗しました。\n\nエラー詳細:\n' + e.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    
    // シートにもエラー表示
    sheet.getRange(startRow, dataStartCol).setValue('❌ エラー: ' + e.message);
  }
}

// 登録モーダルを表示
function createModalLoanInformations() {
  const html = HtmlService
    .createHtmlOutputFromFile('CreateModalLoanInformation')
    .setWidth(980)   // 横幅
    .setHeight(1020); // 高さ

  SpreadsheetApp
    .getUi()
    .showModalDialog(html, '融資情報登録モーダル');
}

/**
 * HTML から呼び出される登録関数
 * @param {Object} formData 登録フォームのデータ
 */
function registerLoanInformation(formData) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'アクセストークンが未設定' };
  }
  
  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // ペイロード作成
  const payload = Object.assign({}, formData);
  
  // 管理者の場合はフォームから送られたclient_idを使用、クライアントの場合は自動設定
  if (clientId && clientId.trim() !== '') {
    // クライアントユーザーの場合：自動的にclient_idを設定
    payload.client_id = clientId;
  } else {
    // 管理者の場合：フォームからclient_idが送られていることを期待
    if (!formData.client_id) {
      return { success: false, message: '管理者権限での登録にはクライアントIDの指定が必要です' };
    }
  }
  
  // デバッグ用：ペイロードの内容をログ出力
  console.log('registerLoanInformation - formData:', JSON.stringify(formData));
  console.log('registerLoanInformation - payload:', JSON.stringify(payload));

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/loan_informations`,
    {
      method: 'post',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: 'return=representation'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const result = JSON.parse(res.getContentText());
  
  if (code === 201 && Array.isArray(result) && result.length > 0) {
    // 検索結果を更新
    searchLoanInformations();
    return { success: true, id: result[0].id };
  }
  
  return { success: false, message: result.message || JSON.stringify(result) };
}

// 更新モーダルを表示
function updateModalLoanInformations() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const activeRange = sheet.getActiveRange();
  const selectedRow = activeRange.getRow();
  
  // A列からIDを取得
  const loanInformationId = sheet.getRange(selectedRow, 1).getValue();
  
  if (!loanInformationId) {
    SpreadsheetApp.getUi().alert(
      '選択エラー',
      '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return;
  }
  
  try {
    // Supabaseからデータを取得
    const loanInformationData = fetchLoanInformationById(loanInformationId);
    
    if (!loanInformationData) {
      SpreadsheetApp.getUi().alert(
        '融資情報が見つかりません',
        '指定されたIDの融資情報が見つかりません。\n\n融資情報ID: ' + loanInformationId,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }
    
    // 取得したデータを一時保存
    const docProps = PropertiesService.getDocumentProperties();
    docProps.setProperty('TEMP_LOAN_INFORMATION_DATA', JSON.stringify(loanInformationData));
    
    const html = HtmlService
      .createHtmlOutputFromFile('UpdateModalLoanInformation')
      .setWidth(980)   // 横幅
      .setHeight(1020); // 高さ

    SpreadsheetApp
      .getUi()
      .showModalDialog(html, '融資情報編集モーダル');
      
  } catch (error) {
    SpreadsheetApp.getUi().alert(
      '融資情報データ取得エラー',
      '融資情報データの取得に失敗しました。\n\nエラー詳細:\n' + error.message + '\n\n融資情報ID: ' + loanInformationId,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * IDで特定の融資情報データを取得
 * @param {string} loanInformationId 融資情報ID
 * @return {Object} 融資情報データ
 */
function fetchLoanInformationById(loanInformationId) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
  }

  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // PostgreSQL RPC関数を使用して融資情報を取得
  const payload = {
    p_loan_information_id: loanInformationId,
    filter_client_id: (clientId && clientId.trim() !== '') ? clientId : null
  };

  const url = `${supabaseUrl}/rest/v1/rpc/get_loan_information_by_id`;
  const res = UrlFetchApp.fetch(url, {
    method: 'POST',
    contentType: 'application/json',
    headers: {
      apikey: apiKey,
      Authorization: 'Bearer ' + accessToken
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  });

  if (res.getResponseCode() !== 200) {
    const err = JSON.parse(res.getContentText());
    throw new Error('融資情報データ取得エラー: ' + (err.message || JSON.stringify(err)));
  }
  
  const result = JSON.parse(res.getContentText());
  return result.length > 0 ? result[0] : null;
}

/**
 * HTML側から呼び出される関数：一時保存された融資情報データを取得
 * @return {Object} 融資情報データ
 */
function getTempLoanInformationData() {
  const docProps = PropertiesService.getDocumentProperties();
  const dataStr = docProps.getProperty('TEMP_LOAN_INFORMATION_DATA');
  
  if (!dataStr) {
    return null;
  }
  
  // 一時データを削除
  docProps.deleteProperty('TEMP_LOAN_INFORMATION_DATA');
  
  return JSON.parse(dataStr);
}

/**
 * 全てのクライアントを取得（セレクトボックス用）
 * @return {Object[]} クライアント配列
 */
function fetchAllClients() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
      throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // clientsテーブルから全てのクライアントを取得
    const url = `${supabaseUrl}/rest/v1/clients?select=id,name&order=name.asc`;
    const res = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('クライアント取得エラー: ' + (err.message || JSON.stringify(err)));
    }
    
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    throw error;
  }
}

/**
 * HTML から呼び出される更新関数
 * @param {Object} formData 更新フォームのデータ
 */
function updateLoanInformation(formData) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'アクセストークンが未設定' };
  }
  
  if (!formData.id) {
    return { success: false, message: '融資情報IDが指定されていません' };
  }
  
  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // ペイロード作成（idは除外）
  const payload = Object.assign({}, formData);
  delete payload.id; // idは更新対象から除外
  payload.updated_at = new Date().toISOString(); // 現在時刻を更新時刻として追加

  // 管理者の場合はclient_idでフィルタリングしない
  let filter = `id=eq.${formData.id}`;
  if (clientId && clientId.trim() !== '') {
    filter = `client_id=eq.${clientId}&${filter}`;
    payload.client_id = clientId; // クライアントユーザーの場合はclient_idを確実に設定
  }

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/loan_informations?${filter}`,
    {
      method: 'PATCH',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: 'return=representation'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const result = JSON.parse(res.getContentText());
  
  if (code === 200 && Array.isArray(result) && result.length > 0) {
    // 検索結果を更新
    searchLoanInformations();
    return { success: true, id: result[0].id };
  }

  return { success: false, message: result.message || JSON.stringify(result) };
}