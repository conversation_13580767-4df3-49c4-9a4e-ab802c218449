<!DOCTYPE html>
<html lang="ja">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">
        .ritz .waffle a {
            color: inherit;
        }

        .ritz .waffle .s4 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: center;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s6 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: left;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s13 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: right;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s14 {
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s5 {
            border-bottom: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s2 {
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s3 {
            border-bottom: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: center;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s8 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: center;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s0 {
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s11 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: right;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s9 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s12 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: right;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s1 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: center;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s15 {
            background-color: #ffffff;
            text-align: right;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s7 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s10 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .waffle tbody th {
            visibility: hidden;
            width: 1px;
            display: block;
        }
    </style>

    <style type="text/css">
        body .ritz .waffle td:has(input, select, textarea) {
            padding: 0 !important;
        }

        body .ritz .waffle input,
        body .ritz .waffle select,
        body .ritz .waffle textarea {
            min-height: 26px;
            display: flex;
            line-height: 1.5;
            box-sizing: border-box;
            padding: 0 8px;
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.15);
            background-color: #fff !important;
        }

        thead {
            display: none;
        }

        /* 見出し系のセルの背景色を変更（s6を除外） */
        body .ritz .waffle .s1,
        body .ritz .waffle .s3,
        body .ritz .waffle .s7,
        body .ritz .waffle .s9,
        body .ritz .waffle .s10 {
            background-color: #efefef;
        }

        /* s6クラスの見出し項目セルのみをグレー背景にする */
        .ritz .waffle .s6.header-cell {
            background-color: #efefef !important;
        }

        /* 新しいスタイル - 青色背景を削除したバージョン */
        .ritz .waffle .s7 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: left;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s9 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s10 {
            border-bottom: 1px SOLID #000000;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s12 {
            border-bottom: 1px SOLID #000000;
            text-align: left;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s13 {
            border-bottom: 1px SOLID #000000;
            text-align: center;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s17 {
            border-bottom: 1px SOLID #000000;
            text-align: center;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s20 {
            border-bottom: 1px SOLID #000000;
            text-align: left;
            color: #ff0000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s22 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            text-align: center;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        /* 緑色背景のスタイル（文字表示のみ）*/
        .ritz .waffle .s14 {
            border-bottom: 1px SOLID #000000;
            background-color: #d9ead3;
            text-align: left;
            color: #ff0000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s16 {
            border-bottom: 1px SOLID #000000;
            background-color: #d9ead3;
            text-align: center;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s19 {
            border-bottom: 1px SOLID #000000;
            background-color: #d9ead3;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .ritz .waffle .s21 {
            border-bottom: 1px SOLID #000000;
            border-right: 1px SOLID #000000;
            background-color: #d9ead3;
            text-align: left;
            color: #000000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        /* 短期物件用のスタイル */
        #propertyTable1 {
            .ritz .waffle a {
                color: inherit
            }

            .ritz .waffle .s9 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s14 {
                border-bottom: 1px SOLID #000;
                background-color: #d9ead3;
                text-align: left;
                color: red;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s1 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #fff;
                text-align: center;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s12 {
                border-bottom: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: left;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s21 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #d9ead3;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s18 {
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s3 {
                border-bottom: 1px SOLID #000;
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s22 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: center;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s8 {
                border-right: 1px SOLID #000;
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s16 {
                border-bottom: 1px SOLID #000;
                background-color: #d9ead3;
                text-align: center;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s2 {
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s4 {
                border-bottom: 1px SOLID #000;
                background-color: #fff;
                text-align: center;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s17 {
                border-bottom: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: center;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s7 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: left;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s0 {
                border-right: 1px SOLID #000;
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s5 {
                background-color: #fff;
                text-align: center;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s19 {
                border-bottom: 1px SOLID #000;
                background-color: #d9ead3;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s13 {
                border-bottom: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: center;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s10 {
                border-bottom: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s20 {
                border-bottom: 1px SOLID #000;
                background-color: #cfe2f3;
                text-align: left;
                color: red;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s15 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: "docs-Aptos Narrow", Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s11 {
                border-bottom: 1px SOLID #000;
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }

            .ritz .waffle .s6 {
                border-bottom: 1px SOLID #000;
                border-right: 1px SOLID #000;
                background-color: #fff;
                text-align: left;
                color: #000;
                font-family: Arial;
                font-size: 11pt;
                vertical-align: bottom;
                white-space: nowrap;
                direction: ltr;
                padding: 2px 3px 2px 3px
            }
        }

        /* 追加スタイル */
        .ritz .waffle .s18 {
            background-color: #ffffff;
            text-align: left;
            color: #000000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        /* 長期物件用のスタイル */
        #propertyTable2 .ritz .waffle .s7 {
            border-bottom: 1px SOLID #000;
            border-right: 1px SOLID #000;
            text-align: left;
            color: #000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #propertyTable2 .ritz .waffle .s10 {
            border-bottom: 1px SOLID #000;
            text-align: left;
            color: #000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #propertyTable2 .ritz .waffle .s14 {
            border-bottom: 1px SOLID #000;
            text-align: left;
            color: #000;
            font-family: Arial;
            font-size: 10pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #propertyTable2 .ritz .waffle .s17 {
            border-bottom: 1px SOLID #000;
            text-align: left;
            color: #000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #propertyTable2 .ritz .waffle .s11 {
            border-bottom: 1px SOLID #000;
            background-color: #d9ead3;
            text-align: left;
            color: #ff0000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #propertyTable2 .ritz .waffle .s13 {
            border-bottom: 1px SOLID #000;
            background-color: #d9ead3;
            text-align: center;
            color: #000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #propertyTable2 .ritz .waffle .s6 {
            border-bottom: 1px SOLID #000;
            border-right: 1px SOLID #000;
            background-color: #fff;
            text-align: left;
            color: #000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        /* 相談銀行情報用のスタイル */
        #bankMemosTable .ritz .waffle .s4 {
            border-bottom: 1px SOLID #000;
            border-right: 1px SOLID #000;
            text-align: center;
            color: #000;
            font-family: Arial;
            font-size: 10pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #bankMemosTable .ritz .waffle .s5 {
            border-bottom: 1px SOLID #000;
            border-right: 1px SOLID #000;
            text-align: left;
            color: #000;
            font-family: Arial;
            font-size: 10pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #bankMemosTable .ritz .waffle .s3 {
            border-bottom: 1px SOLID #000;
            border-right: 1px SOLID #000;
            background-color: #fff;
            text-align: center;
            color: #000;
            font-family: Arial;
            font-size: 10pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #bankMemosTable .ritz .waffle .s2 {
            border-bottom: 1px SOLID #000;
            border-right: 1px SOLID #000;
            background-color: #fff;
            text-align: center;
            color: #000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #bankMemosTable .ritz .waffle .s1 {
            border-bottom: 1px SOLID #000;
            background-color: #fff;
            text-align: center;
            color: #000;
            font-family: Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        #bankMemosTable .ritz .waffle .s0 {
            border-right: 1px SOLID #000;
            background-color: #fff;
            text-align: left;
            color: #000;
            font-family: "docs-Aptos Narrow", Arial;
            font-size: 11pt;
            vertical-align: bottom;
            white-space: nowrap;
            direction: ltr;
            padding: 2px 3px 2px 3px;
        }

        .tab-container {
            margin: 20px 0;
        }

        .bank-memos {
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .delete-row-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 0;
            border-radius: 3px;
            cursor: pointer;
            font-size: 15px;
            width: 100%;
            max-width: 50px;
            height: 20px;
        }

        .delete-row-btn:hover {
            background-color: #c82333;
        }

        .tab-buttons {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-button:hover {
            background-color: #f5f5f5;
        }

        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tab-radio {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .tab-label {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            transition: all 0.3s;
        }

        .tab-label:hover {
            background-color: #e9ecef;
            border-color: #007bff;
        }

        .tab-radio:checked+.tab-label {
            color: #007bff;
            border-color: #007bff;
            background-color: #e7f3ff;
            font-weight: bold;
        }

        .tab-content {
            margin-top: 20px;
        }

        /* ローディング表示用のスタイル */
        #formMessage {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }

        #formMessage::before {
            content: '';
            width: 16px;
            height: 16px;
            margin-bottom: 8px;
            border: 2px solid #CCC;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #formMessage::after {
            content: '';
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.5);
            z-index: -1;
        }

        #formMessage:empty {
            display: none !important;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* ボタンの統一スタイル */
        input[type="submit"],
        button[type="submit"],
        button {
            background-color: #0A4484;
            color: #FFFFFF;
            font-size: 14px;
            font-weight: normal;
            border-radius: 3px;
            border: none;
            padding: 8px 20px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        /* ホバー時のスタイル */
        input[type="submit"]:hover,
        button[type="submit"]:hover,
        button:hover {
            background-color: #083a6f;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* クリック時のスタイル */
        input[type="submit"]:active,
        button[type="submit"]:active,
        button:active {
            background-color: #062a52;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
            transform: translateY(0);
        }
        
        /* datalist用のスタイル調整 */
        input[list] {
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="ritz grid-container" dir="ltr">
        <form id="propertyForm" onsubmit="submitForm(); return false;">
            <div class="tab-container">
                <div class="tab-buttons">
                    <input type="radio" id="tab1" name="tab" class="tab-radio" checked>
                    <label for="tab1" class="tab-label">短期保有物件</label>

                    <input type="radio" id="tab2" name="tab" class="tab-radio">
                    <label for="tab2" class="tab-label">長期保有物件</label>
                </div>

                <div class="bank-memos">
                    <table id="bankMemosTable" class="waffle" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th class="row-header freezebar-origin-ltr"></th>
                                <th id="486796108C0" style="width:2px;" class="column-headers-background">A</th>
                                <th id="486796108C1" style="width:100px;" class="column-headers-background">B</th>
                                <th id="486796108C2" style="width:83px;" class="column-headers-background">C</th>
                                <th id="486796108C3" style="width:83px;" class="column-headers-background">D</th>
                                <th id="486796108C4" style="width:83px;" class="column-headers-background">E</th>
                                <th id="486796108C5" style="width:83px;" class="column-headers-background">F</th>
                                <th id="486796108C6" style="width:83px;" class="column-headers-background">G</th>
                                <th id="486796108C7" style="width:83px;" class="column-headers-background">H</th>
                                <th id="486796108C8" style="width:207px;" class="column-headers-background">I</th>
                                <th id="486796108C9" style="width:60px;" class="column-headers-background">J</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="height: 20px">
                                <th id="486796108R0" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">1</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s1" dir="ltr" colspan="9">相談銀行情報</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="486796108R1" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">2</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s3" dir="ltr">銀行名</td>
                                <td class="s3" dir="ltr">相談日</td>
                                <td class="s3" dir="ltr">回答日</td>
                                <td class="s3" dir="ltr">回答日数</td>
                                <td class="s3" dir="ltr">回答金額</td>
                                <td class="s3" dir="ltr">回答金利</td>
                                <td class="s3" dir="ltr">回答融資割合</td>
                                <td class="s3" dir="ltr">備考</td>
                                <td class="s3" dir="ltr">行削除</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="bankRow1" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">3</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s4" dir="ltr">
                                    <input type="text" id="bank_name_1" name="bank_name_1" list="consultation_bank_list_1"
                                        style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="銀行名を入力または選択">
                                    <datalist id="consultation_bank_list_1">
                                    </datalist>
                                </td>
                                <td class="s4" dir="ltr">
                                    <input type="date" id="consultation_date_1" name="consultation_date_1"
                                        style="width: 100%; border: none; background: transparent; text-align: center;"
                                        onchange="calculateResponseDays(1)">
                                </td>
                                <td class="s4" dir="ltr">
                                    <input type="date" id="response_date_1" name="response_date_1"
                                        style="width: 100%; border: none; background: transparent; text-align: center;"
                                        onchange="calculateResponseDays(1)">
                                </td>
                                <td class="s4" dir="ltr">
                                    <input type="number" id="response_days_1" name="response_days_1" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s4" dir="ltr">
                                    <input type="number" id="response_amount_1" name="response_amount_1" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s4" dir="ltr">
                                    <input type="number" id="response_interest_rate_1" name="response_interest_rate_1" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s4" dir="ltr">
                                    <input type="number" id="loan_ratio_1" name="loan_ratio_1" min="0" max="100"
                                        step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s5">
                                    <input type="text" id="remarks_1" name="remarks_1"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s3" style="text-align: center;">
                                    <button type="button" class="delete-row-btn"
                                        onclick="deleteBankRow(this)">×</button>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                    <div style="margin-top: 10px;">
                        <button type="button" onclick="addBankRow()" style="padding: 8px 16px;">行追加</button>
                    </div>
                </div>

                <div class="tab-content tab-content-1 active">
                    <table id="propertyTable1" class="waffle" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th class="row-header freezebar-origin-ltr"></th>
                                <th id="1659074254C0" style="width:2px;" class="column-headers-background">A</th>
                                <th id="1659074254C1" style="width:183px;" class="column-headers-background">B</th>
                                <th id="1659074254C2" style="width:100px;" class="column-headers-background">C</th>
                                <th id="1659074254C3" style="width:36px;" class="column-headers-background">D</th>
                                <th id="1659074254C4" style="width:50px;" class="column-headers-background">E</th>
                                <th id="1659074254C5" style="width:32px;" class="column-headers-background">F</th>
                                <th id="1659074254C6" style="width:22px;" class="column-headers-background">G</th>
                                <th id="1659074254C7" style="width:183px;" class="column-headers-background">H</th>
                                <th id="1659074254C8" style="width:100px;" class="column-headers-background">I</th>
                                <th id="1659074254C9" style="width:36px;" class="column-headers-background">J</th>
                                <th id="1659074254C10" style="width:50px;" class="column-headers-background">K</th>
                                <th id="1659074254C11" style="width:36px;" class="column-headers-background">L</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="height: 20px">
                                <th id="1659074254R0" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">1</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s1" dir="ltr" colspan="11">短期物件情報</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R1" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">2</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R2" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">3</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">購入日</td>
                                <td class="s7" dir="ltr" colspan="4"><input type="date" id="purchase_date"
                                        name="purchase_date"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">進捗状況</td>
                                <td class="s9" colspan="4">
                                    <select id="progress_status" name="progress_status"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="取得予定">取得予定</option>
                                        <option value="取得済">取得済</option>
                                        <option value="販売済">販売済</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R3" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">4</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">販売手法</td>
                                <td class="s7" dir="ltr" colspan="4">
                                    <select id="sales_method" name="sales_method"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="現況">現況</option>
                                        <option value="改装有">改装有</option>
                                        <option value="古家付き土地">古家付き土地</option>
                                        <option value="更地">更地</option>
                                        <option value="解体更地">解体更地</option>
                                        <option value="建売">建売</option>
                                        <option value="種地">種地</option>
                                        <option value="賃マンPJ">賃マンPJ</option>
                                        <option value="賃マン建築">賃マン建築</option>
                                        <option value="その他リノベ再販">その他リノベ再販</option>
                                        <option value="新築建売">新築建売</option>
                                        <option value="新築売建">新築売建</option>
                                        <option value="現況渡し">現況渡し</option>
                                    </select>
                                </td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">物件種別</td>
                                <td class="s9" colspan="4">
                                    <select id="property_type" name="property_type"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="マンション区分">マンション区分</option>
                                        <option value="戸建て">戸建て</option>
                                        <option value="土地">土地</option>
                                        <option value="一棟マンション">一棟マンション</option>
                                        <option value="一棟テナントビル">一棟テナントビル</option>
                                        <option value="新築マンション建設">新築マンション建設</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R4" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">5</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">最寄り駅</td>
                                <td class="s10" colspan="3"><input type="text" id="nearest_station"
                                        name="nearest_station"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">駅</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">駅距離（分）</td>
                                <td class="s10" colspan="3"><input type="number" id="station_distance"
                                        name="station_distance" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">分</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R5" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">6</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R6" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">7</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">住所</td>
                                <td class="s7" dir="ltr" colspan="10"><input type="text" id="address" name="address"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R7" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">8</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">地番</td>
                                <td class="s7" dir="ltr" colspan="10"><input type="text" id="lot_number"
                                        name="lot_number"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R8" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">9</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">地図URL</td>
                                <td class="s7" dir="ltr" colspan="10"><input type="url" id="map_url" name="map_url"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R9" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">10</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R10" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">11</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">土地面積（㎡）</td>
                                <td class="s12" dir="ltr" colspan="3"><input type="number" id="land_area_sqm"
                                        name="land_area_sqm" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">㎡</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">建物延べ面積(㎡）</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="number" id="building_area_sqm"
                                        name="building_area_sqm" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">㎡</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R11" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">12</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">土地面積（坪）</td>
                                <td class="s14" dir="ltr" colspan="3"></td>
                                <td class="s6" dir="ltr">坪</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">構造</td>
                                <td class="s13" dir="ltr" colspan="3">
                                    <select id="structure" name="structure" style="width: 100%; border: none; background: transparent; text-align: center;">
                                        <option value="">選択してください</option>
                                        <option value="木造">木造</option>
                                        <option value="鉄骨造">鉄骨造</option>
                                        <option value="鉄筋コンクリート造">鉄筋コンクリート造</option>
                                        <option value="鉄骨鉄筋コンクリート造">鉄骨鉄筋コンクリート造</option>
                                    </select>
                                </td>
                                <td class="s6" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R12" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">13</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">用途地域</td>
                                <td class="s12" dir="ltr" colspan="3">
                                    <select id="land_use_zone" name="land_use_zone" style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="第一種低層住居専用地域">第一種低層住居専用地域</option>
                                        <option value="第二種低層住居専用地域">第二種低層住居専用地域</option>
                                        <option value="第一種中高層住居専用地域">第一種中高層住居専用地域</option>
                                        <option value="第二種中高層住居専用地域">第二種中高層住居専用地域</option>
                                        <option value="第一種住居地域">第一種住居地域</option>
                                        <option value="第二種住居地域">第二種住居地域</option>
                                        <option value="準住居地域">準住居地域</option>
                                        <option value="近隣商業地域">近隣商業地域</option>
                                        <option value="商業地域">商業地域</option>
                                        <option value="準工業地域">準工業地域</option>
                                        <option value="工業地域">工業地域</option>
                                        <option value="工業専用地域">工業専用地域</option>
                                        <option value="田園住居地域">田園住居地域</option>
                                    </select>
                                </td>
                                <td class="s6" dir="ltr"></td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">築年月日</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="date" id="construction_date"
                                        name="construction_date"
                                        style="width: 100%; border: none; background: transparent; text-align: center;"
                                        onchange="calculateBuildingAge()">
                                </td>
                                <td class="s6" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R13" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">14</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">建蔽率</td>
                                <td class="s12" dir="ltr" colspan="3"><input type="number" id="building_coverage"
                                        name="building_coverage" min="0" max="100"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">築年数</td>
                                <td class="s16" dir="ltr" colspan="3">
                                    <input type="number" id="building_age" name="building_age" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;" readonly>
                                </td>
                                <td class="s6" dir="ltr">年</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R14" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">15</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">容積率</td>
                                <td class="s12" dir="ltr" colspan="3"><input type="number" id="floor_area_ratio"
                                        name="floor_area_ratio" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">間取り</td>
                                <td class="s17" dir="ltr" colspan="3"><input type="number" id="floor_plan_number"
                                        name="floor_plan_number"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">
                                    <select id="floor_plan_unit" name="floor_plan_unit" style="width: 100%; border: none; background: transparent;">
                                        <option value="R">R</option>
                                        <option value="LDK">LDK</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R15" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">16</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">道路幅員</td>
                                <td class="s12" dir="ltr" colspan="3"><input type="number" id="road_width"
                                        name="road_width" min="0" step="0.1"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">ｍ</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">階建て</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="number" id="floors" name="floors"
                                        min="1"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">階</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R16" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">17</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">道路後退</td>
                                <td class="s12" dir="ltr" colspan="3"><input type="number" id="road_setback"
                                        name="road_setback" min="0" step="0.1"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">ｍ</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">部屋番号</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="text" id="room_number"
                                        name="room_number"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">号室</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R17" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">18</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">固定資産税評価額（土地）</td>
                                <td class="s12" dir="ltr" colspan="3"><input type="number" id="land_tax_value"
                                        name="land_tax_value" min="0" onchange="calculateFixedAssetTaxTotal()" oninput="calculateFixedAssetTaxTotal()"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">固定資産税評価額（建物）</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="number" id="building_tax_value"
                                        name="building_tax_value" min="0" onchange="calculateFixedAssetTaxTotal()" oninput="calculateFixedAssetTaxTotal()"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R18" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">19</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">固定資産税評価額合計</td>
                                <td class="s14" dir="ltr" colspan="3"><input type="number" id="fixed_asset_tax_total"
                                        name="fixed_asset_tax_total" min="0" readonly
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R18" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">19</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s15"></td>
                                <td class="s15"></td>
                                <td class="s15"></td>
                                <td class="s15"></td>
                                <td class="s15"></td>
                                <td class="s15"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R19" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">20</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell"></td>
                                <td class="s6 header-cell" dir="ltr" colspan="2">金額</td>
                                <td class="s6 header-cell" dir="ltr" colspan="2">予定月</td>
                                <td class="s2"></td>
                                <td class="s5" dir="ltr" colspan="5">【銀行条件】</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R20" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">21</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">購入金額（税込）</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="number" id="purchase_price"
                                        name="purchase_price" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">運転資金</td>
                                <td class="s9" colspan="4">
                                    <select id="working_capital_id" name="working_capital_id" style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R21" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">22</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">うち消費税額</td>
                                <td class="s17" dir="ltr" colspan="3"><input type="number" id="consumption_tax_amount"
                                        name="consumption_tax_amount" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入金額</td>
                                <td class="s10" colspan="3"><input type="number" id="loan_amount" name="loan_amount"
                                        min="0" onchange="calculateLoanFee()" oninput="calculateLoanFee()"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R22" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">23</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">（うち手付金・税込）</td>
                                <td class="s13" dir="ltr"><input type="number" id="earnest_money" name="earnest_money"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="earnest_month" name="earnest_month"
                                        min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入期間</td>
                                <td class="s10" colspan="3"><input type="number" id="loan_period" name="loan_period"
                                        min="1"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">年</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R23" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">24</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">（うち残代金・税込）</td>
                                <td class="s13" dir="ltr"><input type="number" id="remaining_payment"
                                        name="remaining_payment" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="remaining_month"
                                        name="remaining_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入金利</td>
                                <td class="s10" colspan="3"><input type="number" id="loan_rate" name="loan_rate" min="0"
                                        step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R24" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">25</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">仲介手数料（購入時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="brokerage_fee_purchase"
                                        name="brokerage_fee_purchase" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="brokerage_fee_purchase_month"
                                        name="brokerage_fee_purchase_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入手数料率</td>
                                <td class="s10" colspan="3"><input type="number" id="loan_fee_rate" name="loan_fee_rate"
                                        min="0" step="0.01" onchange="calculateLoanFee()" oninput="calculateLoanFee()"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R25" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">26</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">登記費用（購入時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="registration_fee_purchase"
                                        name="registration_fee_purchase" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="registration_fee_purchase_month"
                                        name="registration_fee_purchase_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s18" dir="ltr"></td>
                                <td class="s18" dir="ltr"></td>
                                <td class="s2"></td>
                                <td class="s18" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s18" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R26" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">27</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">不動産取得税（購入時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="acquisition_tax"
                                        name="acquisition_tax" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="acquisition_tax_month"
                                        name="acquisition_tax_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s18" dir="ltr"></td>
                                <td class="s5" dir="ltr" colspan="5">【利回り商品として売却する場合】</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R27" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">28</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">印紙代（購入時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="stamp_duty_purchase"
                                        name="stamp_duty_purchase" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="stamp_duty_purchase_month"
                                        name="stamp_duty_purchase_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">総戸数</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="number" id="total_units"
                                        name="total_units" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">戸</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R28" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">29</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">借入手数料（購入時）</td>
                                <td class="s16" dir="ltr"><input type="number" id="loan_fee_purchase"
                                        name="loan_fee_purchase" min="0" readonly
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="loan_fee_purchase_month"
                                        name="loan_fee_purchase_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">仕入時年間賃料</td>
                                <td class="s10" colspan="3"><input type="number" id="annual_rent_purchase"
                                        name="annual_rent_purchase" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R29" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">30</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">固定資産税</td>
                                <td class="s13" dir="ltr"><input type="number" id="property_tax" name="property_tax"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="property_tax_month"
                                        name="property_tax_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">販売時年間賃料（想定）</td>
                                <td class="s9" colspan="3"><input type="number" id="sale_estimated_annual_rent"
                                        name="sale_estimated_annual_rent" min="0" onchange="calculateSaleEstimatedYield(); calculateBestSaleAmount();" oninput="calculateSaleEstimatedYield(); calculateBestSaleAmount();"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R30" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">31</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">火災保険料</td>
                                <td class="s13" dir="ltr"><input type="number" id="fire_insurance" name="fire_insurance"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="fire_insurance_month"
                                        name="fire_insurance_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s18" dir="ltr"></td>
                                <td class="s2"></td>
                                <td class="s2" dir="ltr" colspan="2"></td>
                                <td class="s18" dir="ltr" colspan="2"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R31" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">32</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">測量費</td>
                                <td class="s13" dir="ltr"><input type="number" id="survey_fee" name="survey_fee" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="survey_fee_month"
                                        name="survey_fee_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s18" dir="ltr"></td>
                                <td class="s5" dir="ltr" colspan="5">【売却金額について】</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R32" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">33</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">解体費</td>
                                <td class="s13" dir="ltr"><input type="number" id="demolition_fee" name="demolition_fee"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="demolition_fee_month"
                                        name="demolition_fee_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell"></td>
                                <td class="s6 header-cell" dir="ltr" colspan="2">金額</td>
                                <td class="s6 header-cell" dir="ltr" colspan="2">予定月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R33" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">34</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">リフォーム費</td>
                                <td class="s13" dir="ltr"><input type="number" id="renovation_fee" name="renovation_fee"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="renovation_fee_month"
                                        name="renovation_fee_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">①売却予定金額</td>
                                <td class="s10"><input type="number" id="planned_sale_amount" name="planned_sale_amount"
                                        min="0" onchange="calculateSaleEstimatedYield()" oninput="calculateSaleEstimatedYield()"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s10"><input type="number" id="planned_sale_month" name="planned_sale_month"
                                        min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R34" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">35</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">業務委託費</td>
                                <td class="s13" dir="ltr"><input type="number" id="outsourcing_fee"
                                        name="outsourcing_fee" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="outsourcing_fee_month"
                                        name="outsourcing_fee_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">❶売却時想定利回り</td>
                                <td class="s14" dir="ltr" colspan="3"><input type="number" id="sale_estimated_yield" name="sale_estimated_yield"
                                    style="width: 100%; border: none; background: transparent; text-align: center;" onchange="calculateBestSaleAmount()" oninput="calculateBestSaleAmount()">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R35" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">36</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">その他土地関連費用</td>
                                <td class="s13" dir="ltr"><input type="number" id="other_land_costs"
                                        name="other_land_costs" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="other_land_costs_month"
                                        name="other_land_costs_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">　（うち借入返済額）</td>
                                <td class="s10"><input type="number" id="loan_repayment" name="loan_repayment" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s10" dir="ltr"><input type="number" id="loan_repayment_month"
                                        name="loan_repayment_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R36" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">37</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">その他建物関連費用</td>
                                <td class="s13" dir="ltr"><input type="number" id="other_building_costs"
                                        name="other_building_costs" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="other_building_costs_month"
                                        name="other_building_costs_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">　（うち手付金・税込）</td>
                                <td class="s10"><input type="number" id="earnest_money_sale" name="earnest_money_sale"
                                        min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="earnest_money_sale_month"
                                        name="earnest_money_sale_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R37" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">38</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">その他諸経費</td>
                                <td class="s13" dir="ltr"><input type="number" id="other_expenses" name="other_expenses"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="other_expenses_month"
                                        name="other_expenses_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">　（うち残代金・税込）</td>
                                <td class="s10"><input type="number" id="remaining_payment_sale"
                                        name="remaining_payment_sale" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s10" dir="ltr"><input type="number" id="remaining_payment_sale_month"
                                        name="remaining_payment_sale_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R38" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">39</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">仲介手数料（売却時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="brokerage_fee_sale"
                                        name="brokerage_fee_sale" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="brokerage_fee_sale_month"
                                        name="brokerage_fee_sale_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">②売却ベスト金額</td>
                                <td class="s19" colspan="3"><input type="number" id="best_sale_amount"
                                        name="best_sale_amount" min="0" readonly
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R39" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">40</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">広告宣伝費（売却時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="advertising_fee_sale"
                                        name="advertising_fee_sale" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="advertising_fee_sale_month"
                                        name="advertising_fee_sale_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">❷売却時ベスト利回り</td>
                                <td class="s19" dir="ltr" colspan="3"><input type="number" id="best_sale_yield"
                                        name="best_sale_yield" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R40" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">41</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">銀行諸経費（売却時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="bank_expenses_sale" name="bank_expenses_sale"
                                        min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="bank_expenses_sale_month"
                                        name="bank_expenses_sale_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">①予定利益額</td>
                                <td class="s19" colspan="3"></td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R41" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">42</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">抵当権抹消費用（売却時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="mortgage_cancellation_fee"
                                        name="mortgage_cancellation_fee" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="mortgage_cancellation_fee_month"
                                        name="mortgage_cancellation_fee_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">　予定利益率</td>
                                <td class="s19" colspan="3"></td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R42" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">43</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">登記費用（売却時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="registration_fee_sale"
                                        name="registration_fee_sale" min="0" onchange="calculateTotalCost()" oninput="calculateTotalCost()"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s12" dir="ltr"><input type="number" id="registration_fee_sale_month"
                                        name="registration_fee_sale_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">②ベスト利益額</td>
                                <td class="s19" colspan="3"></td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R43" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">44</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">合計原価額</td>
                                <td class="s16" dir="ltr" colspan="3"></td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">　ベスト利益率</td>
                                <td class="s21" colspan="3"></td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R44" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">45</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s15"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R45" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">46</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">社員成功報酬（売却時）</td>
                                <td class="s13" dir="ltr"><input type="number" id="employee_bonus" name="employee_bonus"
                                        min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s10"><input type="number" id="employee_bonus_month"
                                        name="employee_bonus_month" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">銀行担保評価額</td>
                                <td class="s10" colspan="3"><input type="number" id="bank_collateral_value"
                                        name="bank_collateral_value" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R46" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">47</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">担当社員名</td>
                                <td class="s22" dir="ltr" colspan="4"><input type="text" id="employee_name"
                                        name="employee_name"
                                        style="width: 100%; border: none; background: transparent; text-align: center;">
                                </td>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">銀行リスク額</td>
                                <td class="s10" colspan="3"><input type="number" id="bank_risk" name="bank_risk" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="1659074254R47" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">48</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s2"></td>
                                <td class="s2"></td>
                                <td class="s2"></td>
                                <td class="s2"></td>
                                <td class="s2"></td>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">決算日</td>
                                <td class="s10" colspan="3"><input type="date" id="fiscal_year_end"
                                        name="fiscal_year_end"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="tab-content tab-content-2">
                    <table id="propertyTable2" class="waffle" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th class="row-header freezebar-origin-ltr"></th>
                                <th id="291524064C0" style="width:2px;" class="column-headers-background">A</th>
                                <th id="291524064C1" style="width:183px;" class="column-headers-background">B</th>
                                <th id="291524064C2" style="width:100px;" class="column-headers-background">C</th>
                                <th id="291524064C3" style="width:36px;" class="column-headers-background">D</th>
                                <th id="291524064C4" style="width:50px;" class="column-headers-background">E</th>
                                <th id="291524064C5" style="width:32px;" class="column-headers-background">F</th>
                                <th id="291524064C6" style="width:22px;" class="column-headers-background">G</th>
                                <th id="291524064C7" style="width:183px;" class="column-headers-background">H</th>
                                <th id="291524064C8" style="width:100px;" class="column-headers-background">I</th>
                                <th id="291524064C9" style="width:36px;" class="column-headers-background">J</th>
                                <th id="291524064C10" style="width:50px;" class="column-headers-background">K</th>
                                <th id="291524064C11" style="width:36px;" class="column-headers-background">L</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="height: 20px">
                                <th id="291524064R0" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">1</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s1" dir="ltr" colspan="11">長期物件情報</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R1" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">2</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R2" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">3</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">購入日</td>
                                <td class="s7" dir="ltr" colspan="4"><input type="date" id="purchase_date_long"
                                        name="purchase_date_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">進捗状況</td>
                                <td class="s7" dir="ltr" colspan="4">
                                    <select id="progress_status_long" name="progress_status_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="取得予定">取得予定</option>
                                        <option value="取得済">取得済</option>
                                        <option value="販売済">販売済</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R3" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">4</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R4" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">5</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">物件名</td>
                                <td class="s7" dir="ltr" colspan="10"><input type="text" id="property_name_long"
                                        name="property_name_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R5" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">6</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">住所</td>
                                <td class="s7" dir="ltr" colspan="10"><input type="text" id="address_long"
                                        name="address_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R6" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">7</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R7" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">8</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">土地面積（㎡）</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="land_area_sqm_long"
                                        name="land_area_sqm_long" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">㎡</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">建物延べ面積(㎡）</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="building_area_sqm_long"
                                        name="building_area_sqm_long" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">㎡</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R9" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">10</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">耐用年数</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="useful_life_long"
                                        name="useful_life_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">年</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">構造</td>
                                <td class="s10" dir="ltr" colspan="3">
                                    <select id="structure_long" name="structure_long" style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="木造">木造</option>
                                        <option value="鉄骨造">鉄骨造</option>
                                        <option value="鉄筋コンクリート造">鉄筋コンクリート造</option>
                                        <option value="鉄骨鉄筋コンクリート造">鉄骨鉄筋コンクリート造</option>
                                    </select>
                                </td>
                                <td class="s6" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R9" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">10</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経済的耐用年数</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="economic_useful_life_long"
                                        name="economic_useful_life_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">年</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">築年月日</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="date" id="construction_date_long"
                                        name="construction_date_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;"
                                        onchange="calculateBuildingAgeLong()">
                                </td>
                                <td class="s6" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R9" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">10</div>
                                </th>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">築年数</td>
                                <td class="s16" dir="ltr" colspan="3">
                                    <input type="number" id="building_age_long" name="building_age_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;" readonly>
                                </td>
                                <td class="s6" dir="ltr">年</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R1" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">2</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s2"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                                <td class="s5"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R11" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">12</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">固定資産税評価額（土地）</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="land_tax_value_long"
                                        name="land_tax_value_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;"
                                        oninput="calculateFixedAssetTaxTotalLong()">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">現状賃貸収入（年間）</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number"
                                        id="current_rental_income_long" name="current_rental_income_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R12" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">13</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell">固定資産税評価額（建物）</td>
                                <td class="s14" colspan="3"><input type="number" id="building_tax_value_long"
                                        name="building_tax_value_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;"
                                        oninput="calculateFixedAssetTaxTotalLong()">
                                </td>
                                <td class="s6">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">満室想定賃貸収入（年間）</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="full_occupancy_income_long"
                                        name="full_occupancy_income_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R13" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">14</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">固定資産税評価額合計</td>
                                <td class="s13" dir="ltr" colspan="3"><input type="number" id="fixed_asset_tax_total_long"
                                        name="fixed_asset_tax_total_long" min="0" readonly
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">利回り</td>
                                <td class="s10" dir="ltr" colspan="3"><input type="number" id="yield_rate_long"
                                        name="yield_rate_long" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R14" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">15</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">修繕履歴</td>
                                <td class="s10" dir="ltr" colspan="3">
                                    <select id="repair_history_long" name="repair_history_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="有">有</option>
                                        <option value="無">無</option>
                                    </select>
                                </td>
                                <td class="s6" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R15" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">16</div>
                                </th>
                                <td class="s2"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s5"></td>
                                <td class="s5" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R16" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">17</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell"></td>
                                <td class="s6 header-cell" dir="ltr" colspan="4">金額</td>
                                <td class="s2"></td>
                                <td class="s5" dir="ltr" colspan="5">【銀行条件】</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R17" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">18</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">購入金額（税込）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="purchase_price_long"
                                        name="purchase_price_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;"
                                        oninput="calculateLoanRatioLong()">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">運転資金</td>
                                <td class="s7" dir="ltr" colspan="4">
                                    <select id="working_capital_id_long" name="working_capital_id_long" style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R18" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">19</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経費（管理費）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="management_fee_long"
                                        name="management_fee_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入金額</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="loan_amount_long"
                                        name="loan_amount_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;"
                                        oninput="calculateLoanRatioLong()">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R19" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">20</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経費（修繕費）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="repair_cost_long"
                                        name="repair_cost_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入期間</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="loan_period_long"
                                        name="loan_period_long" min="1"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">年</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R20" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">21</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経費（固定資産税）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="property_tax_long"
                                        name="property_tax_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入金利</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="loan_rate_long"
                                        name="loan_rate_long" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R21" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">22</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経費（水道光熱費）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="utility_cost_long"
                                        name="utility_cost_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入手数料率</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="loan_fee_rate_long"
                                        name="loan_fee_rate_long" min="0" step="0.01"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R23" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">23</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経費（減価償却費）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="depreciation_long"
                                        name="depreciation_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">借入割合</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="loan_ratio_long"
                                        name="loan_ratio_long" min="0" step="0.01" readonly
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">％</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R23" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">24</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">経費（その他）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="other_expenses_long"
                                        name="other_expenses_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s5" dir="ltr" colspan="5">【売却金額について】</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R24" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">25</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">収支（現状）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="current_balance_long"
                                        name="current_balance_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell"></td>
                                <td class="s6 header-cell" dir="ltr" colspan="2">金額</td>
                                <td class="s6 header-cell" dir="ltr" colspan="2">予定月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R25" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">26</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">収支（満室想定）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="full_occupancy_balance_long"
                                        name="full_occupancy_balance_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">売却予定金額</td>
                                <td class="s17"><input type="number" id="planned_sale_amount_long"
                                        name="planned_sale_amount_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s17"><input type="number" id="planned_sale_month_long"
                                        name="planned_sale_month_long" min="1" max="12"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">月</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R26" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">27</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">修繕費拠出額累計</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="repair_fund_total_long"
                                        name="repair_fund_total_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s2" colspan="3"></td>
                                <td class="s2" dir="ltr"></td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R27" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">28</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">借入残高（現時点）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="current_loan_balance_long"
                                        name="current_loan_balance_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s2" dir="ltr"></td>
                                <td class="s5" dir="ltr" colspan="5">【その他】</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R28" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">29</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">元本返済額（年間）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number"
                                        id="annual_principal_payment_long" name="annual_principal_payment_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">銀行担保評価額</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number"
                                        id="bank_collateral_value_long" name="bank_collateral_value_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R29" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">30</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">支払金利額（年間）</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number"
                                        id="annual_interest_payment_long" name="annual_interest_payment_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">銀行リスク額</td>
                                <td class="s7" dir="ltr" colspan="3"><input type="number" id="bank_risk_long"
                                        name="bank_risk_long" min="0"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr">円</td>
                            </tr>
                            <tr style="height: 20px">
                                <th id="291524064R30" style="height: 20px;" class="row-headers-background">
                                    <div class="row-header-wrapper" style="line-height: 20px">31</div>
                                </th>
                                <td class="s0"></td>
                                <td class="s6 header-cell" dir="ltr">担保状況</td>
                                <td class="s7" dir="ltr" colspan="3">
                                    <select id="collateral_status_long" name="collateral_status_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                        <option value="">選択してください</option>
                                        <option value="根抵当権">根抵当権</option>
                                        <option value="抵当権">抵当権</option>
                                    </select>
                                </td>
                                <td class="s6" dir="ltr"></td>
                                <td class="s0" dir="ltr"></td>
                                <td class="s6 header-cell" dir="ltr">決算日</td>
                                <td class="s17" colspan="3"><input type="date" id="fiscal_year_end_long"
                                        name="fiscal_year_end_long"
                                        style="width: 100%; border: none; background: transparent; text-align: left;">
                                </td>
                                <td class="s6" dir="ltr"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div style="text-align: left; margin-top: 20px;">
                <button type="submit">更新する</button>
                <div id="formMessage" style="margin-top: 10px; font-weight: bold;"></div>
            </div>
        </form>
    </div>

    <script>
        // 銀行データを保持するグローバル変数
        let bankData = [];
        // 現在編集中の物件ID
        let currentPropertyId = null;

        // 築年月日から築年数を計算する関数
        function calculateBuildingAge() {
            const constructionDate = document.getElementById('construction_date').value;
            const buildingAgeInput = document.getElementById('building_age');
            
            if (constructionDate) {
                const today = new Date();
                const construction = new Date(constructionDate);
                
                let years = today.getFullYear() - construction.getFullYear();
                const monthDifference = today.getMonth() - construction.getMonth();
                
                if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < construction.getDate())) {
                    years--;
                }
                
                const calculatedAge = Math.max(0, years);
                buildingAgeInput.value = calculatedAge;
            } else {
                buildingAgeInput.value = '';
            }
        }

        // 長期物件用の固定資産税評価額合計の自動計算 (土地 + 建物)
        function calculateFixedAssetTaxTotalLong() {
            const landTaxValue = getNumericValue('land_tax_value_long') || 0;
            const buildingTaxValue = getNumericValue('building_tax_value_long') || 0;
            
            const total = landTaxValue + buildingTaxValue;
            const totalElement = document.getElementById('fixed_asset_tax_total_long');
            if (totalElement) {
                totalElement.value = total > 0 ? total : '';
            }
        }

        // 長期物件用の借入割合の自動計算 (借入金額 / 購入金額 * 100)
        function calculateLoanRatioLong() {
            const loanAmount = getNumericValue('loan_amount_long') || 0;
            const purchasePrice = getNumericValue('purchase_price_long') || 0;
            
            const loanRatioElement = document.getElementById('loan_ratio_long');
            if (loanRatioElement) {
                if (purchasePrice > 0 && loanAmount > 0) {
                    const ratio = (loanAmount / purchasePrice) * 100;
                    loanRatioElement.value = Math.round(ratio * 100) / 100; // 小数点以下2桁で四捨五入
                } else {
                    loanRatioElement.value = '';
                }
            }
        }

        // 長期物件用の築年月日から築年数を計算する関数
        function calculateBuildingAgeLong() {
            const constructionDate = document.getElementById('construction_date_long').value;
            const buildingAgeInput = document.getElementById('building_age_long');
            
            if (constructionDate) {
                const today = new Date();
                const construction = new Date(constructionDate);
                
                let years = today.getFullYear() - construction.getFullYear();
                const monthDifference = today.getMonth() - construction.getMonth();
                
                if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < construction.getDate())) {
                    years--;
                }
                
                const calculatedAge = Math.max(0, years);
                buildingAgeInput.value = calculatedAge;
            } else {
                buildingAgeInput.value = '';
            }
        }

        // 借入金額 × 借入手数料率から借入手数料（購入時）を計算する関数
        function calculateLoanFee() {
            const loanAmount = parseFloat(document.getElementById('loan_amount').value) || 0;
            const loanFeeRate = parseFloat(document.getElementById('loan_fee_rate').value) || 0;
            const loanFeePurchaseInput = document.getElementById('loan_fee_purchase');
            
            if (loanAmount > 0 && loanFeeRate > 0) {
                const loanFee = Math.round(loanAmount * loanFeeRate / 100);
                loanFeePurchaseInput.value = loanFee;
            } else if (loanAmount === 0 || loanFeeRate === 0) {
                loanFeePurchaseInput.value = '';
            }
            
            // 借入手数料が更新されたら合計も再計算
            calculateTotalCost();
        }

        // 回答日数の自動計算 (回答日 - 相談日)
        function calculateResponseDays(bankNumber) {
            const consultationDateElement = document.getElementById(`consultation_date_${bankNumber}`);
            const responseDateElement = document.getElementById(`response_date_${bankNumber}`);
            const responseDaysElement = document.getElementById(`response_days_${bankNumber}`);
            
            if (consultationDateElement && responseDateElement && responseDaysElement) {
                const consultationDate = consultationDateElement.value;
                const responseDate = responseDateElement.value;
                
                if (consultationDate && responseDate) {
                    const consultationDateObj = new Date(consultationDate);
                    const responseDateObj = new Date(responseDate);
                    
                    // 日数の差を計算
                    const timeDiff = responseDateObj.getTime() - consultationDateObj.getTime();
                    const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
                    
                    // 負の値の場合は0にする
                    responseDaysElement.value = Math.max(0, dayDiff);
                } else {
                    responseDaysElement.value = '';
                }
            }
        }

        // 合計原価額を計算する関数
        function calculateTotalCost() {
            const fieldIds = [
                'purchase_price',
                'consumption_tax_amount',
                'earnest_money',
                'balance_payment_tax_incl', 
                'brokerage_fee_purchase',
                'registration_fee_purchase',
                'real_estate_tax_purchase',
                'stamp_duty_purchase',
                'loan_fee_purchase',
                'property_tax',
                'fire_insurance',
                'survey_fee',
                'demolition_fee',
                'renovation_fee',
                'outsourcing_fee',
                'other_land_costs',
                'other_building_costs',
                'other_expenses',
                'brokerage_fee_sale',
                'advertising_fee_sale',
                'bank_expenses_sale',
                'mortgage_cancellation_fee',
                'registration_fee_sale'
            ];
            
            let total = 0;
            fieldIds.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    const value = parseFloat(element.value) || 0;
                    total += value;
                }
            });
            
            const totalCostInput = document.getElementById('total_cost');
            if (totalCostInput) {
                totalCostInput.value = total;
            }
        }
        
        // ページロード時の初期化
        document.addEventListener('DOMContentLoaded', function () {
            const unitTexts = ['円', '㎡', '坪', '％', '年', '月', '階', 'ｍ', '号室', '分', '駅', '戸', ''];

            // propertyTable1に適用
            const table1 = document.getElementById('propertyTable1');
            if (table1) {
                const s6Cells = table1.querySelectorAll('td.s6');
                s6Cells.forEach(cell => {
                    const text = cell.textContent.trim();
                    // 単位用セル（短いテキスト）以外のセルに header-cell クラスを追加
                    if (!unitTexts.includes(text) && text.length > 3) {
                        cell.classList.add('header-cell');
                    }
                });
            }

            // propertyTable2にも適用
            const table2 = document.getElementById('propertyTable2');
            if (table2) {
                const s6Cells = table2.querySelectorAll('td.s6');
                s6Cells.forEach(cell => {
                    const text = cell.textContent.trim();
                    // 単位用セル（短いテキスト）以外のセルに header-cell クラスを追加
                    if (!unitTexts.includes(text) && text.length > 3) {
                        cell.classList.add('header-cell');
                    }
                });
            }

            // ラジオボタンによるタブ切り替え機能
            const tab1Radio = document.getElementById('tab1');
            const tab2Radio = document.getElementById('tab2');
            const tabContent1 = document.querySelector('.tab-content-1');
            const tabContent2 = document.querySelector('.tab-content-2');

            function switchTab() {
                if (tab1Radio.checked) {
                    tabContent1.classList.add('active');
                    tabContent2.classList.remove('active');
                } else if (tab2Radio.checked) {
                    tabContent1.classList.remove('active');
                    tabContent2.classList.add('active');
                }
            }

            // ラジオボタンの変更を監視
            tab1Radio.addEventListener('change', switchTab);
            tab2Radio.addEventListener('change', switchTab);
            
            // 銀行一覧を取得してdatalistに設定し、物件データを初期化
            initializeForm();
            
            // 旧銀行入力イベントは不要のため削除
            
            // 初期の相談銀行フィールドにイベントリスナーを追加
            addConsultationBankEventListener(1);
        });

        // 相談銀行のdatalistを更新する関数
        function populateConsultationBankDatalist() {
            for (let i = 1; i <= bankRowCounter; i++) {
                populateConsultationBankDatalistForRow(i);
            }
        }

        // 特定の行の相談銀行datalistを更新する関数
        function populateConsultationBankDatalistForRow(rowNumber) {
            const datalistElement = document.getElementById(`consultation_bank_list_${rowNumber}`);
            if (datalistElement && bankData) {
                datalistElement.innerHTML = '';
                bankData.forEach(function(bank) {
                    const option = document.createElement('option');
                    option.value = bank.name;
                    option.setAttribute('data-code', bank.code);
                    datalistElement.appendChild(option);
                });
            }
        }

        // 相談銀行フィールドにイベントリスナーを追加する関数
        function addConsultationBankEventListener(rowNumber) {
            const inputElement = document.getElementById(`bank_name_${rowNumber}`);
            if (inputElement) {
                inputElement.addEventListener('input', function() {
                    const inputValue = this.value;
                    const selectedBank = bankData.find(bank => bank.name === inputValue);
                    
                    if (selectedBank) {
                        // 選択された銀行のコードをdata属性に保存
                        this.setAttribute('data-selected-code', selectedBank.code);
                    } else {
                        // マッチしない場合はdata属性をクリア
                        this.removeAttribute('data-selected-code');
                    }
                });
            }
        }

        // フォーム初期化関数
        function initializeForm() {
            const formMessage = document.getElementById('formMessage');
            formMessage.textContent = 'データ読み込み中...';
            
            // 運転資金一覧・銀行一覧と物件データを並行して取得
            Promise.all([
                new Promise((resolve, reject) => {
                    google.script.run
                        .withSuccessHandler(resolve)
                        .withFailureHandler(reject)
                        .fetchMyWorkingCapital(null, null);
                }),
                new Promise((resolve, reject) => {
                    google.script.run
                        .withSuccessHandler(resolve)
                        .withFailureHandler(reject)
                        .getBanksFromAPI();
                }),
                new Promise((resolve, reject) => {
                    google.script.run
                        .withSuccessHandler(resolve)
                        .withFailureHandler(reject)
                        .getTempPropertyData();
                })
            ]).then(([workingCapitals, banks, propertyId]) => {
                // 運転資金セレクトの構築
                const shortSelect = document.getElementById('working_capital_id');
                const longSelect = document.getElementById('working_capital_id_long');
                function fillWc(selectEl) {
                    if (!selectEl) return;
                    while (selectEl.options.length > 1) selectEl.remove(1);
                    (workingCapitals || []).forEach(function(wc) {
                        const label = [wc.bank_name, wc.client_name].filter(Boolean).join(' / ') || (wc.id + '');
                        const opt = document.createElement('option');
                        opt.value = wc.id;
                        opt.textContent = label;
                        selectEl.appendChild(opt);
                    });
                }
                fillWc(shortSelect);
                fillWc(longSelect);
                // 相談銀行のdatalist更新用に銀行一覧を反映
                bankData = banks || [];
                populateConsultationBankDatalist();
                
                // 物件データを取得
                if (propertyId) {
                    currentPropertyId = propertyId;
                    formMessage.textContent = '物件データを読み込み中...';
                    
                    google.script.run
                        .withSuccessHandler(function(propertyData) {
                            console.log('取得した物件データ:', propertyData);
                            
                            if (propertyData) {
                                // エラー情報が含まれているかチェック
                                if (propertyData.error) {
                                    formMessage.className = 'error';
                                    formMessage.textContent = '物件データエラー (物件ID: ' + propertyId + '): ' + propertyData.error;
                                } else {
                                    populateForm(propertyData);
                                }
                            } else {
                                formMessage.className = 'error';
                                formMessage.textContent = '物件データが見つかりません (物件ID: ' + propertyId + ')';
                            }
                        })
                        .withFailureHandler(function(error) {
                            formMessage.className = 'error';
                            formMessage.textContent = '物件データの読み込みエラー (物件ID: ' + propertyId + '): ' + error.toString();
                        })
                        .getPropertyByIdForUpdate(propertyId);
                } else {
                    formMessage.className = 'error';
                    formMessage.textContent = '物件IDが取得できません。物件の行を選択してから更新ボタンを押してください。';
                }
            }).catch(error => {
                formMessage.className = 'error';
                formMessage.textContent = 'データ読み込みエラー: ' + error.toString();
            });
        }



        // フォームにデータを設定
        function populateForm(data) {
            const formMessage = document.getElementById('formMessage');
            
            try {
                console.log('populateForm - 受信データ:', JSON.stringify(data, null, 2));
                
                // 新しいデータ構造に対応
                const propertyData = data.property || data;
                const shortPropertyData = data.short_properties;
                const longPropertyData = data.long_properties;
                const consultationBanks = data.consultation_banks || [];
                
                console.log('基本物件データ:', propertyData);
                console.log('短期物件データ:', shortPropertyData);
                console.log('長期物件データ:', longPropertyData);
                console.log('相談銀行データ:', consultationBanks);
                
                // タブの設定（種別に応じて）
                if (propertyData.type === 1) {
                    document.getElementById('tab1').checked = true;
                    document.querySelector('.tab-content-1').classList.add('active');
                    document.querySelector('.tab-content-2').classList.remove('active');
                } else if (propertyData.type === 2) {
                    document.getElementById('tab2').checked = true;
                    document.querySelector('.tab-content-1').classList.remove('active');
                    document.querySelector('.tab-content-2').classList.add('active');
                }

                // 基本物件情報の設定
                setValue('purchase_date', propertyData.purchase_date);
                setValue('address', propertyData.address);
                setValue('land_area_sqm', propertyData.land_area_sqm);
                setValue('building_area_sqm', propertyData.building_area_sqm);
                setValue('structure', propertyData.structure);
                setValue('construction_date', propertyData.construction_date);
                setValue('progress_status', propertyData.progress_status);
                setValue('purchase_price', propertyData.purchase_price);
                setValue('loan_amount', propertyData.loan_amount);
                setValue('loan_period', propertyData.loan_period);
                setValue('loan_rate', propertyData.loan_interest_rate);
                setValue('loan_fee_rate', propertyData.loan_fee_rate);
                setValue('planned_sale_amount', propertyData.planned_sale_amount);
                setValue('planned_sale_month', propertyData.planned_sale_month);
                setValue('bank_collateral_value', propertyData.bank_collateral_value);
                setValue('bank_risk', propertyData.bank_risk_amount);
                setValue('closing_date', propertyData.closing_date);
                setValue('land_tax_value', propertyData.fixed_asset_tax_land);
                setValue('building_tax_value', propertyData.fixed_asset_tax_building);
                // 新スキーマ: working_capital_id をセレクトに反映
                if (propertyData.working_capital_id) {
                    const wcSelect = document.getElementById('working_capital_id');
                    if (wcSelect) wcSelect.value = propertyData.working_capital_id;
                }

                // 短期物件フィールド（基本物件データから取得される場合もある）
                setValue('land_use_zone', shortPropertyData?.land_use_zone || propertyData.land_use_zone);
                setValue('building_coverage', shortPropertyData?.building_coverage_ratio || propertyData.building_coverage_ratio);
                setValue('floor_area_ratio', shortPropertyData?.floor_area_ratio || propertyData.floor_area_ratio);
                setValue('road_width', shortPropertyData?.road_width || propertyData.road_width);
                setValue('road_setback', shortPropertyData?.road_setback || propertyData.road_setback);
                setFloorPlan(shortPropertyData?.floor_plan || propertyData.floor_plan);
                setValue('floors', shortPropertyData?.building_floors || propertyData.building_floors);
                setValue('room_number', shortPropertyData?.room_number || propertyData.room_number);
                // 短期物件の費用・経費情報（shortPropertyDataから取得）
                if (shortPropertyData) {
                    setValue('consumption_tax_amount', shortPropertyData.consumption_tax_amount);
                    setValue('earnest_money', shortPropertyData.down_payment_tax_incl);
                    setValue('earnest_month', shortPropertyData.down_payment_tax_incl_month);
                    setValue('remaining_payment', shortPropertyData.balance_payment_tax_incl);
                    setValue('remaining_month', shortPropertyData.balance_payment_tax_incl_month);
                    setValue('brokerage_fee_purchase', shortPropertyData.brokerage_fee_purchase);
                    setValue('brokerage_fee_purchase_month', shortPropertyData.brokerage_fee_purchase_month);
                    setValue('registration_fee_purchase', shortPropertyData.registration_fee_purchase);
                    setValue('registration_fee_purchase_month', shortPropertyData.registration_fee_purchase_month);
                    setValue('acquisition_tax', shortPropertyData.real_estate_tax_purchase);
                    setValue('acquisition_tax_month', shortPropertyData.real_estate_tax_purchase_month);
                    setValue('stamp_duty_purchase', shortPropertyData.stamp_duty_purchase);
                    setValue('stamp_duty_purchase_month', shortPropertyData.stamp_duty_purchase_month);
                    setValue('loan_fee_purchase', shortPropertyData.loan_fee_purchase);
                    setValue('loan_fee_purchase_month', shortPropertyData.loan_fee_purchase_month);
                    setValue('property_tax', shortPropertyData.property_tax);
                    setValue('property_tax_month', shortPropertyData.property_tax_month);
                    setValue('fire_insurance', shortPropertyData.fire_insurance);
                    setValue('fire_insurance_month', shortPropertyData.fire_insurance_month);
                    setValue('survey_fee', shortPropertyData.survey_cost);
                    setValue('survey_fee_month', shortPropertyData.survey_cost_month);
                    setValue('demolition_fee', shortPropertyData.demolition_cost);
                    setValue('demolition_fee_month', shortPropertyData.demolition_cost_month);
                    setValue('renovation_fee', shortPropertyData.renovation_cost);
                    setValue('renovation_fee_month', shortPropertyData.renovation_cost_month);
                    setValue('outsourcing_fee', shortPropertyData.management_fee);
                    setValue('outsourcing_fee_month', shortPropertyData.management_fee_month);
                    setValue('other_land_costs', shortPropertyData.other_land_expenses);
                    setValue('other_land_costs_month', shortPropertyData.other_land_expenses_month);
                    setValue('other_building_costs', shortPropertyData.other_building_expenses);
                    setValue('other_building_costs_month', shortPropertyData.other_building_expenses_month);
                    setValue('other_expenses', shortPropertyData.other_expenses);
                    setValue('other_expenses_month', shortPropertyData.other_expenses_month);
                    setValue('brokerage_fee_sale', shortPropertyData.brokerage_fee_sale);
                    setValue('brokerage_fee_sale_month', shortPropertyData.brokerage_fee_sale_month);
                    setValue('advertising_fee_sale', shortPropertyData.advertising_fee_sale);
                    setValue('advertising_fee_sale_month', shortPropertyData.advertising_fee_sale_month);
                    setValue('bank_expenses_sale', shortPropertyData.bank_expenses_sale);
                    setValue('bank_expenses_sale_month', shortPropertyData.bank_expenses_sale_month);
                    setValue('mortgage_cancellation_fee', shortPropertyData.mortgage_cancellation_fee);
                    setValue('mortgage_cancellation_fee_month', shortPropertyData.mortgage_cancellation_fee_month);
                    setValue('registration_fee_sale', shortPropertyData.registration_fee_sale);
                    setValue('registration_fee_sale_month', shortPropertyData.registration_fee_sale_month);
                    setValue('employee_bonus', shortPropertyData.employee_commission_sale);
                    setValue('employee_name', shortPropertyData.responsible_employee);
                    setValue('total_units', shortPropertyData.total_units);
                    setValue('annual_rent_purchase', shortPropertyData.purchase_annual_rent);
                    setValue('sale_estimated_annual_rent', shortPropertyData.sale_estimated_annual_rent);
                    setValue('loan_repayment', shortPropertyData.loan_repayment_amount);
                    setValue('loan_repayment_month', shortPropertyData.loan_repayment_month);
                    setValue('earnest_money_sale', shortPropertyData.down_payment_sale_tax_incl);
                    setValue('earnest_money_sale_month', shortPropertyData.down_payment_sale_tax_incl_month);
                    setValue('remaining_payment_sale', shortPropertyData.balance_payment_sale_tax_incl);
                    setValue('remaining_payment_sale_month', shortPropertyData.balance_payment_sale_tax_incl_month);
                    setValue('best_sale_amount', shortPropertyData.best_sale_amount);
                    setValue('best_sale_yield', shortPropertyData.best_sale_yield);
                }
                
                // 決算日は基本物件データから
                setValue('fiscal_year_end', propertyData.closing_date);

                // 短期物件情報の設定
                if (shortPropertyData) {
                    setValue('sales_method', shortPropertyData.sales_method);
                    setValue('progress_status', shortPropertyData.progress_status);
                    setValue('property_type', shortPropertyData.property_type);
                    setValue('nearest_station', shortPropertyData.nearest_station);
                    setValue('station_distance', shortPropertyData.station_distance_min);
                    setValue('lot_number', shortPropertyData.lot_number);
                    setValue('map_url', shortPropertyData.map_url);
                    setValue('land_use_zone', shortPropertyData.land_use_zone);
                    setValue('building_coverage_ratio', shortPropertyData.building_coverage_ratio);
                    setValue('floor_area_ratio', shortPropertyData.floor_area_ratio);
                    setValue('road_width', shortPropertyData.road_width);
                    setValue('road_setback', shortPropertyData.road_setback);
                    setFloorPlan(shortPropertyData.floor_plan);
                    setValue('building_floors', shortPropertyData.building_floors);
                    setValue('room_number', shortPropertyData.room_number);
                    setValue('consumption_tax_amount', shortPropertyData.consumption_tax_amount);
                    setValue('down_payment_tax_incl', shortPropertyData.down_payment_tax_incl);
                    setValue('down_payment_tax_incl_month', shortPropertyData.down_payment_tax_incl_month);
                    setValue('balance_payment_tax_incl', shortPropertyData.balance_payment_tax_incl);
                    setValue('balance_payment_tax_incl_month', shortPropertyData.balance_payment_tax_incl_month);
                    
                    // 追加の短期物件フィールド
                    setValue('brokerage_fee_purchase', shortPropertyData.brokerage_fee_purchase);
                    setValue('brokerage_fee_purchase_month', shortPropertyData.brokerage_fee_purchase_month);
                    setValue('registration_fee_purchase', shortPropertyData.registration_fee_purchase);
                    setValue('registration_fee_purchase_month', shortPropertyData.registration_fee_purchase_month);
                    setValue('real_estate_tax_purchase', shortPropertyData.real_estate_tax_purchase);
                    setValue('real_estate_tax_purchase_month', shortPropertyData.real_estate_tax_purchase_month);
                    setValue('stamp_duty_purchase', shortPropertyData.stamp_duty_purchase);
                    setValue('stamp_duty_purchase_month', shortPropertyData.stamp_duty_purchase_month);
                    setValue('loan_fee_purchase', shortPropertyData.loan_fee_purchase);
                    setValue('loan_fee_purchase_month', shortPropertyData.loan_fee_purchase_month);
                }

                // 長期物件情報の設定
                if (longPropertyData) {
                    setValue('property_name_long', longPropertyData.property_name);
                    setValue('purchase_date_long', propertyData.purchase_date);
                    setValue('address_long', propertyData.address); // 基本物件と同じ住所
                    setValue('land_area_sqm_long', propertyData.land_area_sqm);
                    setValue('building_area_sqm_long', propertyData.building_area_sqm);
                    setValue('structure_long', propertyData.structure);
                    setValue('construction_date_long', propertyData.construction_date);
                    setValue('progress_status_long', propertyData.progress_status);
                    calculateBuildingAgeLong();
                    setValue('useful_life_long', longPropertyData.useful_life);
                    setValue('economic_useful_life_long', longPropertyData.economic_useful_life);
                    setValue('land_tax_value_long', propertyData.fixed_asset_tax_land);
                    setValue('building_tax_value_long', propertyData.fixed_asset_tax_building);
                    calculateFixedAssetTaxTotalLong();
                    setValue('current_rental_income_long', longPropertyData.current_rental_income);
                    setValue('full_occupancy_income_long', longPropertyData.full_occupancy_income);
                    setValue('yield_rate_long', longPropertyData.yield_rate);
                    setValue('purchase_price_long', propertyData.purchase_price);
                    if (propertyData.working_capital_id) {
                        const wcSelectLong = document.getElementById('working_capital_id_long');
                        if (wcSelectLong) wcSelectLong.value = propertyData.working_capital_id;
                    }
                    
                    setValue('loan_amount_long', propertyData.loan_amount);
                    setValue('loan_period_long', propertyData.loan_period);
                    setValue('loan_ratio_long', longPropertyData.loan_ratio);
                    calculateLoanRatioLong();
                    setValue('loan_rate_long', propertyData.loan_interest_rate);
                    setValue('loan_fee_rate_long', propertyData.loan_fee_rate);
                    setValue('planned_sale_amount_long', propertyData.planned_sale_amount);
                    setValue('planned_sale_month_long', propertyData.planned_sale_month);
                    setValue('bank_collateral_value_long', propertyData.bank_collateral_value);
                    setValue('bank_risk_long', propertyData.bank_risk_amount);
                    setValue('fiscal_year_end_long', propertyData.closing_date);
                    
                    // 長期物件専用フィールド
                    setValue('management_fee_long', longPropertyData.management_fee);
                    setValue('repair_cost_long', longPropertyData.repair_cost);
                    setValue('property_tax_long', longPropertyData.property_tax);
                    setValue('utility_cost_long', longPropertyData.utility_cost);
                    setValue('depreciation_long', longPropertyData.depreciation);
                    setValue('other_expenses_long', longPropertyData.other_expenses);
                    setValue('current_balance_long', longPropertyData.current_balance);
                    setValue('full_occupancy_balance_long', longPropertyData.full_occupancy_balance);
                    setValue('repair_fund_total_long', longPropertyData.repair_fund_total);
                    setValue('current_loan_balance_long', longPropertyData.current_loan_balance);
                    setValue('annual_principal_payment_long', longPropertyData.annual_principal_payment);
                    setValue('annual_interest_payment_long', longPropertyData.annual_interest_payment);
                    setSelectedOption('collateral_status_long', longPropertyData.collateral_status);
                    setSelectedOption('repair_history_long', longPropertyData.repair_history);
                }

                // 相談銀行情報の設定
                populateConsultationBanks(consultationBanks);

                formMessage.textContent = 'データ読み込み完了';
                setTimeout(() => {
                    formMessage.textContent = '';
                }, 1000);
                
            } catch (error) {
                console.error('populateForm エラー:', error);
                formMessage.className = 'error';
                formMessage.textContent = 'データ設定エラー: ' + error.message;
            }
        }

        // 相談銀行情報テーブルの設定
        function populateConsultationBanks(consultationBanks) {
            console.log('populateConsultationBanks - 受信データ:', consultationBanks);
            
            // 既存の行をクリア（ヘッダー以外）
            const tbody = document.querySelector('#bankMemosTable tbody');
            const rows = tbody.querySelectorAll('tr');
            
            // データ行（3行目以降）を削除
            for (let i = rows.length - 1; i >= 2; i--) {
                rows[i].remove();
            }

            // 行カウンターをリセット（削除後は0行の状態）
            bankRowCounter = 0;

            // 相談銀行データがある場合、すべてのデータに対して行を確保
            if (consultationBanks && consultationBanks.length > 0) {
                // データ件数分だけ行を追加
                for (let i = 0; i < consultationBanks.length; i++) {
                    addBankRow();
                }
                
                consultationBanks.forEach((bank, index) => {
                    const rowIndex = index + 1;
                    setValue(`bank_name_${rowIndex}`, bank.bank_name);
                    // consultation_dateをDBから設定
                    setValue(`consultation_date_${rowIndex}`, bank.consultation_date);
                    setValue(`response_date_${rowIndex}`, bank.response_date);
                    setValue(`response_days_${rowIndex}`, bank.response_days);
                    // DBに保存されているresponse_daysがある場合は再計算しない
                    if (!bank.response_days && bank.consultation_date && bank.response_date) {
                        calculateResponseDays(rowIndex);
                    }
                    setValue(`response_amount_${rowIndex}`, bank.response_amount);
                    setValue(`response_interest_rate_${rowIndex}`, bank.response_interest_rate);
                    setValue(`loan_ratio_${rowIndex}`, bank.response_loan_ratio);
                    setValue(`remarks_${rowIndex}`, bank.remarks);
                });
            }
        }

        // 相談銀行情報テーブルの行管理
        let bankRowCounter = 1;

        function addBankRow() {
            bankRowCounter++;
            const tbody = document.querySelector('#bankMemosTable tbody');
            const newRow = document.createElement('tr');
            newRow.style.height = '20px';

            newRow.innerHTML = `
                <th id="bankRow${bankRowCounter}" style="height: 20px;" class="row-headers-background">
                    <div class="row-header-wrapper" style="line-height: 20px">${bankRowCounter + 2}</div>
                </th>
                <td class="s0"></td>
                <td class="s4" dir="ltr">
                    <input type="text" id="bank_name_${bankRowCounter}" name="bank_name_${bankRowCounter}" list="consultation_bank_list_${bankRowCounter}"
                           style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="銀行名を入力または選択">
                    <datalist id="consultation_bank_list_${bankRowCounter}">
                    </datalist>
                </td>
                <td class="s4" dir="ltr">
                    <input type="date" id="consultation_date_${bankRowCounter}" name="consultation_date_${bankRowCounter}" 
                           style="width: 100%; border: none; background: transparent; text-align: center;"
                           onchange="calculateResponseDays(${bankRowCounter})">
                </td>
                <td class="s4" dir="ltr">
                    <input type="date" id="response_date_${bankRowCounter}" name="response_date_${bankRowCounter}" 
                           style="width: 100%; border: none; background: transparent; text-align: center;"
                           onchange="calculateResponseDays(${bankRowCounter})">
                </td>
                <td class="s4" dir="ltr">
                    <input type="number" id="response_days_${bankRowCounter}" name="response_days_${bankRowCounter}" min="0" 
                           style="width: 100%; border: none; background: transparent; text-align: center;">
                </td>
                <td class="s4" dir="ltr">
                    <input type="number" id="response_amount_${bankRowCounter}" name="response_amount_${bankRowCounter}" min="0" 
                           style="width: 100%; border: none; background: transparent; text-align: center;">
                </td>
                <td class="s4" dir="ltr">
                    <input type="number" id="response_interest_rate_${bankRowCounter}" name="response_interest_rate_${bankRowCounter}" min="0" step="0.01" 
                           style="width: 100%; border: none; background: transparent; text-align: center;">
                </td>
                <td class="s4" dir="ltr">
                    <input type="number" id="loan_ratio_${bankRowCounter}" name="loan_ratio_${bankRowCounter}" min="0" max="100" step="0.01" 
                           style="width: 100%; border: none; background: transparent; text-align: center;">
                </td>
                <td class="s5">
                    <input type="text" id="remarks_${bankRowCounter}" name="remarks_${bankRowCounter}" 
                           style="width: 100%; border: none; background: transparent; text-align: left;">
                </td>
                <td class="s3" style="text-align: center;">
                    <button type="button" class="delete-row-btn" onclick="deleteBankRow(this)">×</button>
                </td>
            `;

            tbody.appendChild(newRow);
            
            // 新しく追加された行の銀行datalistに銀行データを追加
            populateConsultationBankDatalistForRow(bankRowCounter);
            
            // 新しく追加されたフィールドに銀行選択イベントを追加
            addConsultationBankEventListener(bankRowCounter);
        }

        function deleteBankRow(button) {
            const row = button.closest('tr');
            row.remove();

            // 行番号を振り直す
            const tbody = document.querySelector('#bankMemosTable tbody');
            const dataRows = tbody.querySelectorAll('tr');

            // ヘッダー行（最初の2行）をスキップして、データ行のみ処理
            for (let i = 2; i < dataRows.length; i++) {
                const rowHeader = dataRows[i].querySelector('.row-header-wrapper');
                if (rowHeader) {
                    rowHeader.textContent = i + 1;
                }
            }
        }

        // フォーム送信処理
        function submitForm() {
            try {
                const formMessage = document.getElementById('formMessage');
                formMessage.className = 'loading';
                formMessage.textContent = 'データを収集中...';

                // 現在アクティブなタブを確認
                const isShortTerm = document.getElementById('tab1').checked;
                const isLongTerm = document.getElementById('tab2').checked;
                const typeValue = isShortTerm ? 1 : 2;

                // 基本物件情報の収集
                const propertyData = {
                    id: currentPropertyId,
                    type: typeValue,
                    purchase_date: isShortTerm ? (getValue('purchase_date') || null) : 
                                 isLongTerm ? (getValue('purchase_date_long') || null) : null,
                    address: isShortTerm ? (getValue('address') || null) : 
                           isLongTerm ? (getValue('address_long') || null) : null,
                    land_area_sqm: isShortTerm ? (getNumericValue('land_area_sqm') || null) : 
                                 isLongTerm ? (getNumericValue('land_area_sqm_long') || null) : null,
                    fixed_asset_tax_land: isShortTerm ? (getNumericValue('land_tax_value') || null) : 
                                        isLongTerm ? (getNumericValue('land_tax_value_long') || null) : null,
                    building_area_sqm: isShortTerm ? (getNumericValue('building_area_sqm') || null) : 
                                     isLongTerm ? (getNumericValue('building_area_sqm_long') || null) : null,
                    structure: isShortTerm ? (getValue('structure') || null) : 
                             isLongTerm ? (getValue('structure_long') || null) : null,
                    construction_date: isShortTerm ? (getValue('construction_date') || null) : 
                                     isLongTerm ? (getValue('construction_date_long') || null) : null,
                    building_age: isShortTerm ? (getNumericValue('building_age') || null) : 
                                isLongTerm ? (getNumericValue('building_age_long') || null) : null,
                    fixed_asset_tax_building: isShortTerm ? (getNumericValue('building_tax_value') || null) : 
                                            isLongTerm ? (getNumericValue('building_tax_value_long') || null) : null,
                    purchase_price: isShortTerm ? (getNumericValue('purchase_price') || null) : 
                                  isLongTerm ? (getNumericValue('purchase_price_long') || null) : null,
                    loan_amount: isShortTerm ? (getNumericValue('loan_amount') || null) : 
                                isLongTerm ? (getNumericValue('loan_amount_long') || null) : null,
                    loan_period: isShortTerm ? (getNumericValue('loan_period') || null) : 
                                isLongTerm ? (getNumericValue('loan_period_long') || null) : null,
                    loan_interest_rate: isShortTerm ? (getNumericValue('loan_rate') || null) : 
                                       isLongTerm ? (getNumericValue('loan_rate_long') || null) : null,
                    loan_fee_rate: isShortTerm ? (getNumericValue('loan_fee_rate') || null) : 
                                  isLongTerm ? (getNumericValue('loan_fee_rate_long') || null) : null,
                    planned_sale_amount: isShortTerm ? (getNumericValue('planned_sale_amount') || null) : 
                                       isLongTerm ? (getNumericValue('planned_sale_amount_long') || null) : null,
                    planned_sale_month: isShortTerm ? (getNumericValue('planned_sale_month') || null) : 
                                      isLongTerm ? (getNumericValue('planned_sale_month_long') || null) : null,
                    bank_collateral_value: isShortTerm ? (getNumericValue('bank_collateral_value') || null) : 
                                         isLongTerm ? (getNumericValue('bank_collateral_value_long') || null) : null,
                    bank_risk_amount: isShortTerm ? (getNumericValue('bank_risk') || null) : 
                                    isLongTerm ? (getNumericValue('bank_risk_long') || null) : null,
                    remarks: isShortTerm ? (getValue('remarks') || null) : 
                           isLongTerm ? (getValue('remarks_long') || null) : null,
                    loan_ratio: isShortTerm ? (getNumericValue('loan_ratio') || null) : 
                              isLongTerm ? (getNumericValue('loan_ratio_long') || null) : null,
                    fixed_asset_tax_total: isShortTerm ? (getNumericValue('fixed_asset_tax_total') || null) : 
                                         isLongTerm ? (getNumericValue('fixed_asset_tax_total_long') || null) : null,
                    closing_date: isShortTerm ? (getValue('closing_date') || null) : 
                                isLongTerm ? (getValue('closing_date_long') || null) : null,
                    working_capital_id: null // 新しいスキーマでは working_capital_id を使用
                };

                // 短期物件データの収集
                let shortPropertyData = null;
                if (isShortTerm) {
                    shortPropertyData = collectShortPropertyData();
                }

                // 長期物件データの収集
                let longPropertyData = null;
                if (isLongTerm) {
                    longPropertyData = collectLongPropertyData();
                }

                // 相談銀行情報の収集
                const consultationBanks = collectConsultationBanks();

                // セレクトされた working_capital_id を設定
                const selectedWorkingCapitalId = isShortTerm
                    ? (document.getElementById('working_capital_id')?.value || '')
                    : (document.getElementById('working_capital_id_long')?.value || '');
                propertyData.working_capital_id = selectedWorkingCapitalId !== '' ? Number(selectedWorkingCapitalId) : null;

                // 直接更新処理を実行
                executeUpdate(propertyData, shortPropertyData, longPropertyData, consultationBanks);

            } catch (error) {
                const formMessage = document.getElementById('formMessage');
                formMessage.className = 'error';
                formMessage.textContent = 'スクリプトエラー: ' + error.message;
            }
        }

        // 更新実行関数
        function executeUpdate(propertyData, shortPropertyData, longPropertyData, consultationBanks) {
            const formMessage = document.getElementById('formMessage');
            formMessage.className = 'loading';
            formMessage.textContent = '物件データを更新中...';
            
            const updateData = {
                propertyData: propertyData,
                shortPropertyData: shortPropertyData,
                longPropertyData: longPropertyData,
                consultationBanks: consultationBanks
            };
            
            google.script.run
                .withSuccessHandler(function(result) {
                    if (result && result.success) {
                        formMessage.className = 'success';
                        formMessage.textContent = '更新完了: ID=' + (result.property_id || '');
                        setTimeout(() => {
                            google.script.host.close();
                        }, 1500);
                    } else {
                        formMessage.className = 'error';
                        formMessage.textContent = 'エラー: ' + (result ? result.message : '不明なエラー');
                    }
                })
                .withFailureHandler(function(error) {
                    formMessage.className = 'error';
                    formMessage.textContent = '通信エラー: ' + error.toString();
                })
                .updatePropertyWithDetails(updateData);
        }

        // ヘルパー関数
        function getValue(elementId) {
            const element = document.getElementById(elementId);
            return element ? element.value.trim() : '';
        }

        function setValue(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                if (value !== null && value !== undefined) {
                    // 0や空文字列も有効な値として設定
                    element.value = value;
                    
                    // セレクトボックスの場合は適切なオプションを選択
                    if (element.tagName === 'SELECT') {
                        const options = element.options;
                        for (let i = 0; i < options.length; i++) {
                            if (options[i].value === value) {
                                element.selectedIndex = i;
                                break;
                            }
                        }
                    }
                    
                    console.log('setValue - ' + elementId + ':', value, '(設定済み)');
                } else {
                    console.log('setValue - ' + elementId + ': 値がnull/undefinedのためスキップ');
                }
            } else {
                console.log('setValue - ' + elementId + ': 要素が見つかりません');
            }
        }

        function setSelectedOption(elementId, value) {
            const element = document.getElementById(elementId);
            if (element && element.tagName === 'SELECT') {
                if (value !== null && value !== undefined && value !== '') {
                    element.value = value;
                    // より確実な選択のために、option要素を直接チェック
                    const options = element.options;
                    for (let i = 0; i < options.length; i++) {
                        if (options[i].value === value) {
                            options[i].selected = true;
                            break;
                        }
                    }
                } else {
                    // 空値の場合は最初のオプション（選択してください）を選択
                    element.selectedIndex = 0;
                }
            }
        }

        function getNumericValue(elementId) {
            const value = getValue(elementId);
            return value ? parseFloat(value) : null;
        }

        function getCombinedFloorPlan() {
            const number = getValue('floor_plan_number');
            const unit = getValue('floor_plan_unit');
            if (number && unit) {
                return number + unit;
            }
            return '';
        }

        function setFloorPlan(value) {
            if (!value) {
                setValue('floor_plan_number', '');
                setValue('floor_plan_unit', 'R');
                return;
            }
            
            // 数値と単位を分解
            const match = value.match(/^(\d+(?:\.\d+)?)(R|LDK)$/);
            if (match) {
                setValue('floor_plan_number', match[1]);
                setValue('floor_plan_unit', match[2]);
            } else {
                // パターンマッチしない場合は、元の値をnumberフィールドに設定
                setValue('floor_plan_number', value);
                setValue('floor_plan_unit', 'R');
            }
        }

        // 売却時想定利回りの自動計算
        function calculateSaleEstimatedYield() {
            const annualRentSale = getNumericValue('sale_estimated_annual_rent');
            const plannedSaleAmount = getNumericValue('planned_sale_amount');
            
            if (annualRentSale && plannedSaleAmount && plannedSaleAmount > 0) {
                const yieldValue = annualRentSale / plannedSaleAmount;
                const yieldElement = document.getElementById('sale_estimated_yield');
                if (yieldElement) {
                    yieldElement.value = yieldValue.toFixed(2);
                }
            } else {
                const yieldElement = document.getElementById('sale_estimated_yield');
                if (yieldElement) {
                    yieldElement.value = '';
                }
            }
            
            // 利回りが計算された後、ベスト売却金額も計算
            calculateBestSaleAmount();
        }

        // ベスト売却金額の自動計算 年間賃料 ÷ 利回り
        function calculateBestSaleAmount() {
            const annualRentSale = getNumericValue('sale_estimated_annual_rent');
            const yieldValue = getNumericValue('sale_estimated_yield');
            
            if (annualRentSale && yieldValue && yieldValue > 0) {
                const bestSaleAmount = annualRentSale / yieldValue;
                const bestSaleElement = document.getElementById('best_sale_amount');
                if (bestSaleElement) {
                    bestSaleElement.value = Math.round(bestSaleAmount);
                }
            } else {
                const bestSaleElement = document.getElementById('best_sale_amount');
                if (bestSaleElement) {
                    bestSaleElement.value = '';
                }
            }
        }

        // 固定資産税評価額合計の自動計算 (土地 + 建物)
        function calculateFixedAssetTaxTotal() {
            const landTaxValue = getNumericValue('land_tax_value') || 0;
            const buildingTaxValue = getNumericValue('building_tax_value') || 0;
            
            const total = landTaxValue + buildingTaxValue;
            const totalElement = document.getElementById('fixed_asset_tax_total');
            if (totalElement) {
                totalElement.value = total > 0 ? total : '';
            }
        }

        // 短期物件データの収集
        function collectShortPropertyData() {
            return {
                sales_method: getValue('sales_method'),
                nearest_station: getValue('nearest_station'),
                progress_status: getValue('progress_status'),
                property_type: getValue('property_type'),
                station_distance_min: getNumericValue('station_distance'),
                lot_number: getValue('lot_number'),
                map_url: getValue('map_url'),
                land_area_tsubo: getNumericValue('land_area_tsubo'),
                land_use_zone: getValue('land_use_zone'),
                building_coverage_ratio: getNumericValue('building_coverage'),
                floor_area_ratio: getNumericValue('floor_area_ratio'),
                road_width: getNumericValue('road_width'),
                road_setback: getNumericValue('road_setback'),
                floor_plan: getCombinedFloorPlan(),
                building_floors: getValue('floors'),
                room_number: getValue('room_number'),
                consumption_tax_amount: getNumericValue('consumption_tax_amount'),
                down_payment_tax_incl: getNumericValue('earnest_money'),
                down_payment_tax_incl_month: getNumericValue('earnest_month'),
                balance_payment_tax_incl: getNumericValue('balance_payment'),
                balance_payment_tax_incl_month: getNumericValue('balance_month'),
                brokerage_fee_purchase: getNumericValue('brokerage_fee_purchase'),
                brokerage_fee_purchase_month: getNumericValue('brokerage_fee_purchase_month'),
                registration_fee_purchase: getNumericValue('registration_fee_purchase'),
                registration_fee_purchase_month: getNumericValue('registration_fee_purchase_month'),
                real_estate_tax_purchase: getNumericValue('real_estate_tax_purchase'),
                real_estate_tax_purchase_month: getNumericValue('real_estate_tax_purchase_month'),
                stamp_duty_purchase: getNumericValue('stamp_duty_purchase'),
                stamp_duty_purchase_month: getNumericValue('stamp_duty_purchase_month'),
                loan_fee_purchase: getNumericValue('loan_fee_purchase'),
                loan_fee_purchase_month: getNumericValue('loan_fee_purchase_month'),
                property_tax: getNumericValue('property_tax'),
                property_tax_month: getNumericValue('property_tax_month'),
                fire_insurance: getNumericValue('fire_insurance'),
                fire_insurance_month: getNumericValue('fire_insurance_month'),
                survey_cost: getNumericValue('survey_fee'),
                survey_cost_month: getNumericValue('survey_fee_month'),
                demolition_cost: getNumericValue('demolition_fee'),
                demolition_cost_month: getNumericValue('demolition_fee_month'),
                renovation_cost: getNumericValue('renovation_fee'),
                renovation_cost_month: getNumericValue('renovation_fee_month'),
                management_fee: getNumericValue('outsourcing_fee'),
                management_fee_month: getNumericValue('outsourcing_fee_month'),
                other_land_expenses: getNumericValue('other_land_costs'),
                other_land_expenses_month: getNumericValue('other_land_costs_month'),
                other_building_expenses: getNumericValue('other_building_costs'),
                other_building_expenses_month: getNumericValue('other_building_costs_month'),
                other_expenses: getNumericValue('other_expenses'),
                other_expenses_month: getNumericValue('other_expenses_month'),
                brokerage_fee_sale: getNumericValue('brokerage_fee_sale'),
                brokerage_fee_sale_month: getNumericValue('brokerage_fee_sale_month'),
                advertising_fee_sale: getNumericValue('advertising_fee_sale'),
                advertising_fee_sale_month: getNumericValue('advertising_fee_sale_month'),
                bank_expenses_sale: getNumericValue('bank_expenses_sale'),
                bank_expenses_sale_month: getNumericValue('bank_expenses_sale_month'),
                mortgage_cancellation_fee: getNumericValue('mortgage_cancellation_fee'),
                mortgage_cancellation_fee_month: getNumericValue('mortgage_cancellation_fee_month'),
                registration_fee_sale: getNumericValue('registration_fee_sale'),
                registration_fee_sale_month: getNumericValue('registration_fee_sale_month'),
                total_cost: getNumericValue('total_cost'),
                employee_commission_sale: getNumericValue('employee_bonus'),
                responsible_employee: getValue('employee_name'),
                total_units: getNumericValue('total_units'),
                purchase_annual_rent: getNumericValue('annual_rent_purchase'),
                sale_estimated_annual_rent: getNumericValue('sale_estimated_annual_rent'),
                sale_estimated_yield: getNumericValue('sale_estimated_yield'),
                loan_repayment_amount: getNumericValue('loan_repayment'),
                loan_repayment_month: getNumericValue('loan_repayment_month'),
                down_payment_sale_tax_incl: getNumericValue('earnest_money_sale'),
                down_payment_sale_tax_incl_month: getNumericValue('earnest_money_sale_month'),
                balance_payment_sale_tax_incl: getNumericValue('remaining_payment_sale'),
                balance_payment_sale_tax_incl_month: getNumericValue('remaining_payment_sale_month'),
                best_sale_amount: getNumericValue('best_sale_amount'),
                best_sale_yield: getNumericValue('best_sale_yield'),
                planned_profit_amount: getNumericValue('planned_profit_amount'),
                planned_profit_rate: getNumericValue('planned_profit_rate'),
                best_profit_amount: getNumericValue('best_profit_amount'),
                best_profit_rate: getNumericValue('best_profit_rate')
            };
        }

        // 長期物件データの収集
        function collectLongPropertyData() {
            return {
                property_name: getValue('property_name_long'),
                current_rental_income: getNumericValue('current_rental_income_long'),
                full_occupancy_income: getNumericValue('full_occupancy_income_long'),
                yield_rate: getNumericValue('yield_rate_long'),
                useful_life: getNumericValue('useful_life_long'),
                economic_useful_life: getNumericValue('economic_useful_life_long'),
                repair_history: getValue('repair_history_long'),
                management_fee: getNumericValue('management_fee_long'),
                repair_cost: getNumericValue('repair_cost_long'),
                property_tax: getNumericValue('property_tax_long'),
                utility_cost: getNumericValue('utility_cost_long'),
                depreciation: getNumericValue('depreciation_long'),
                other_expenses: getNumericValue('other_expenses_long'),
                current_balance: getNumericValue('current_balance_long'),
                full_occupancy_balance: getNumericValue('full_occupancy_balance_long'),
                repair_fund_total: getNumericValue('repair_fund_total_long'),
                current_loan_balance: getNumericValue('current_loan_balance_long'),
                annual_principal_payment: getNumericValue('annual_principal_payment_long'),
                annual_interest_payment: getNumericValue('annual_interest_payment_long'),
                collateral_status: getValue('collateral_status_long'),
                loan_ratio: getNumericValue('loan_ratio_long'),
                is_deleted: false
            };
        }

        // 相談銀行情報の収集
        function collectConsultationBanks() {
            const consultationBanks = [];
            for (let i = 1; i <= bankRowCounter; i++) {
                const bankName = getValue(`bank_name_${i}`);
                if (bankName && bankName.trim() !== '') {
                    const bankInputElement = document.getElementById(`bank_name_${i}`);
                    const selectedBankCode = bankInputElement ? bankInputElement.getAttribute('data-selected-code') : null;
                    
                    // 選択された銀行の詳細情報を取得
                    let bankDetails = {};
                    if (selectedBankCode && bankData) {
                        const selectedBank = bankData.find(bank => bank.code === selectedBankCode);
                        if (selectedBank) {
                            bankDetails = {
                                bank_code: selectedBank.code || null,
                                bank_name_half_kana: selectedBank.halfWidthKana || null,
                                bank_name_full_kana: selectedBank.fullWidthKana || null,
                                bank_name_hiragana: selectedBank.hiragana || null,
                                bank_business_type_code: selectedBank.businessTypeCode || null,
                                bank_business_type: selectedBank.businessType || null
                            };
                        }
                    }
                    
                    consultationBanks.push({
                        bank_name: bankName,
                        bank_code: bankDetails.bank_code,
                        bank_name_half_kana: bankDetails.bank_name_half_kana,
                        bank_name_full_kana: bankDetails.bank_name_full_kana,
                        bank_name_hiragana: bankDetails.bank_name_hiragana,
                        bank_business_type_code: bankDetails.bank_business_type_code,
                        bank_business_type: bankDetails.bank_business_type,
                        consultation_date: getValue(`consultation_date_${i}`) || null,
                        response_date: getValue(`response_date_${i}`) || null,
                        response_days: getNumericValue(`response_days_${i}`) || null,
                        response_amount: getNumericValue(`response_amount_${i}`) || null,
                        response_interest_rate: getNumericValue(`response_interest_rate_${i}`) || null,
                        response_loan_ratio: getNumericValue(`loan_ratio_${i}`) || null,
                        remarks: getValue(`remarks_${i}`) || null
                    });
                }
            }
            return consultationBanks;
        }
    </script>
</body>

</html>