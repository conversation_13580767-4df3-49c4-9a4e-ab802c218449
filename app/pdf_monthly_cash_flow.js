/**
 * 資金繰表（月）PDF作成処理
 * 長期・短期物件の月次データを項目別に集計表示
 */

/**
 * 資金繰表（月）を作成する関数
 * 長期・短期物件の情報を項目別に月次集計して資金繰表を作成する
 */
function createCashFlowMonthlySimulation() {
  try {
    // 長期物件と短期物件データを取得
    const longTermProperties = fetchLongTermProperties();
    const shortTermProperties = fetchShortTermProperties();

    if (longTermProperties.length === 0 && shortTermProperties.length === 0) {
      SpreadsheetApp.getUi().alert(
        '物件データなし',
        '資金繰表対象の物件がありません。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // テンプレートタブをコピーして新しいタブを作成
    const templateSheetName = '【PDFテンプレート】資金繰表（月）';
    const newSheetName = '資金繰表（月）';
    const simulationSheet = copyTemplateSheet(templateSheetName, newSheetName);

    if (!simulationSheet) {
      SpreadsheetApp.getUi().alert(
        'テンプレートエラー',
        'PDFテンプレートタブのコピーに失敗しました。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // 月次資金繰データをシートに設定
    setMonthlyCashFlowDataToSheet(simulationSheet, longTermProperties, shortTermProperties);

    // 完了メッセージ
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      '資金繰表作成完了',
      '「資金繰表（月）」シートを作成しました。\n\n長期物件数: ' + longTermProperties.length + '件\n短期物件数: ' + shortTermProperties.length + '件',
      ui.ButtonSet.OK
    );

  } catch (error) {
    SpreadsheetApp.getUi().alert(
      '資金繰表作成エラー',
      '資金繰表（月）の作成に失敗しました。\n\nエラー詳細:\n' + error.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * PDFテンプレートタブをコピーして新しいタブを作成する
 * @param {string} templateSheetName テンプレートシート名
 * @param {string} newSheetName 新しく作成するシート名
 * @return {GoogleAppsScript.Spreadsheet.Sheet|null} 作成されたシート、失敗時はnull
 */
function copyTemplateSheet(templateSheetName, newSheetName) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

    // テンプレートシートを取得
    let templateSheet;
    try {
      templateSheet = spreadsheet.getSheetByName(templateSheetName);
    } catch (e) {
      throw new Error(`テンプレートシート「${templateSheetName}」が見つかりません。`);
    }

    // 既存の同名シートを削除（存在する場合）
    try {
      const existingSheet = spreadsheet.getSheetByName(newSheetName);
      spreadsheet.deleteSheet(existingSheet);
    } catch (e) {
      // シートが存在しない場合は何もしない
    }

    // テンプレートをコピー
    const newSheet = templateSheet.copyTo(spreadsheet);
    newSheet.setName(newSheetName);

    // 新しいシートをアクティブにする
    spreadsheet.setActiveSheet(newSheet);

    return newSheet;

  } catch (error) {
    console.error('テンプレートコピーエラー:', error.message);
    throw error;
  }
}

/**
 * 月次資金繰データをシートの各セルに設定する
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象シート
 * @param {Object[]} longTermProperties 長期物件データ配列
 * @param {Object[]} shortTermProperties 短期物件データ配列
 */
function setMonthlyCashFlowDataToSheet(sheet, longTermProperties, shortTermProperties) {
  try {
    // === 月次データ設定（2行目、D列〜O列）===

    // 2行目：年月（D2=2025/05, E2=2025/06, ...O2=2026/04）
    const currentYear = 2025;
    const currentMonth = 5; // 5月
    const startColumn = 4; // D列
    const endColumn = 15;  // O列（12ヶ月分）

    // 年月配列を作成
    const monthlyPeriods = [];
    for (let col = startColumn; col <= endColumn; col++) {
      const monthOffset = col - startColumn;
      const targetMonth = currentMonth + monthOffset;
      const targetYear = currentYear + Math.floor((targetMonth - 1) / 12);
      const displayMonth = ((targetMonth - 1) % 12) + 1;
      
      const yearMonthText = `${targetYear}/${String(displayMonth).padStart(2, '0')}`;
      const cellAddress = getColumnLetter(col) + '2';
      setValueIfCellExists(sheet, cellAddress, yearMonthText);
      
      monthlyPeriods.push({
        year: targetYear,
        month: displayMonth,
        column: col
      });
    }

    // 月別データを計算
    const monthlyData = calculateMonthlyData(longTermProperties, shortTermProperties, monthlyPeriods);

    // 各行にデータを設定
    setMonthlyDataToRows(sheet, monthlyData, startColumn, endColumn);

    // === メタ情報 ===

    // 作成日時
    const now = new Date();
    const formattedDate = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy/MM/dd HH:mm:ss');
    setValueIfCellExists(sheet, 'A1', `作成日時: ${formattedDate}`);

    // 対象物件数
    setValueIfCellExists(sheet, 'A3', `長期物件数: ${longTermProperties.length}件、短期物件数: ${shortTermProperties.length}件`);

  } catch (error) {
    console.error('データ設定エラー:', error.message);
    throw new Error('シートへのデータ設定に失敗しました: ' + error.message);
  }
}

/**
 * 月別の資金繰データを計算する
 * @param {Object[]} longTermProperties 長期物件データ配列
 * @param {Object[]} shortTermProperties 短期物件データ配列
 * @param {Object[]} monthlyPeriods 月次期間配列
 * @return {Object} 月別データ（行番号 => 月別配列）
 */
function calculateMonthlyData(longTermProperties, shortTermProperties, monthlyPeriods) {
  const monthlyData = {};

  // 各月の初期化
  monthlyPeriods.forEach(period => {
    const colIndex = period.column - 4; // D列=0, E列=1, ...
    
    // 初期化（各行番号ごと）
    [4, 5, 10, 11, 12, 13, 30, 31, 32, 34, 35, 36, 37].forEach(row => {
      if (!monthlyData[row]) monthlyData[row] = Array(12).fill(0);
    });
  });

  // 4行目: 長期物件売却
  longTermProperties.forEach(property => {
    if (property.planned_sale_price && property.planned_sale_month) {
      const saleMonth = property.planned_sale_month;
      const colIndex = getColumnIndexForMonth(saleMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[4][colIndex] += property.planned_sale_price;
      }
    }
  });

  // 5行目: 短期物件売却
  shortTermProperties.forEach(property => {
    if (property.planned_sale_price && property.planned_sale_month) {
      const saleMonth = property.planned_sale_month;
      const colIndex = getColumnIndexForMonth(saleMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[5][colIndex] += property.planned_sale_price;
      }
    }
  });

  // 10行目: 長期物件購入
  longTermProperties.forEach(property => {
    if (property.acquisition_price && property.acquisition_month) {
      const purchaseMonth = property.acquisition_month;
      const colIndex = getColumnIndexForMonth(purchaseMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[10][colIndex] += property.acquisition_price;
      }
    }
  });

  // 11行目: 短期物件購入
  shortTermProperties.forEach(property => {
    if (property.acquisition_price && property.acquisition_month) {
      const purchaseMonth = property.acquisition_month;
      const colIndex = getColumnIndexForMonth(purchaseMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[11][colIndex] += property.acquisition_price;
      }
    }
  });

  // 12行目: 長期物件経費
  longTermProperties.forEach(property => {
    if (property.acquisition_month) {
      const purchaseMonth = property.acquisition_month;
      const colIndex = getColumnIndexForMonth(purchaseMonth, monthlyPeriods);
      if (colIndex >= 0) {
        const totalExpenses = (property.management_fee_long || 0) + 
                             (property.repair_cost_long || 0) + 
                             (property.property_tax_long || 0) + 
                             (property.utility_cost_long || 0) + 
                             (property.fire_insurance_long || 0) + 
                             (property.other_expenses_long || 0);
        monthlyData[12][colIndex] += totalExpenses;
      }
    }
  });

  // 13行目: 短期物件経費
  shortTermProperties.forEach(property => {
    if (property.acquisition_month) {
      const purchaseMonth = property.acquisition_month;
      const colIndex = getColumnIndexForMonth(purchaseMonth, monthlyPeriods);
      if (colIndex >= 0) {
        const totalExpenses = (property.consumption_tax || 0) + 
                             (property.registration_fee_purchase || 0) + 
                             (property.fire_insurance || 0) + 
                             (property.other_land_costs || 0) + 
                             (property.other_building_costs || 0) + 
                             (property.other_expenses || 0) + 
                             (property.registration_fee_sale || 0);
        monthlyData[13][colIndex] += totalExpenses;
      }
    }
  });

  // 30行目: 長期物件借入
  longTermProperties.forEach(property => {
    if (property.loan_amount && property.acquisition_month) {
      const purchaseMonth = property.acquisition_month;
      const colIndex = getColumnIndexForMonth(purchaseMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[30][colIndex] += property.loan_amount;
      }
    }
  });

  // 31行目: 短期物件借入
  shortTermProperties.forEach(property => {
    if (property.loan_amount && property.acquisition_month) {
      const purchaseMonth = property.acquisition_month;
      const colIndex = getColumnIndexForMonth(purchaseMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[31][colIndex] += property.loan_amount;
      }
    }
  });

  // 32行目: 運転借入（空欄 - DB未作成）
  // monthlyData[32] は既に0で初期化済み

  // 34行目: 長期物件借入金返済
  longTermProperties.forEach(property => {
    if (property.loan_balance_current && property.planned_sale_month) {
      const saleMonth = property.planned_sale_month;
      const colIndex = getColumnIndexForMonth(saleMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[34][colIndex] += property.loan_balance_current;
      }
    }
  });

  // 35行目: 短期物件借入金返済
  shortTermProperties.forEach(property => {
    if (property.loan_repayment_amount && property.loan_repayment_month) {
      const repaymentMonth = property.loan_repayment_month;
      const colIndex = getColumnIndexForMonth(repaymentMonth, monthlyPeriods);
      if (colIndex >= 0) {
        monthlyData[35][colIndex] += property.loan_repayment_amount;
      }
    }
  });

  // 36行目: 運転資金借入返済（空欄 - DB未作成）
  // monthlyData[36] は既に0で初期化済み

  // 37行目: 支払利息
  monthlyPeriods.forEach((period, colIndex) => {
    let monthlyInterest = 0;

    // 短期物件の利息: 借入金額 × 借入金利 ÷ 12
    shortTermProperties.forEach(property => {
      if (property.loan_amount && property.loan_interest_rate) {
        monthlyInterest += (property.loan_amount * property.loan_interest_rate / 100) / 12;
      }
    });

    // 長期物件の利息: 借入残高（現時点） × 借入金利 ÷ 12
    longTermProperties.forEach(property => {
      if (property.loan_balance_current && property.loan_interest_rate_long) {
        monthlyInterest += (property.loan_balance_current * property.loan_interest_rate_long / 100) / 12;
      }
    });

    // 運転資金の利息（DB未作成のため、現在は0）
    // 将来的に運転資金のDBが作成されたら追加

    monthlyData[37][colIndex] = monthlyInterest;
  });

  return monthlyData;
}

/**
 * 指定された月に対応する列インデックスを取得する
 * @param {number} targetMonth 対象月（1-12）
 * @param {Object[]} monthlyPeriods 月次期間配列
 * @return {number} 列インデックス（0-11）、見つからない場合は-1
 */
function getColumnIndexForMonth(targetMonth, monthlyPeriods) {
  for (let i = 0; i < monthlyPeriods.length; i++) {
    if (monthlyPeriods[i].month === targetMonth) {
      return i;
    }
  }
  return -1;
}

/**
 * 月別データを各行に設定する
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象シート
 * @param {Object} monthlyData 月別データ（行番号 => 月別配列）
 * @param {number} startColumn 開始列（D列=4）
 * @param {number} endColumn 終了列（O列=15）
 */
function setMonthlyDataToRows(sheet, monthlyData, startColumn, endColumn) {
  // 各行にデータを設定
  Object.keys(monthlyData).forEach(row => {
    const rowNumber = parseInt(row);
    const monthlyValues = monthlyData[rowNumber];
    
    for (let col = startColumn; col <= endColumn; col++) {
      const colIndex = col - startColumn; // D列=0, E列=1, ...
      const value = monthlyValues[colIndex] || 0;
      const cellAddress = getColumnLetter(col) + rowNumber;
      
      // 0以外の値または明示的に0を設定したい行の場合のみ設定
      if (value !== 0 || [32, 36].includes(rowNumber)) { // 運転借入・運転資金借入返済は0でも設定
        setValueIfCellExists(sheet, cellAddress, value);
      }
    }
  });
}

/**
 * 長期物件の一覧を取得する
 * @return {Object[]} 長期物件の配列
 */
function fetchLongTermProperties() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');

    if (!accessToken || !clientId) {
      throw new Error('認証情報が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // 長期物件（property_type=2）のデータを取得
    const filter = `client_id=eq.${clientId}&property_type=eq.2&deleted_at=is.null&order=property_name.asc`;
    const url = `${supabaseUrl}/rest/v1/properties?select=*&${filter}`;

    const res = UrlFetchApp.fetch(url, {
      method: 'get',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('長期物件データ取得エラー: ' + (err.message || JSON.stringify(err)));
    }

    return JSON.parse(res.getContentText());

  } catch (error) {
    throw error;
  }
}

/**
 * 短期物件の一覧を取得する
 * @return {Object[]} 短期物件の配列
 */
function fetchShortTermProperties() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');

    if (!accessToken || !clientId) {
      throw new Error('認証情報が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // 短期物件（property_type=1）のデータを取得
    const filter = `client_id=eq.${clientId}&property_type=eq.1&deleted_at=is.null&order=property_name.asc`;
    const url = `${supabaseUrl}/rest/v1/properties?select=*&${filter}`;

    const res = UrlFetchApp.fetch(url, {
      method: 'get',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('短期物件データ取得エラー: ' + (err.message || JSON.stringify(err)));
    }

    return JSON.parse(res.getContentText());

  } catch (error) {
    throw error;
  }
}

/**
 * セルが存在する場合のみ値を設定するヘルパー関数
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象シート
 * @param {string} cellAddress セルアドレス（例：'A1'）
 * @param {any} value 設定する値
 */
function setValueIfCellExists(sheet, cellAddress, value) {
  try {
    if (value !== null && value !== undefined) {
      sheet.getRange(cellAddress).setValue(value);
    }
  } catch (error) {
    // セルが存在しない場合は警告のみ出力（エラーにしない）
    console.warn(`セル ${cellAddress} への値設定に失敗: ${error.message}`);
  }
}

/**
 * 列番号をアルファベットに変換するヘルパー関数
 * @param {number} columnNumber 列番号（1=A, 2=B, 3=C, ...）
 * @return {string} アルファベット表記の列名
 */
function getColumnLetter(columnNumber) {
  let columnName = '';
  while (columnNumber > 0) {
    columnNumber--;
    columnName = String.fromCharCode(65 + (columnNumber % 26)) + columnName;
    columnNumber = Math.floor(columnNumber / 26);
  }
  return columnName;
} 