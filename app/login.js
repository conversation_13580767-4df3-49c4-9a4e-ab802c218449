// ログインボタン
function loginToSupabase() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // --- ここでD列をリセット ---
  sheet.getRange("C12:C14").clearContent();
  const email = sheet.getRange("D6").getValue();      // 例: B2 にメール
  const password = sheet.getRange("D7").getValue();   // 例: B3 にパスワード

  // 特定のキーの値を取得
  const scriptProperties = PropertiesService.getScriptProperties(); 
  const supabaseUrl = scriptProperties.getProperty('API_URL');
  const apiKey = scriptProperties.getProperty('API_KEY');

  const payload = {
    email: email,
    password: password
  };

  const response = UrlFetchApp.fetch(`${supabaseUrl}/auth/v1/token?grant_type=password`, {
    method: "post",
    contentType: "application/json",
    headers: {
      apikey: apiKey
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true // エラー内容を取得しやすくする
  });

  const result = JSON.parse(response.getContentText());

  if (response.getResponseCode() === 200) {
    access_token = result.access_token;
    const userEmail = result.user.email;
    const userId       = result.user.id;

    // アクセストークンをドキュメントプロパティに保存
    PropertiesService.getDocumentProperties().setProperty('SUPABASE_ACCESS_TOKEN', access_token);

    // ─── ここから追加 ───
    // profiles テーブルから client_id と role を取得
    const profileRes = UrlFetchApp.fetch(
      `${supabaseUrl}/rest/v1/profiles?select=client_id,role&id=eq.${userId}`,
      {
        method:      "get",
        headers: {
          apikey:        apiKey,
          Authorization: "Bearer " + access_token
        },
        muteHttpExceptions: true
      }
    );
    const profiles = JSON.parse(profileRes.getContentText());
    if (profileRes.getResponseCode() === 200 && profiles.length > 0) {
      const clientId = profiles[0].client_id;
      const role = profiles[0].role;
      
      if (role === 1) {
        // 管理者の場合
        PropertiesService.getDocumentProperties().setProperty('SUPABASE_CLIENT_ID', '');
        sheet.getRange("C12").setValue("✅ 管理者ログイン成功：" + userEmail);
      } else if (clientId) {
        // クライアントユーザーでclient_idが存在する場合
        PropertiesService.getDocumentProperties().setProperty('SUPABASE_CLIENT_ID', clientId);
        sheet.getRange("C12").setValue("✅ クライアントログイン成功：" + userEmail);
      } else {
        // クライアントユーザーだがclient_idが設定されていない場合
        PropertiesService.getDocumentProperties().setProperty('SUPABASE_CLIENT_ID', '');
        sheet.getRange("C12").setValue("⚠️ ログイン成功しましたが、クライアントIDが未設定です：" + userEmail);
      }
    } else {
      sheet.getRange("C12").setValue("✅ ログイン成功：" + userEmail);
      sheet.getRange("C13").setValue("プロフィール情報の取得に失敗");
      sheet.getRange("C14").setValue("❌ プロフィール取得失敗：" + profileRes.getContentText());
    }

  } else {
    sheet.getRange("C14").setValue("❌ ログイン失敗：" + result.error_description || result.error);
  }
}





