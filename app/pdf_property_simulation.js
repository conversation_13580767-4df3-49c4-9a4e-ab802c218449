/**
 * 長期物件購入シミュレーションPDF出力処理
 * property_overview.jsを参考に作成
 */

/**
 * 長期物件購入シミュレーション（個別）を作成する関数
 * 指定された物件の情報を使ってPDFテンプレートをコピーし、データを設定する
 * @param {string} propertyId 対象物件のID
 */
function createLongTermPropertySimulationIndividual(propertyId) {
  try {
    // 物件データを取得
    const property = fetchPropertyForSimulation(propertyId);

    if (!property) {
      SpreadsheetApp.getUi().alert(
        '物件データなし',
        '指定された物件データが見つかりません。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // テンプレートタブをコピーして新しいタブを作成
    const templateSheetName = '【PDFテンプレート】長期物件購入シミュレーション';
    const newSheetName = '長期物件購入シミュレーション（個別）';
    const simulationSheet = copyTemplateSheet(templateSheetName, newSheetName);

    if (!simulationSheet) {
      SpreadsheetApp.getUi().alert(
        'テンプレートエラー',
        'PDFテンプレートタブのコピーに失敗しました。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // 個別物件データをシートに設定
    setPropertyDataToSheet(simulationSheet, [property], 'individual');

    // 完了メッセージ
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'シミュレーション作成完了',
      '「長期物件購入シミュレーション（個別）」シートを作成しました。\n\n物件名: ' + (property.property_name || '未設定'),
      ui.ButtonSet.OK
    );

  } catch (error) {
    SpreadsheetApp.getUi().alert(
      'シミュレーション作成エラー',
      '長期物件購入シミュレーション（個別）の作成に失敗しました。\n\nエラー詳細:\n' + error.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * 長期物件購入シミュレーション（合算）を作成する関数
 * 全ての長期物件の情報を合算してシミュレーションを作成する
 */
function createLongTermPropertySimulationCombined() {
  try {
    // 全ての長期物件データを取得
    const properties = fetchLongTermProperties();

    if (properties.length === 0) {
      SpreadsheetApp.getUi().alert(
        '物件データなし',
        '長期物件購入シミュレーション対象の物件（取得済み）がありません。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // テンプレートタブをコピーして新しいタブを作成
    const templateSheetName = '【PDFテンプレート】長期物件購入シミュレーション';
    const newSheetName = '長期物件購入シミュレーション（合算）';
    const simulationSheet = copyTemplateSheet(templateSheetName, newSheetName);

    if (!simulationSheet) {
      SpreadsheetApp.getUi().alert(
        'テンプレートエラー',
        'PDFテンプレートタブのコピーに失敗しました。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // 合算物件データをシートに設定
    setPropertyDataToSheet(simulationSheet, properties, 'combined');

    // 完了メッセージ
    const ui = SpreadsheetApp.getUi();
    ui.alert(
      'シミュレーション作成完了',
      '「長期物件購入シミュレーション（合算）」シートを作成しました。\n\n対象物件数: ' + properties.length + '件',
      ui.ButtonSet.OK
    );

  } catch (error) {
    SpreadsheetApp.getUi().alert(
      'シミュレーション作成エラー',
      '長期物件購入シミュレーション（合算）の作成に失敗しました。\n\nエラー詳細:\n' + error.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * PDFテンプレートタブをコピーして新しいタブを作成する
 * @param {string} templateSheetName テンプレートシート名
 * @param {string} newSheetName 新しく作成するシート名
 * @return {GoogleAppsScript.Spreadsheet.Sheet|null} 作成されたシート、失敗時はnull
 */
function copyTemplateSheet(templateSheetName, newSheetName) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

    // テンプレートシートを取得
    let templateSheet;
    try {
      templateSheet = spreadsheet.getSheetByName(templateSheetName);
    } catch (e) {
      throw new Error(`テンプレートシート「${templateSheetName}」が見つかりません。`);
    }

    // 既存の同名シートを削除（存在する場合）
    try {
      const existingSheet = spreadsheet.getSheetByName(newSheetName);
      spreadsheet.deleteSheet(existingSheet);
    } catch (e) {
      // シートが存在しない場合は何もしない
    }

    // テンプレートをコピー
    const newSheet = templateSheet.copyTo(spreadsheet);
    newSheet.setName(newSheetName);

    // 新しいシートをアクティブにする
    spreadsheet.setActiveSheet(newSheet);

    return newSheet;

  } catch (error) {
    console.error('テンプレートコピーエラー:', error.message);
    throw error;
  }
}

/**
 * 指定された物件IDの詳細データを取得する
 * @param {string} propertyId 物件ID
 * @return {Object|null} 物件データ、見つからない場合はnull
 */
function fetchPropertyForSimulation(propertyId) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');

    if (!accessToken || !clientId) {
      const errorMsg = 'SUPABASE_ACCESS_TOKEN または SUPABASE_CLIENT_ID が設定されていません。';
      SpreadsheetApp.getUi().alert(
        '設定エラー',
        errorMsg + '\n\n管理者にお問い合わせください。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // 指定されたIDの物件データを取得
    const filter = `id=eq.${propertyId}&client_id=eq.${clientId}&deleted_at=is.null`;
    const url = `${supabaseUrl}/rest/v1/properties?select=*&${filter}`;

    const res = UrlFetchApp.fetch(url, {
      method: 'get',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      const errorMsg = '物件データ取得エラー: ' + (err.message || JSON.stringify(err));
      SpreadsheetApp.getUi().alert(
        '物件データ取得エラー',
        'Supabaseからの物件データ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const properties = JSON.parse(res.getContentText());
    return properties.length > 0 ? properties[0] : null;

  } catch (error) {
    // 既にダイアログ表示済みでない場合のみ表示
    if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('物件データ取得エラー')) {
      SpreadsheetApp.getUi().alert(
        '予期しないエラー',
        '物件データの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
    throw error;
  }
}

/**
 * 物件データをシートの各セルに設定する（個別・合算対応）
 * propertyTable2の項目構造に基づいてマッピング
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象シート
 * @param {Object[]} properties 物件データ配列（個別の場合は1件、合算の場合は複数件）
 * @param {string} type シミュレーションタイプ（'individual' または 'combined'）
 */
function setPropertyDataToSheet(sheet, properties, type) {
  try {
    // === 年間データ設定（3行目〜5行目、C列〜AG列）===

    // 3行目：年（C3=2025, D3=2026, ...AG3=2055）
    const currentYear = 2025;
    const startColumn = 3; // C列
    const endColumn = 33;  // AG列（30年分）

    // 年を設定（C3からAG3まで）
    for (let col = startColumn; col <= endColumn; col++) {
      const year = currentYear + (col - startColumn);
      const cellAddress = getColumnLetter(col) + '3';
      setValueIfCellExists(sheet, cellAddress, year);
    }

    // データの合算処理
    const propertyData = calculatePropertyData(properties, type);

    // 指定された行にデータを30年分設定
    setYearlyDataToSheet(sheet, propertyData, startColumn, endColumn);

    // === 基本物件情報（合算データ）の設定 ===

    // 総取得価格
    if (propertyData.totalAcquisitionPrice > 0) {
      setValueIfCellExists(sheet, 'C18', propertyData.totalAcquisitionPrice); // 購入金額
    }

    // 総借入金額
    if (propertyData.totalLoanAmount > 0) {
      setValueIfCellExists(sheet, 'I19', propertyData.totalLoanAmount); // 借入金額
    }

    // === 物件詳細情報（個別の場合のみ）===

    if (type === 'individual' && properties.length > 0) {
      const property = properties[0];

      // 物件名（propertyTable2のid="property_name_long"に対応）
      if (property.property_name) {
        setValueIfCellExists(sheet, 'C5', property.property_name); // 例：5行目のC列
      }

      // 住所（propertyTable2のid="address_long"に対応）
      if (property.address) {
        setValueIfCellExists(sheet, 'C6', property.address); // 例：6行目のC列
      }

      // 土地面積（㎡）（propertyTable2のid="land_area_sqm_long"に対応）
      if (property.land_area_sqm) {
        setValueIfCellExists(sheet, 'C8', property.land_area_sqm); // 例：8行目のC列
      }

      // 建物延べ面積（㎡）（propertyTable2のid="building_area_sqm_long"に対応）
      if (property.building_area_sqm) {
        setValueIfCellExists(sheet, 'I8', property.building_area_sqm); // 例：8行目のI列
      }

      // 構造（propertyTable2のid="structure_long"に対応）
      if (property.structure) {
        setValueIfCellExists(sheet, 'I9', property.structure); // 例：9行目のI列
      }

      // 築年月日（propertyTable2のid="construction_date_long"に対応）
      if (property.construction_date) {
        setValueIfCellExists(sheet, 'I10', property.construction_date); // 例：10行目のI列
      }

      // 銀行名
      if (property.bank_name) {
        setValueIfCellExists(sheet, 'I18', property.bank_name); // 例：18行目のI列
      }
    }

    // === 合算詳細情報（合算の場合のみ）===

    if (type === 'combined') {
      // タイトルを設定
      setValueIfCellExists(sheet, 'C5', `長期物件合算シミュレーション（${properties.length}件）`);

      // 物件一覧を別の場所に設定
      let propertyListText = '対象物件:\n';
      properties.forEach((property, index) => {
        propertyListText += `${index + 1}. ${property.property_name || '名称未設定'} (${property.address || '住所未設定'})\n`;
      });
      setValueIfCellExists(sheet, 'C30', propertyListText); // 例：30行目に物件一覧
    }

    // === 評価額・賃貸情報（共通・合算対応）===

    // 現状賃貸収入（年間）（合算済み）
    if (propertyData.totalCurrentRental > 0) {
      setValueIfCellExists(sheet, 'I12', propertyData.totalCurrentRental); // 例：12行目のI列
    }

    // 満室想定賃貸収入（年間）（合算済み）
    if (propertyData.totalFullRental > 0) {
      setValueIfCellExists(sheet, 'I13', propertyData.totalFullRental); // 例：13行目のI列
    }

    // === 個別物件の詳細情報（個別の場合のみ）===

    if (type === 'individual' && properties.length > 0) {
      const property = properties[0];

      // 固定資産税評価額（土地）（propertyTable2のid="land_tax_value_long"に対応）
      if (property.land_tax_value) {
        setValueIfCellExists(sheet, 'C12', property.land_tax_value); // 例：12行目のC列
      }

      // 利回り（propertyTable2のid="yield_rate_long"に対応）
      if (property.yield_rate) {
        setValueIfCellExists(sheet, 'I14', property.yield_rate); // 例：14行目のI列
      }

      // 耐用年数（propertyTable2のid="useful_life_long"に対応）
      if (property.useful_life) {
        setValueIfCellExists(sheet, 'C10', property.useful_life);
      }

      // 修繕履歴（propertyTable2のid="repair_history_long"に対応）
      if (property.repair_history) {
        setValueIfCellExists(sheet, 'C15', property.repair_history);
      }

      // 予定売却情報
      if (property.planned_sale_price) {
        setValueIfCellExists(sheet, 'J26', property.planned_sale_price); // 例：26行目のJ列
      }

      if (property.planned_sale_month) {
        setValueIfCellExists(sheet, 'L26', property.planned_sale_month); // 例：26行目のL列
      }
    }

    // === 合算データの計算項目（合算の場合のみ）===

    if (type === 'combined') {
      // 平均利回りを計算
      let totalYieldRate = 0;
      let yieldRateCount = 0;
      properties.forEach(property => {
        if (property.yield_rate) {
          totalYieldRate += property.yield_rate;
          yieldRateCount++;
        }
      });
      if (yieldRateCount > 0) {
        const averageYieldRate = totalYieldRate / yieldRateCount;
        setValueIfCellExists(sheet, 'I14', averageYieldRate); // 平均利回り
      }
    }

    // === メタ情報 ===

    // 作成日時
    const now = new Date();
    const formattedDate = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy/MM/dd HH:mm:ss');
    setValueIfCellExists(sheet, 'A1', `作成日時: ${formattedDate}`); // 例：A1セルに作成日時

    // シミュレーションタイプ
    setValueIfCellExists(sheet, 'A2', `タイプ: ${type === 'individual' ? '個別' : '合算'}`);

    // 物件情報（参照用）
    if (type === 'individual' && properties.length > 0) {
      setValueIfCellExists(sheet, 'A3', `物件ID: ${properties[0].id}`); // 例：A3セルに物件ID
    } else if (type === 'combined') {
      setValueIfCellExists(sheet, 'A3', `対象物件数: ${properties.length}件`); // 例：A3セルに物件数
    }

  } catch (error) {
    console.error('データ設定エラー:', error.message);
    throw new Error('シートへのデータ設定に失敗しました: ' + error.message);
  }
}

/**
 * セルが存在する場合のみ値を設定するヘルパー関数
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象シート
 * @param {string} cellAddress セルアドレス（例：'A1'）
 * @param {any} value 設定する値
 */
function setValueIfCellExists(sheet, cellAddress, value) {
  try {
    if (value !== null && value !== undefined) {
      sheet.getRange(cellAddress).setValue(value);
    }
  } catch (error) {
    // セルが存在しない場合は警告のみ出力（エラーにしない）
    console.warn(`セル ${cellAddress} への値設定に失敗: ${error.message}`);
  }
}

/**
 * 列番号をアルファベットに変換するヘルパー関数
 * @param {number} columnNumber 列番号（1=A, 2=B, 3=C, ...）
 * @return {string} アルファベット表記の列名
 */
function getColumnLetter(columnNumber) {
  let columnName = '';
  while (columnNumber > 0) {
    columnNumber--;
    columnName = String.fromCharCode(65 + (columnNumber % 26)) + columnName;
    columnNumber = Math.floor(columnNumber / 26);
  }
  return columnName;
}



/**
 * 利用可能な物件一覧を取得して選択ダイアログを表示する（個別用）
 * 選択された物件で個別シミュレーションを実行する
 */
function showLongTermPropertySelectionDialog() {
  try {
    // 長期物件（取得済み）の一覧を取得
    const properties = fetchLongTermProperties();

    if (properties.length === 0) {
      SpreadsheetApp.getUi().alert(
        '物件データなし',
        '長期物件購入シミュレーション対象の物件（取得済み）がありません。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }

    // 物件選択肢を作成
    const propertyOptions = properties.map(property =>
      `${property.property_name || '名称未設定'} (${property.address || '住所未設定'})`
    );

    // 選択ダイアログを表示（簡易版）
    const ui = SpreadsheetApp.getUi();
    const response = ui.alert(
      '物件選択',
      `長期物件購入シミュレーション（個別）対象の物件を選択してください。\n\n利用可能な物件数: ${properties.length}件\n\n最初の物件でシミュレーションタブを作成しますか？\n\n${propertyOptions[0]}`,
      ui.ButtonSet.YES_NO
    );

    if (response === ui.Button.YES) {
      // 最初の物件で個別シミュレーションを実行
      createLongTermPropertySimulationIndividual(properties[0].id);
    }

  } catch (error) {
    SpreadsheetApp.getUi().alert(
      'エラー',
      '物件選択ダイアログの表示に失敗しました。\n\nエラー詳細:\n' + error.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * 長期物件（取得済み）の一覧を取得する
 * @return {Object[]} 長期物件の配列
 */
function fetchLongTermProperties() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');

    if (!accessToken || !clientId) {
      throw new Error('認証情報が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // 長期物件（取得済み）のデータを取得
    const filter = `client_id=eq.${clientId}&status=eq.取得済み&deleted_at=is.null&order=property_name.asc`;
    const url = `${supabaseUrl}/rest/v1/properties?select=*&${filter}`;

    const res = UrlFetchApp.fetch(url, {
      method: 'get',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('物件データ取得エラー: ' + (err.message || JSON.stringify(err)));
    }

    return JSON.parse(res.getContentText());

  } catch (error) {
    throw error;
  }
}

/**
 * 物件データを計算・合算する
 * @param {Object[]} properties 物件データ配列
 * @param {string} type シミュレーションタイプ（'individual' または 'combined'）
 * @return {Object} 計算済みの物件データ
 */
function calculatePropertyData(properties, type) {
  let data = {
    // 基本合算データ
    totalCurrentRental: 0,
    totalFullRental: 0,
    totalAcquisitionPrice: 0,
    totalLoanAmount: 0,

    // 運営費用データ
    totalManagementFee: 0,
    totalRepairCost: 0,
    totalPropertyTax: 0,
    totalUtilityCost: 0,
    totalFireInsurance: 0,
    totalDepreciation: 0,
    totalOtherExpenses: 0,

    // 収支データ
    totalFullBalance: 0,
    totalCurrentBalance: 0,
    totalAnnualPrincipalPayment: 0
  };

  if (type === 'individual') {
    // 個別の場合：単一物件のデータを使用
    const property = properties[0];
    data.totalCurrentRental = property.current_rental_income_long || 0;
    data.totalFullRental = property.full_rental_income_long || 0;
    data.totalAcquisitionPrice = property.acquisition_price || 0;
    data.totalLoanAmount = property.loan_amount || 0;

    // 運営費用
    data.totalManagementFee = property.management_fee_long || 0;
    data.totalRepairCost = property.repair_cost_long || 0;
    data.totalPropertyTax = property.property_tax_long || 0;
    data.totalUtilityCost = property.utility_cost_long || 0;
    data.totalFireInsurance = property.fire_insurance_long || 0;
    data.totalDepreciation = property.depreciation_long || 0;
    data.totalOtherExpenses = property.other_expenses_long || 0;

    // 収支データ
    data.totalFullBalance = property.full_balance_long || 0;
    data.totalCurrentBalance = property.current_balance_long || 0;
    data.totalAnnualPrincipalPayment = property.annual_principal_payment_long || 0;

  } else if (type === 'combined') {
    // 合算の場合：全物件のデータを合計
    properties.forEach(property => {
      data.totalCurrentRental += property.current_rental_income_long || 0;
      data.totalFullRental += property.full_rental_income_long || 0;
      data.totalAcquisitionPrice += property.acquisition_price || 0;
      data.totalLoanAmount += property.loan_amount || 0;

      // 運営費用
      data.totalManagementFee += property.management_fee_long || 0;
      data.totalRepairCost += property.repair_cost_long || 0;
      data.totalPropertyTax += property.property_tax_long || 0;
      data.totalUtilityCost += property.utility_cost_long || 0;
      data.totalFireInsurance += property.fire_insurance_long || 0;
      data.totalDepreciation += property.depreciation_long || 0;
      data.totalOtherExpenses += property.other_expenses_long || 0;

      // 収支データ
      data.totalFullBalance += property.full_balance_long || 0;
      data.totalCurrentBalance += property.current_balance_long || 0;
      data.totalAnnualPrincipalPayment += property.annual_principal_payment_long || 0;
    });
  }

  return data;
}

/**
 * 指定された行に30年分のデータを設定する
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象シート
 * @param {Object} propertyData 計算済みの物件データ
 * @param {number} startColumn 開始列（C列=3）
 * @param {number} endColumn 終了列（AG列=33）
 */
function setYearlyDataToSheet(sheet, propertyData, startColumn, endColumn) {
  // データ設定マッピング（行番号 => データ値）
  const dataMapping = {
    4: propertyData.totalCurrentRental,          // 年間賃料（現状）
    5: propertyData.totalFullRental,             // 年間賃料（満室想定）
    8: propertyData.totalManagementFee,          // 管理費
    9: propertyData.totalRepairCost,             // 修繕費
    10: propertyData.totalPropertyTax,           // 固定資産税
    11: propertyData.totalUtilityCost,           // 水道光熱費
    12: propertyData.totalFireInsurance,         // 火災保険料
    13: propertyData.totalDepreciation,          // 減価償却費
    14: propertyData.totalOtherExpenses,         // その他の費用
    16: propertyData.totalFullBalance,           // 年間物件収支（満室想定）
    17: propertyData.totalCurrentBalance,        // 年間物件収支（現状）
    18: propertyData.totalAnnualPrincipalPayment // ローン元本返済額
  };

  // 各行に30年分のデータを設定
  Object.keys(dataMapping).forEach(row => {
    const value = dataMapping[row];
    // 0の値でも設定する（null、undefinedの場合は除く）
    if (value !== null && value !== undefined) {
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = getColumnLetter(col) + row;
        setValueIfCellExists(sheet, cellAddress, value);
      }
    }
  });
}