/**
 * ISO形式の日時文字列をY-m-d H:i:s形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d H:i:s形式の日時文字列
 */
function formatDateTime(isoString) {
    if (!isoString) return '';
    
    try {
        const date = new Date(isoString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        return isoString; // 変換に失敗した場合は元の値を返す
    }
}

/**
 * ユーザー一覧を取得（profilesテーブルとauth.usersテーブルを結合）
 * @param {string=} clientName 部分検索するクライアント名。省略時は絞り込みなし
 * @param {string=} userName 部分検索するユーザー名。省略時は絞り込みなし
 * @param {string=} userEmail 部分検索するメールアドレス。省略時は絞り込みなし
 * @param {number=} role ロール（1: 管理者, 2: クライアント）。省略時は絞り込みなし
 * @return {Object[]} Supabase から返ってきたユーザー配列
 */
function fetchUsers(clientName, userName, userEmail, role) {
    try {
        const docProps = PropertiesService.getDocumentProperties();
        const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
        
        if (!accessToken) {
            const errorMsg = 'SUPABASE_ACCESS_TOKEN が設定されていません。';
            SpreadsheetApp.getUi().alert(
                '設定エラー',
                errorMsg + '\n\n管理者にお問い合わせください。',
                SpreadsheetApp.getUi().ButtonSet.OK
            );
            throw new Error(errorMsg);
        }

        const scriptProps = PropertiesService.getScriptProperties();
        const supabaseUrl = scriptProps.getProperty('API_URL');
        const apiKey = scriptProps.getProperty('API_KEY');

        // PostgreSQL RPC関数を使用してユーザー情報を取得
        const payload = {
            search_user_name: userName || null,
            search_user_email: userEmail || null,
            search_role_filter: role || null,
            search_client_name: clientName || null
        };

        const url = `${supabaseUrl}/rest/v1/rpc/get_users_with_profiles`;
        const res = UrlFetchApp.fetch(url, {
            method: 'POST',
            contentType: 'application/json',
            headers: {
                apikey: apiKey,
                Authorization: 'Bearer ' + accessToken
            },
            payload: JSON.stringify(payload),
            muteHttpExceptions: true
        });

        if (res.getResponseCode() !== 200) {
            const err = JSON.parse(res.getContentText());
            const errorMsg = 'ユーザー取得エラー: ' + (err.message || JSON.stringify(err));
            SpreadsheetApp.getUi().alert(
                'ユーザーデータ取得エラー',
                'Supabaseからのユーザーデータ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
                SpreadsheetApp.getUi().ButtonSet.OK
            );
            throw new Error(errorMsg);
        }
        return JSON.parse(res.getContentText());
        
    } catch (error) {
        // 既にダイアログ表示済みでない場合のみ表示
        if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('ユーザー取得エラー')) {
            SpreadsheetApp.getUi().alert(
                '予期しないエラー',
                'ユーザーデータの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
                SpreadsheetApp.getUi().ButtonSet.OK
            );
        }
        throw error;
    }
}

/**
 * 検索ボタンに割り当てる関数。
 * ユーザー名、メールアドレス、ロールを検索条件として結果をシートに出力します
 */
function searchUsers() {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
    const clientName = sheet.getRange('D4').getDisplayValue().trim();
    const userName = sheet.getRange('D5').getDisplayValue().trim();
    const userEmail = sheet.getRange('D6').getDisplayValue().trim();
    
    // プルダウンの値を数値に変換
    const roleValue = sheet.getRange('D7').getDisplayValue().trim();
    let role = null;
    if (roleValue === '管理者') {
        role = 1;
    } else if (roleValue === 'クライアント') {
        role = 2;
    }
    // 空の場合は null のまま
    
    const startRow = 17;
    const idCol = 1;   // A 列
    const dataStartCol = 3;  // C 列
    const numCols = 5;  // C〜G の列数
    const lastRow = sheet.getMaxRows();

    try {
        const users = fetchUsers(clientName, userName, userEmail, role);

        // ─── クリア ───
        sheet.getRange(startRow, idCol, lastRow - (startRow - 1), 1).clearContent();
        sheet.getRange(startRow, dataStartCol, lastRow - (startRow - 1), numCols).clearContent();

        if (users.length === 0) {
            sheet.getRange(startRow, dataStartCol).setValue('該当なし');
            return;
        }

        // ─── 出力用配列を作成 ───
        // A列用：profile_id
        const ids = users.map(u => [u.profile_id]);

        // C〜H列用：各フィールド（ユーザー名、メールアドレス、ロール、クライアント名、作成日時、更新日時）
        const values = users.map(u => [
            u.user_name || '',
            u.user_email || '',
            u.role === 1 ? '管理者' : 'クライアント',
            u.remarks,
            u.client_name || '管理者',
        ]);

        // ─── 一括書き込み ───
        sheet
            .getRange(startRow, idCol, ids.length, 1)
            .setValues(ids);

        sheet
            .getRange(startRow, dataStartCol, values.length, numCols)
            .setValues(values);

    } catch (e) {
        // エラーダイアログを表示
        SpreadsheetApp.getUi().alert(
            'ユーザー検索エラー',
            'ユーザーデータの取得に失敗しました。\n\nエラー詳細:\n' + e.message,
            SpreadsheetApp.getUi().ButtonSet.OK
        );
        
        // シートにもエラー表示
        sheet.getRange(startRow, dataStartCol).setValue('❌ エラー: ' + e.message);
    }
}

/**
 * 登録モーダルを表示
 */
function createModalUser() {
    const html = HtmlService
        .createHtmlOutputFromFile('CreateModalUser')
        .setWidth(980)   // 横幅
        .setHeight(800); // 高さ

    SpreadsheetApp
        .getUi()
        .showModalDialog(html, 'ユーザー登録モーダル');
}

/**
 * HTML から呼び出される登録関数
 * @param {Object} formData 登録フォームのデータ
 */
function registerUser(formData) {
    const scriptProps = PropertiesService.getScriptProperties();
    // service_role キーを API_KEY として正しく設定していることを確認
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const serviceRoleKey = scriptProps.getProperty('SERVICE_ROLE_KEY'); // <-- ここには Service Role Key が設定されているべき

    // docProps.getProperty('SUPABASE_ACCESS_TOKEN') は管理者ユーザー登録では不要。
    // profiles テーブルへの挿入も serviceRoleKey で行うのが適切。
    // const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN'); // 不要または別の用途

    if (!serviceRoleKey) {
        return { success: false, message: 'Service Role Keyが未設定' };
    }

    try {
        // 1. auth.usersテーブルにユーザーを作成（service_role キーを使用）
        const authPayload = {
            email: formData.email,
            password: formData.password,
            email_confirm: true // メール確認をスキップ
        };

        const authRes = UrlFetchApp.fetch(
            `${supabaseUrl}/auth/v1/admin/users`,
            {
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    apikey: serviceRoleKey, // <<-- Service Role Keyを使用
                    Authorization: 'Bearer ' + serviceRoleKey // <<-- Service Role Keyを使用
                },
                payload: JSON.stringify(authPayload),
                muteHttpExceptions: true
            }
        );

        const authCode = authRes.getResponseCode();
        const authResult = JSON.parse(authRes.getContentText());

        if (authCode !== 200 && authCode !== 201) { // 成功は200または201
            // エラーの詳細をログに記録
            Logger.log('Auth API Response: ' + authRes.getContentText());
            return { success: false, message: 'ユーザー作成エラー: ' + (authResult.message || authResult.msg || JSON.stringify(authResult)) };
        }

        // authResult.id は Supabase の Auth User の UUID です
        // authResult.user.id の形式で返ってくる場合もあるので確認
        const newUserId = authResult.id || authResult.user.id;

        if (!newUserId) {
            return { success: false, message: 'ユーザーIDが取得できませんでした。Auth APIのレスポンスを確認してください。' };
        }

        // 2. profilesテーブルにプロフィールを作成
        // profilesテーブルへの挿入も Service Role Key で行い、RLS をバイパスするのが最も簡単
        const profilePayload = {
            id: newUserId, // auth.users の ID と profiles の ID を紐付け
            name: formData.name,
            client_id: formData.client_id || null,
            role: formData.role || 2
        };

        const profileRes = UrlFetchApp.fetch(
            `${supabaseUrl}/rest/v1/profiles`,
            {
                method: 'POST',
                contentType: 'application/json',
                headers: {
                    apikey: serviceRoleKey, // <<-- Service Role Keyを使用
                    Authorization: 'Bearer ' + serviceRoleKey, // <<-- Service Role Keyを使用
                    Prefer: 'return=representation'
                },
                payload: JSON.stringify(profilePayload),
                muteHttpExceptions: true
            }
        );

        const profileCode = profileRes.getResponseCode();
        const profileResult = JSON.parse(profileRes.getContentText());

        if (profileCode === 201) {
            // 検索結果を更新 (もしsearchUsers()が同じスクリプト内にあるなら)
            if (typeof searchUsers === 'function') {
                searchUsers();
            }
            return { success: true, id: newUserId };
        }

        Logger.log('Profiles API Response: ' + profileRes.getContentText());
        return { success: false, message: 'プロフィール作成エラー: ' + (profileResult.message || profileResult.details || JSON.stringify(profileResult)) };

    } catch (error) {
        // 例外発生時のログとエラーメッセージ
        Logger.log('処理中にエラーが発生しました: ' + error.message);
        return { success: false, message: '処理中にエラーが発生しました: ' + error.message };
    }
}

/**
 * 更新モーダルを表示
 */
function updateModalUser() {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
    const activeRange = sheet.getActiveRange();
    const selectedRow = activeRange.getRow();
    
    // A列からプロフィールIDを取得
    const profileId = sheet.getRange(selectedRow, 1).getValue();
    
    if (!profileId) {
        SpreadsheetApp.getUi().alert(
            '選択エラー',
            '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
            SpreadsheetApp.getUi().ButtonSet.OK
        );
        return;
    }
    
    try {
        // Supabaseからデータを取得
        const userData = fetchUserById(profileId);
        
        if (!userData) {
            SpreadsheetApp.getUi().alert(
                'ユーザーが見つかりません',
                '指定されたIDのユーザーが見つかりません。\n\nプロフィールID: ' + profileId,
                SpreadsheetApp.getUi().ButtonSet.OK
            );
            return;
        }
        
        // 取得したデータを一時保存
        const docProps = PropertiesService.getDocumentProperties();
        docProps.setProperty('TEMP_USER_DATA', JSON.stringify(userData));
        
        const html = HtmlService
            .createHtmlOutputFromFile('UpdateModalUser')
            .setWidth(980)   // 横幅
            .setHeight(800); // 高さ

        SpreadsheetApp
            .getUi()
            .showModalDialog(html, 'ユーザー編集モーダル');
            
    } catch (error) {
        SpreadsheetApp.getUi().alert(
            'ユーザーデータ取得エラー',
            'ユーザーデータの取得に失敗しました。\n\nエラー詳細:\n' + error.message + '\n\nプロフィールID: ' + profileId,
            SpreadsheetApp.getUi().ButtonSet.OK
        );
    }
}

/**
 * IDで特定のユーザーデータを取得
 * @param {string} profileId プロフィールID
 * @return {Object} ユーザーデータ
 */
function fetchUserById(profileId) {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
        throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // PostgreSQL RPC関数を使用してユーザー情報を取得
    const payload = {
        p_profile_id: profileId
    };

    const url = `${supabaseUrl}/rest/v1/rpc/get_user_by_profile_id`;
    const res = UrlFetchApp.fetch(url, {
        method: 'POST',
        contentType: 'application/json',
        headers: {
            apikey: apiKey,
            Authorization: 'Bearer ' + accessToken
        },
        payload: JSON.stringify(payload),
        muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
        const err = JSON.parse(res.getContentText());
        throw new Error('ユーザーデータ取得エラー: ' + (err.message || JSON.stringify(err)));
    }
    
    const result = JSON.parse(res.getContentText());
    return result.length > 0 ? result[0] : null;
}

/**
 * HTML側から呼び出される関数：一時保存されたユーザーデータを取得
 * @return {Object} ユーザーデータ
 */
function getTempUserData() {
    const docProps = PropertiesService.getDocumentProperties();
    const dataStr = docProps.getProperty('TEMP_USER_DATA');
    
    if (!dataStr) {
        return null;
    }
    
    // 一時データを削除
    docProps.deleteProperty('TEMP_USER_DATA');
    
    return JSON.parse(dataStr);
}

/**
 * 全てのクライアントを取得（セレクトボックス用）
 * @return {Object[]} クライアント配列
 */
function fetchAllClients() {
    try {
        const docProps = PropertiesService.getDocumentProperties();
        const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
        
        if (!accessToken) {
            throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
        }

        const scriptProps = PropertiesService.getScriptProperties();
        const supabaseUrl = scriptProps.getProperty('API_URL');
        const apiKey = scriptProps.getProperty('API_KEY');

        // clientsテーブルから全てのクライアントを取得
        const url = `${supabaseUrl}/rest/v1/clients?select=id,name&order=name.asc`;
        const res = UrlFetchApp.fetch(url, {
            method: 'GET',
            headers: {
                apikey: apiKey,
                Authorization: 'Bearer ' + accessToken
            },
            muteHttpExceptions: true
        });

        if (res.getResponseCode() !== 200) {
            const err = JSON.parse(res.getContentText());
            throw new Error('クライアント取得エラー: ' + (err.message || JSON.stringify(err)));
        }
        
        return JSON.parse(res.getContentText());
        
    } catch (error) {
        throw error;
    }
}

/**
 * HTML から呼び出される更新関数
 * @param {Object} formData 更新フォームのデータ
 */
function updateUser(formData) {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
        return { success: false, message: 'トークンが未設定' };
    }
    
    if (!formData.id) {
        return { success: false, message: 'プロフィールIDが指定されていません' };
    }
    
    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');
    const serviceRoleKey = scriptProps.getProperty('SERVICE_ROLE_KEY'); // Service Role Key を取得

    if (!serviceRoleKey) {
        return { success: false, message: 'Service Role Keyが未設定' };
    }

    try {
        // 1. auth.usersテーブルのユーザー情報を更新（SERVICE_ROLE_KEYを使用）
        if (formData.user_id && formData.password) {
            const authPayload = {
                password: formData.password
            };

            const authRes = UrlFetchApp.fetch(
                `${supabaseUrl}/auth/v1/admin/users/${formData.user_id}`,
                {
                    method: 'PUT',
                    contentType: 'application/json',
                    headers: {
                        apikey: serviceRoleKey, // Service Role Key を使用
                        Authorization: 'Bearer ' + serviceRoleKey // Service Role Key を使用
                    },
                    payload: JSON.stringify(authPayload),
                    muteHttpExceptions: true
                }
            );

            const authCode = authRes.getResponseCode();
            const authResult = JSON.parse(authRes.getContentText());

            if (authCode !== 200) {
                Logger.log('Auth update error - Code: ' + authCode + ', Response: ' + authRes.getContentText());
                return { success: false, message: 'ユーザー更新エラー: ' + (authResult.message || JSON.stringify(authResult)) };
            }
        }

        // 2. profilesテーブルのプロフィール情報を更新（SERVICE_ROLE_KEYを使用）
        const profilePayload = {
            name: formData.name,
            client_id: formData.client_id || null,
            role: formData.role || 2,
            updated_at: new Date().toISOString()
        };

        const profileRes = UrlFetchApp.fetch(
            `${supabaseUrl}/rest/v1/profiles?id=eq.${formData.id}`,
            {
                method: 'PATCH',
                contentType: 'application/json',
                headers: {
                    apikey: serviceRoleKey, // Service Role Key を使用
                    Authorization: 'Bearer ' + serviceRoleKey, // Service Role Key を使用
                    Prefer: 'return=representation'
                },
                payload: JSON.stringify(profilePayload),
                muteHttpExceptions: true
            }
        );

        const profileCode = profileRes.getResponseCode();
        const profileResult = JSON.parse(profileRes.getContentText());

        if (profileCode === 200) {
            // 検索結果を更新
            searchUsers();
            return { success: true, id: formData.id };
        }

        // エラーの詳細をログに記録
        Logger.log('Profile update error - Code: ' + profileCode + ', Response: ' + profileRes.getContentText());
        Logger.log('Profile update payload: ' + JSON.stringify(profilePayload));
        Logger.log('Profile update URL: ' + `${supabaseUrl}/rest/v1/profiles?id=eq.${formData.id}`);
        
        return { 
            success: false, 
            message: 'プロフィール更新エラー (Code: ' + profileCode + '): ' + (profileResult.message || profileResult.details || JSON.stringify(profileResult)),
            debug: {
                code: profileCode,
                response: profileResult,
                payload: profilePayload,
                url: `${supabaseUrl}/rest/v1/profiles?id=eq.${formData.id}`
            }
        };

    } catch (error) {
        return { success: false, message: '処理中にエラーが発生しました: ' + error.message };
    }
}

/**
 * 選択セル行のA列IDを取得してユーザーを物理削除する関数
 */
function deleteSelectedUser() {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
    const activeRange = sheet.getActiveRange();
    const selectedRow = activeRange.getRow();
    
    // A列からプロフィールIDを取得
    const profileId = sheet.getRange(selectedRow, 1).getValue();
    
    if (!profileId) {
        SpreadsheetApp.getUi().alert(
            '選択エラー',
            '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
            SpreadsheetApp.getUi().ButtonSet.OK
        );
        return;
    }
    
    // 削除確認ダイアログ
    const ui = SpreadsheetApp.getUi();
    const response = ui.alert(
        'ユーザー削除の確認',
        '選択されたユーザーを削除してもよろしいですか？\n\nプロフィールID: ' + profileId + '\n\n※この操作は物理削除のため、復元できません。',
        ui.ButtonSet.YES_NO
    );
    
    if (response !== ui.Button.YES) {
        return; // ユーザーがキャンセルした場合
    }
    
    try {
        const result = deleteUser(profileId);
        
        if (result.success) {
            ui.alert(
                '削除完了',
                'ユーザーの削除が完了しました。\n\nプロフィールID: ' + profileId,
                ui.ButtonSet.OK
            );
            
            // 削除後に検索結果を更新
            searchUsers();
        } else {
            ui.alert(
                '削除エラー',
                'ユーザーの削除に失敗しました。\n\nエラー詳細:\n' + result.message + '\n\nプロフィールID: ' + profileId,
                ui.ButtonSet.OK
            );
        }
        
    } catch (error) {
        ui.alert(
            '削除エラー',
            'ユーザーの削除中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message + '\n\nプロフィールID: ' + profileId,
            ui.ButtonSet.OK
        );
    }
}

/**
 * auth.usersテーブルとprofilesテーブルからユーザーを物理削除する
 * @param {string} profileId 削除するユーザーのプロフィールID
 * @return {Object} 削除結果
 */
function deleteUser(profileId) {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
        return { success: false, message: 'SUPABASE_ACCESS_TOKEN が設定されていません。' };
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');
    const serviceRoleKey = scriptProps.getProperty('SERVICE_ROLE_KEY');

    if (!serviceRoleKey) {
        return { success: false, message: 'SERVICE_ROLE_KEY が設定されていません。物理削除には管理者権限が必要です。' };
    }

    try {
        // まず、ユーザーデータを取得してuser_idを確認
        const userData = fetchUserById(profileId);
        
        if (!userData) {
            return { success: false, message: '指定されたプロフィールIDのユーザーが見つかりません。' };
        }

        const userId = userData.user_id;

        // 1. profilesテーブルから物理削除
        const profileRes = UrlFetchApp.fetch(
            `${supabaseUrl}/rest/v1/profiles?id=eq.${profileId}`,
            {
                method: 'DELETE',
                headers: {
                    apikey: serviceRoleKey,
                    Authorization: `Bearer ${serviceRoleKey}`
                },
                muteHttpExceptions: true
            }
        );

        const profileCode = profileRes.getResponseCode();
        
        if (profileCode !== 204) {
            const profileResult = JSON.parse(profileRes.getContentText());
            Logger.log('Profile delete error - Code: ' + profileCode + ', Response: ' + profileRes.getContentText());
            return { 
                success: false, 
                message: 'プロフィール削除エラー (Code: ' + profileCode + '): ' + (profileResult.message || JSON.stringify(profileResult))
            };
        }

        // 2. auth.usersテーブルから物理削除
        if (userId) {
            const authRes = UrlFetchApp.fetch(
                `${supabaseUrl}/auth/v1/admin/users/${userId}`,
                {
                    method: 'DELETE',
                    headers: {
                        apikey: serviceRoleKey,
                        Authorization: `Bearer ${serviceRoleKey}`
                    },
                    muteHttpExceptions: true
                }
            );

            const authCode = authRes.getResponseCode();
            
            if (authCode !== 200) {
                const authResult = JSON.parse(authRes.getContentText());
                Logger.log('Auth delete error - Code: ' + authCode + ', Response: ' + authRes.getContentText());
                return { 
                    success: false, 
                    message: 'ユーザー削除エラー (Code: ' + authCode + '): ' + (authResult.message || JSON.stringify(authResult))
                };
            }
        }

        return { success: true };

    } catch (error) {
        Logger.log('Delete user error: ' + error.message);
        return { success: false, message: '削除処理中にエラーが発生しました: ' + error.message };
    }
}
