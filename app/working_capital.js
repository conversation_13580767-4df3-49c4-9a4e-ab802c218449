/**
 * ISO形式の日時文字列をY-m-d H:i:s形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d H:i:s形式の日時文字列
 */
function formatDateTime(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * ISO形式の日時文字列をY-m-d形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d形式の日付文字列
 */
function formatDate(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * 数値を金額形式でフォーマット（カンマ区切り）
 * @param {number|string} amount 金額
 * @return {string} フォーマット済み金額文字列
 */
function formatAmount(amount) {
  if (!amount || amount === 0) return '0';
  
  try {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return num.toLocaleString('ja-JP');
  } catch (error) {
    return amount.toString();
  }
}

/**
 * 運転資金一覧を取得（working_capitalテーブルとclientsテーブルを結合）
 * @param {string=} bankName 部分検索する銀行名。省略時は絞り込みなし
 * @param {string=} clientName 部分検索するクライアント名。省略時は絞り込みなし
 * @return {Object[]} Supabase から返ってきた運転資金配列
 */
function fetchMyWorkingCapital(bankName, clientName) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken) {
      const errorMsg = 'SUPABASE_ACCESS_TOKEN が設定されていません。';
      SpreadsheetApp.getUi().alert(
        '設定エラー',
        errorMsg + '\n\n管理者にお問い合わせください。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // PostgreSQL RPC関数を使用して運転資金情報を取得
    const payload = {
      search_bank_name: bankName || null,
      search_client_name: clientName || null,
      filter_client_id: (clientId && clientId.trim() !== '') ? clientId : null
    };

    const url = `${supabaseUrl}/rest/v1/rpc/get_working_capital_with_clients`;
    const res = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      const errorMsg = '運転資金取得エラー: ' + (err.message || JSON.stringify(err));
      SpreadsheetApp.getUi().alert(
        '運転資金データ取得エラー',
        'Supabaseからの運転資金データ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    // 既にダイアログ表示済みでない場合のみ表示
    if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('運転資金取得エラー')) {
      SpreadsheetApp.getUi().alert(
        '予期しないエラー',
        '運転資金データの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
    throw error;
  }
}

/**
 * 検索ボタンに割り当てる関数。
 * D4セルの銀行名、D5セルのクライアント名で検索し、結果をシートに出力します
 */
function searchWorkingCapital() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const bankName = sheet.getRange('D4').getDisplayValue().trim();    // D4セルから銀行名取得
  const clientName = sheet.getRange('D5').getDisplayValue().trim();  // D5セルからクライアント名取得
  const startRow = 15;
  const idCol = 1;   // A 列
  const dataStartCol = 3;  // C 列
  const numCols = 9;  // C〜K の列数（一覧表示項目+クライアント名）
  const lastRow = sheet.getMaxRows();

  try {
    const workingCapitalList = fetchMyWorkingCapital(bankName, clientName);  // 銀行名・クライアント名で検索

    // ─── クリア ───
    sheet.getRange(startRow, idCol, lastRow - (startRow - 1), 1).clearContent();
    sheet.getRange(startRow, dataStartCol, lastRow - (startRow - 1), numCols).clearContent();

    if (workingCapitalList.length === 0) {
      sheet.getRange(startRow, dataStartCol).setValue('該当なし');
      return;
    }

    // ─── 出力用配列を作成 ───
    // A列用：id
    const ids = workingCapitalList.map(wc => [wc.id]);

    // C〜K列用：一覧表示項目（銀行名、当初借入金額、現在借入残高、元本返済額、借入日、返済期日、借入形態、借入金利、クライアント名）
    const values = workingCapitalList.map(wc => [
      wc.bank_name || '',                          // 銀行名
      formatAmount(wc.initial_loan_amount),        // 当初借入金額
      formatAmount(wc.current_loan_balance),       // 現在借入残高
      formatAmount(wc.principal_repayment),        // 元本返済額
      formatDate(wc.loan_date),                    // 借入日
      formatDate(wc.repayment_due_date),           // 返済期日
      wc.loan_type || '',                          // 借入形態
      wc.loan_interest_rate || '',                 // 借入金利
      wc.client_name || ''                         // クライアント名（RPC関数の戻り値から直接取得）
    ]);

    // ─── 一括書き込み ───
    sheet
      .getRange(startRow, idCol, ids.length, 1)
      .setValues(ids);

    sheet
      .getRange(startRow, dataStartCol, values.length, numCols)
      .setValues(values);

  } catch (e) {
    // エラーダイアログを表示
    SpreadsheetApp.getUi().alert(
      '運転資金検索エラー',
      '運転資金データの取得に失敗しました。\n\nエラー詳細:\n' + e.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    
    // シートにもエラー表示
    sheet.getRange(startRow, dataStartCol).setValue('❌ エラー: ' + e.message);
  }
}

// 登録モーダルを表示
function createModalWorkingCapital() {
  const html = HtmlService
    .createHtmlOutputFromFile('CreateModalWorkingCapital')
    .setWidth(980)   // 横幅
    .setHeight(800); // 高さ

  SpreadsheetApp
    .getUi()
    .showModalDialog(html, '運転資金登録モーダル');
}

/**
 * HTML から呼び出される登録関数
 * @param {Object} formData 登録フォームのデータ
 */
function registerWorkingCapital(formData) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'アクセストークンが未設定' };
  }
  
  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // ペイロード作成
  const payload = Object.assign({}, formData);
  
  // 管理者の場合はフォームから送られたclient_idを使用、クライアントの場合は自動設定
  if (clientId && clientId.trim() !== '') {
    // クライアントユーザーの場合：自動的にclient_idを設定
    payload.client_id = clientId;
  } else {
    // 管理者の場合：フォームからclient_idが送られていることを期待
    if (!formData.client_id) {
      return { success: false, message: '管理者権限での登録にはクライアントIDの指定が必要です' };
    }
  }
  
  // デバッグ用：ペイロードの内容をログ出力
  console.log('registerWorkingCapital - formData:', JSON.stringify(formData));
  console.log('registerWorkingCapital - payload:', JSON.stringify(payload));

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/working_capital`,
    {
      method: 'post',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: 'return=representation'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const result = JSON.parse(res.getContentText());
  
  if (code === 201 && Array.isArray(result) && result.length > 0) {
    // 検索結果を更新
    searchWorkingCapital();
    return { success: true, id: result[0].id };
  }
  
  return { success: false, message: result.message || JSON.stringify(result) };
}

// 更新モーダルを表示
function updateModalWorkingCapital() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const activeRange = sheet.getActiveRange();
  const selectedRow = activeRange.getRow();
  
  // A列からIDを取得
  const workingCapitalId = sheet.getRange(selectedRow, 1).getValue();
  
  if (!workingCapitalId) {
    SpreadsheetApp.getUi().alert(
      '選択エラー',
      '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return;
  }
  
  try {
    // Supabaseからデータを取得
    const workingCapitalData = fetchWorkingCapitalById(workingCapitalId);
    
    if (!workingCapitalData) {
      SpreadsheetApp.getUi().alert(
        '運転資金が見つかりません',
        '指定されたIDの運転資金が見つかりません。\n\n運転資金ID: ' + workingCapitalId,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }
    
    // 取得したデータを一時保存
    const docProps = PropertiesService.getDocumentProperties();
    docProps.setProperty('TEMP_WORKING_CAPITAL_DATA', JSON.stringify(workingCapitalData));
    
    const html = HtmlService
      .createHtmlOutputFromFile('UpdateModalWorkingCapital')
      .setWidth(980)   // 横幅
      .setHeight(800); // 高さ

    SpreadsheetApp
      .getUi()
      .showModalDialog(html, '運転資金編集モーダル');
      
  } catch (error) {
    SpreadsheetApp.getUi().alert(
      '運転資金データ取得エラー',
      '運転資金データの取得に失敗しました。\n\nエラー詳細:\n' + error.message + '\n\n運転資金ID: ' + workingCapitalId,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * IDで特定の運転資金データを取得
 * @param {string} workingCapitalId 運転資金ID
 * @return {Object} 運転資金データ
 */
function fetchWorkingCapitalById(workingCapitalId) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
  }

  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // PostgreSQL RPC関数を使用して運転資金情報を取得
  const payload = {
    p_working_capital_id: workingCapitalId,
    filter_client_id: (clientId && clientId.trim() !== '') ? clientId : null
  };

  const url = `${supabaseUrl}/rest/v1/rpc/get_working_capital_by_id`;
  const res = UrlFetchApp.fetch(url, {
    method: 'POST',
    contentType: 'application/json',
    headers: {
      apikey: apiKey,
      Authorization: 'Bearer ' + accessToken
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  });

  if (res.getResponseCode() !== 200) {
    const err = JSON.parse(res.getContentText());
    throw new Error('運転資金データ取得エラー: ' + (err.message || JSON.stringify(err)));
  }
  
  const result = JSON.parse(res.getContentText());
  return result.length > 0 ? result[0] : null;
}

/**
 * HTML側から呼び出される関数：一時保存された運転資金データを取得
 * @return {Object} 運転資金データ
 */
function getTempWorkingCapitalData() {
  const docProps = PropertiesService.getDocumentProperties();
  const dataStr = docProps.getProperty('TEMP_WORKING_CAPITAL_DATA');
  
  if (!dataStr) {
    return null;
  }
  
  // 一時データを削除
  docProps.deleteProperty('TEMP_WORKING_CAPITAL_DATA');
  
  return JSON.parse(dataStr);
}

/**
 * 全てのクライアントを取得（セレクトボックス用）
 * @return {Object[]} クライアント配列
 */
function fetchAllClients() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
      throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // clientsテーブルから全てのクライアントを取得
    const url = `${supabaseUrl}/rest/v1/clients?select=id,name&order=name.asc`;
    const res = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('クライアント取得エラー: ' + (err.message || JSON.stringify(err)));
    }
    
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    throw error;
  }
}

/**
 * HTML から呼び出される更新関数
 * @param {Object} formData 更新フォームのデータ
 */
function updateWorkingCapital(formData) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'アクセストークンが未設定' };
  }
  
  if (!formData.id) {
    return { success: false, message: '運転資金IDが指定されていません' };
  }
  
  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // ペイロード作成（idは除外）
  const payload = Object.assign({}, formData);
  delete payload.id; // idは更新対象から除外
  payload.updated_at = new Date().toISOString(); // 現在時刻を更新時刻として追加

  // 管理者の場合はclient_idでフィルタリングしない
  let filter = `id=eq.${formData.id}`;
  if (clientId && clientId.trim() !== '') {
    filter = `client_id=eq.${clientId}&${filter}`;
    payload.client_id = clientId; // クライアントユーザーの場合はclient_idを確実に設定
  }

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/working_capital?${filter}`,
    {
      method: 'PATCH',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: 'return=representation'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const result = JSON.parse(res.getContentText());
  
  if (code === 200 && Array.isArray(result) && result.length > 0) {
    // 検索結果を更新
    searchWorkingCapital();
    return { success: true, id: result[0].id };
  }

  return { success: false, message: result.message || JSON.stringify(result) };
}
