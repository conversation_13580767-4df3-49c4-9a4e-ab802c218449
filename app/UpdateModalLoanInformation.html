<!DOCTYPE html>
<html lang="ja">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s14{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s0{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.waffle tbody th{visibility: hidden;width: 1px;display: block;}</style>

    <style type="text/css">
        body .ritz .waffle td:has(input, select, textarea) {
            padding: 0 !important;
        }
        body .ritz .waffle input,
        body .ritz .waffle select,
        body .ritz .waffle textarea {
            min-height: 26px;
            display: flex;
            line-height: 1.5;
            box-sizing: border-box;
            padding: 0 8px;
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.15);
            background-color: #fff !important;
            text-align: left !important;
        }
        
        /* 見出し系のセルの背景色を変更 */
        body .ritz .waffle .s1,
        body .ritz .waffle .s3,
        body .ritz .waffle .s6,
        body .ritz .waffle .s9 {
            background-color: #efefef !important;
        }
        
        /* datalist用のスタイル調整 */
        input[list] {
            cursor: pointer;
        }
        
        /* disabled状態のスタイル */
        input:disabled {
            background-color: #f8f8f8 !important;
            color: #999 !important;
            cursor: not-allowed !important;
        }
        
        /* ボタンの統一スタイル */
        input[type="submit"], button[type="submit"], button {
            background-color: #0A4484;
            color: #FFFFFF;
            font-size: 14px;
            font-weight: normal;
            border-radius: 3px;
            border: none;
            padding: 8px 20px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.2s ease;
        }
        
        /* ホバー時のスタイル */
        input[type="submit"]:hover, button[type="submit"]:hover, button:hover {
            background-color: #083a6f;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        /* クリック時のスタイル */
        input[type="submit"]:active, button[type="submit"]:active, button:active {
            background-color: #062a52;
            box-shadow: 0 1px 4px rgba(0,0,0,0.3);
            transform: translateY(0);
        }
        
        /* ローディング表示用のスタイル */
        #formMessage {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }
        #formMessage::before {
            content: '';
            width: 16px;
            height: 16px;
            margin-bottom: 8px;
            border: 2px solid #CCC;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        #formMessage::after {
            content: '';
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.5);
            z-index: -1;
        }
        #formMessage:empty {
            display: none !important;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

    </style>
</head>
<body>
    <div class="ritz grid-container" dir="ltr">
        <form id="loanInformationForm" onsubmit="submitForm(); return false;">
            <input type="hidden" id="loan_information_id" name="loan_information_id">
        <table class="waffle" cellspacing="0" cellpadding="0">
            <thead>
                <tr>
                    <th class="row-header freezebar-origin-ltr"></th>
                    <th id="1836490454C0" style="width:200px;" class="column-headers-background"></th>
                    <th id="1836490454C1" style="width:600px;" class="column-headers-background"></th>

                </tr>
            </thead>
            <tbody>
                <tr style="height: 20px">
                    <th id="1836490454R0" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">1</div>
                    </th>
                    <td class="s1" dir="ltr" colspan="2">融資情報編集フォーム</td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R1" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">2</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px; display: none;" id="clientRow">
                    <th id="1836490454R2_0" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">3</div>
                    </th>
                    <td class="s6" dir="ltr">クライアント名<span style="color: red;">*</span></td>
                    <td class="s4" dir="ltr">
                        <select id="client_id" name="client_id" required style="width: 100%; border: none; background: transparent; text-align: center;">
                            <option value="">クライアントを選択してください</option>
                        </select>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R2" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">4</div>
                    </th>
                    <td class="s6" dir="ltr">銀行名称</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="bank_code" name="bank_code" list="bank_list" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="銀行名を入力または選択">
                        <datalist id="bank_list">
                        </datalist>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R3" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">5</div>
                    </th>
                    <td class="s6" dir="ltr">支店名称</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="branch_code" name="branch_code" list="branch_list" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="先に銀行を選択してください" disabled>
                        <datalist id="branch_list">
                        </datalist>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R4" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">6</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R5" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">7</div>
                    </th>
                    <td class="s9" dir="ltr">貸出金利</td>
                    <td class="s4" dir="ltr">
                        <input type="number" id="lending_rate" name="lending_rate" step="0.001" min="0" max="100" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="例: 2.500">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R6" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">8</div>
                    </th>
                    <td class="s9" dir="ltr">融資手数料</td>
                    <td class="s4" dir="ltr">
                        <input type="number" id="loan_fee" name="loan_fee" min="0" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="融資手数料を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R7" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">9</div>
                    </th>
                    <td class="s9" dir="ltr">融資枠</td>
                    <td class="s4" dir="ltr">
                        <input type="number" id="loan_limit" name="loan_limit" min="0" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="融資枠を入力">
                    </td>
                </tr>
                
                <!-- 融資詳細条件 -->
                <tr style="height: 20px">
                    <th id="1836490454R10" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">10</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R11" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">11</div>
                    </th>
                    <td class="s9" dir="ltr">総額いくらまで融資可能か</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="max_loan_amount" name="max_loan_amount" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="総額融資可能額を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R12" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">12</div>
                    </th>
                    <td class="s9" dir="ltr">借入残高合計</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="total_loan_balance" name="total_loan_balance" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="借入残高合計を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R13" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">13</div>
                    </th>
                    <td class="s9" dir="ltr">担保評価合計</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="total_collateral_value" name="total_collateral_value" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="担保評価合計を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R14" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">14</div>
                    </th>
                    <td class="s9" dir="ltr">銀行リスク額合計</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="total_bank_risk_amount" name="total_bank_risk_amount" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="銀行リスク額合計を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R15" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">15</div>
                    </th>
                    <td class="s9" dir="ltr">同時に何件融資可能か</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="max_concurrent_loans" name="max_concurrent_loans" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="同時融資可能件数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R16" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">16</div>
                    </th>
                    <td class="s9" dir="ltr">融資取組件数</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="loan_execution_count" name="loan_execution_count" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="融資取組件数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R17" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">17</div>
                    </th>
                    <td class="s9" dir="ltr">平均回答日数</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="avg_response_days" name="avg_response_days" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="平均回答日数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R18" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">18</div>
                    </th>
                    <td class="s9" dir="ltr">平均回答融資割合</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="avg_response_loan_ratio" name="avg_response_loan_ratio" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="平均回答融資割合を入力">
                    </td>
                </tr>
                
                <!-- 決裁権限情報 -->
                <tr style="height: 20px">
                    <th id="1836490454R19" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">19</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R20" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">20</div>
                    </th>
                    <td class="s9" dir="ltr">支店長権限額</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="branch_manager_limit" name="branch_manager_limit" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="支店長権限額を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R21" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">21</div>
                    </th>
                    <td class="s9" dir="ltr">担当役員の決裁権限</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="officer_approval_limit" name="officer_approval_limit" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="担当役員の決裁権限を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R22" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">22</div>
                    </th>
                    <td class="s9" dir="ltr">下限金利</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="minimum_interest_rate" name="minimum_interest_rate" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="下限金利を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R23" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">23</div>
                    </th>
                    <td class="s9" dir="ltr">下限手数料</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="minimum_fee" name="minimum_fee" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="下限手数料を入力">
                    </td>
                </tr>
                
                <!-- プロジェクト・物件条件 -->
                <tr style="height: 20px">
                    <th id="1836490454R24" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">24</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R25" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">25</div>
                    </th>
                    <td class="s9" dir="ltr">最長PJ期間</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="max_project_period" name="max_project_period" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="最長PJ期間を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R26" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">26</div>
                    </th>
                    <td class="s9" dir="ltr">木造耐用年数〇年</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="wooden_useful_life" name="wooden_useful_life" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="木造耐用年数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R27" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">27</div>
                    </th>
                    <td class="s9" dir="ltr">鉄骨耐用年数〇年</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="steel_useful_life" name="steel_useful_life" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="鉄骨耐用年数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R28" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">28</div>
                    </th>
                    <td class="s9" dir="ltr">RC耐用年数〇年</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="rc_useful_life" name="rc_useful_life" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="RC耐用年数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R29" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">29</div>
                    </th>
                    <td class="s9" dir="ltr">エリア</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="target_area" name="target_area" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="対象エリアを入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R30" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">30</div>
                    </th>
                    <td class="s9" dir="ltr">自己資金何％必要</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="required_equity_ratio" name="required_equity_ratio" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="必要自己資金割合を入力">
                    </td>
                </tr>
                
                <!-- 物件条件フラグ -->
                <tr style="height: 20px">
                    <th id="1836490454R31" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">31</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R32" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">32</div>
                    </th>
                    <td class="s9" dir="ltr">検査済証なし</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="no_inspection_cert" name="no_inspection_cert" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="検査済証なしの状況を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R33" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">33</div>
                    </th>
                    <td class="s9" dir="ltr">旧耐震</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="old_earthquake_standard" name="old_earthquake_standard" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="旧耐震の状況を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R34" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">34</div>
                    </th>
                    <td class="s9" dir="ltr">容積率オーバー</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="floor_area_ratio_over" name="floor_area_ratio_over" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="容積率オーバーを入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R35" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">35</div>
                    </th>
                    <td class="s9" dir="ltr">建蔽率オーバー</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="building_coverage_over" name="building_coverage_over" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="建蔽率オーバーを入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R36" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">36</div>
                    </th>
                    <td class="s9" dir="ltr">底地</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="land_only" name="land_only" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="底地を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R37" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">37</div>
                    </th>
                    <td class="s9" dir="ltr">借地</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="leased_land" name="leased_land" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="借地を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R38" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">38</div>
                    </th>
                    <td class="s9" dir="ltr">立ち退き</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="eviction_case" name="eviction_case" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="立ち退きを入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R39" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">39</div>
                    </th>
                    <td class="s9" dir="ltr">3為</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="third_party_for" name="third_party_for" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="3為を入力">
                    </td>
                </tr>
                
                <!-- 融資用途フラグ -->
                <tr style="height: 20px">
                    <th id="1836490454R40" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">40</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R41" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">41</div>
                    </th>
                    <td class="s9" dir="ltr">建築費用</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="construction_cost" name="construction_cost" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="建築費用を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R42" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">42</div>
                    </th>
                    <td class="s9" dir="ltr">解体費用</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="demolition_cost" name="demolition_cost" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="解体費用を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R43" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">43</div>
                    </th>
                    <td class="s9" dir="ltr">運転資金</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="working_capital" name="working_capital" style="width: 100%; border: none; background: transparent; text-align: left;" placeholder="運転資金を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R44" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">44</div>
                    </th>
                    <td class="s9" dir="ltr">納税費用</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="tax_payment" name="tax_payment" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="納税費用を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R45" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">45</div>
                    </th>
                    <td class="s9" dir="ltr">賞与資金</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="bonus_fund" name="bonus_fund" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="賞与資金を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R46" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">46</div>
                    </th>
                    <td class="s9" dir="ltr">当座貸越</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="overdraft" name="overdraft" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="当座貸越を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R47" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">47</div>
                    </th>
                    <td class="s9" dir="ltr">バックファイナンス</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="back_finance" name="back_finance" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="バックファイナンスを入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R48" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">48</div>
                    </th>
                    <td class="s9" dir="ltr">連棟登記</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="joint_registration" name="joint_registration" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="連棟登記を入力">
                    </td>
                </tr>
                
                <!-- 財務条件 -->
                <tr style="height: 20px">
                    <th id="1836490454R49" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">49</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R50" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">50</div>
                    </th>
                    <td class="s9" dir="ltr">債務償還年数〇年ならOK</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="debt_service_years_ok" name="debt_service_years_ok" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="債務償還年数を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R51" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">51</div>
                    </th>
                    <td class="s9" dir="ltr">自己資本比率〇％ならOK</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="equity_ratio_ok" name="equity_ratio_ok" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="自己資本比率を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R52" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">52</div>
                    </th>
                    <td class="s9" dir="ltr">自社の格付</td>
                    <td class="s4" dir="ltr">
                        <input type="text" id="company_rating" name="company_rating" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="自社の格付を入力">
                    </td>
                </tr>
                
                <!-- その他質問事項 -->
                <tr style="height: 20px">
                    <th id="1836490454R53" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">53</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R54" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">54</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問1</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question1" name="other_question1" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問1を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R55" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">55</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問2</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question2" name="other_question2" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問2を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R56" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">56</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問3</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question3" name="other_question3" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問3を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R57" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">57</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問4</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question4" name="other_question4" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問4を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R58" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">58</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問5</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question5" name="other_question5" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問5を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R59" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">59</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問6</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question6" name="other_question6" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問6を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R60" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">60</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問7</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question7" name="other_question7" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問7を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R61" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">61</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問8</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question8" name="other_question8" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問8を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R62" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">62</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問9</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question9" name="other_question9" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問9を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R63" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">63</div>
                    </th>
                    <td class="s9" dir="ltr">その他の質問10</td>
                    <td class="s4" dir="ltr">
                        <textarea id="other_question10" name="other_question10" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="その他の質問10を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R64" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">64</div>
                    </th>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
 
            </tbody>
        </table>
        <div style="text-align: left; margin-top: 20px;">
            <button type="submit">更新する</button>
            <div id="formMessage" style="margin-top: 10px; font-weight: bold;"></div>
        </div>
    </form>
</div>

        <script>
            // 銀行・支店データを保持するグローバル変数
            let bankData = [];
            let branchData = [];
            
            // ページ読み込み時の処理
            document.addEventListener('DOMContentLoaded', function() {
                const formMessage = document.getElementById('formMessage');
                formMessage.textContent = '初期化中...';
                formMessage.className = 'loading';
                
                // 管理者かどうかをチェック
                checkUserPermission();
                
                // 銀行一覧を取得してdatalistに設定
                google.script.run
                    .withSuccessHandler(function(banks) {
                        bankData = banks; // グローバル変数に保存
                        const datalistElement = document.getElementById('bank_list');
                        
                        // 既存のオプションをクリア
                        datalistElement.innerHTML = '';
                        
                        // 銀行オプションを追加
                        banks.forEach(function(bank) {
                            const option = document.createElement('option');
                            option.value = bank.name; // 表示用には銀行名を使用
                            option.setAttribute('data-code', bank.code); // 銀行コードをdata属性に保存
                            datalistElement.appendChild(option);
                        });
                        
                        // 既存データを読み込み
                        loadExistingData();
                    })
                    .withFailureHandler(function(error) {
                        document.getElementById('formMessage').textContent = '銀行一覧の読み込みエラー: ' + error.toString();
                    })
                    .getBanksFromAPI();
                
                // 支店フィールドの初期状態を設定
                document.getElementById('branch_code').disabled = true;
                document.getElementById('branch_code').placeholder = '先に銀行を選択してください';
                
                // 銀行入力フィールドの変更イベント
                document.getElementById('bank_code').addEventListener('input', function() {
                    const inputValue = this.value;
                    const selectedBank = bankData.find(bank => bank.name === inputValue);
                    
                    if (selectedBank) {
                        // 選択された銀行のコードをdata属性に保存
                        this.setAttribute('data-selected-code', selectedBank.code);
                        // 支店一覧を取得
                        loadBranches(selectedBank.code);
                    } else {
                        // マッチしない場合はdata属性をクリア
                        this.removeAttribute('data-selected-code');
                        // 支店一覧をクリア
                        clearBranches();
                    }
                });
                
                // 支店入力フィールドの変更イベント
                document.getElementById('branch_code').addEventListener('input', function() {
                    const inputValue = this.value;
                    const selectedBranch = branchData.find(branch => branch.name === inputValue);
                    
                    if (selectedBranch) {
                        // 選択された支店のコードをdata属性に保存
                        this.setAttribute('data-selected-code', selectedBranch.code);
                    } else {
                        // マッチしない場合はdata属性をクリア
                        this.removeAttribute('data-selected-code');
                    }
                });
            });
            
            // 既存データを読み込む関数
            function loadExistingData() {
                document.getElementById('formMessage').textContent = '既存データを読み込み中...';
                
                google.script.run
                    .withSuccessHandler(function(data) {
                        if (data) {
                            // データをグローバル変数に保存（クライアント選択で使用）
                            window.tempLoanData = data;
                            
                            // IDを設定
                            document.getElementById('loan_information_id').value = data.id || '';
                            
                            // 銀行・支店情報を設定
                            if (data.bank_name) {
                                document.getElementById('bank_code').value = data.bank_name;
                                document.getElementById('bank_code').setAttribute('data-selected-code', data.bank_code || '');
                                
                                // 支店情報がある場合は支店一覧を読み込み
                                if (data.bank_code) {
                                    loadBranches(data.bank_code, data.branch_name);
                                }
                            }

                            console.log(data);
                            
                            // その他のフィールドを設定
                            document.getElementById('lending_rate').value = data.lending_rate || '';
                            document.getElementById('loan_fee').value = data.loan_fee || '';
                            document.getElementById('loan_limit').value = data.loan_limit || '';
                            
                            // 融資詳細条件フィールドを設定
                            document.getElementById('max_loan_amount').value = data.max_loan_amount || '';
                            document.getElementById('total_loan_balance').value = data.total_loan_balance || '';
                            document.getElementById('total_collateral_value').value = data.total_collateral_value || '';
                            document.getElementById('total_bank_risk_amount').value = data.total_bank_risk_amount || '';
                            document.getElementById('max_concurrent_loans').value = data.max_concurrent_loans || '';
                            document.getElementById('loan_execution_count').value = data.loan_execution_count || '';
                            document.getElementById('avg_response_days').value = data.avg_response_days || '';
                            document.getElementById('avg_response_loan_ratio').value = data.avg_response_loan_ratio || '';
                            
                            // 決裁権限情報フィールドを設定
                            document.getElementById('branch_manager_limit').value = data.branch_manager_limit || '';
                            document.getElementById('officer_approval_limit').value = data.officer_approval_limit || '';
                            document.getElementById('minimum_interest_rate').value = data.minimum_interest_rate || '';
                            document.getElementById('minimum_fee').value = data.minimum_fee || '';
                            
                            // プロジェクト・物件条件フィールドを設定
                            document.getElementById('max_project_period').value = data.max_project_period || '';
                            document.getElementById('wooden_useful_life').value = data.wooden_useful_life || '';
                            document.getElementById('steel_useful_life').value = data.steel_useful_life || '';
                            document.getElementById('rc_useful_life').value = data.rc_useful_life || '';
                            document.getElementById('target_area').value = data.target_area || '';
                            document.getElementById('required_equity_ratio').value = data.required_equity_ratio || '';
                            
                            // 物件条件フラグフィールドを設定
                            document.getElementById('no_inspection_cert').value = data.no_inspection_cert || '';
                            document.getElementById('old_earthquake_standard').value = data.old_earthquake_standard || '';
                            document.getElementById('floor_area_ratio_over').value = data.floor_area_ratio_over || '';
                            document.getElementById('building_coverage_over').value = data.building_coverage_over || '';
                            document.getElementById('land_only').value = data.land_only || '';
                            document.getElementById('leased_land').value = data.leased_land || '';
                            document.getElementById('eviction_case').value = data.eviction_case || '';
                            document.getElementById('third_party_for').value = data.third_party_for || '';
                            
                            // 融資用途フラグフィールドを設定
                            document.getElementById('construction_cost').value = data.construction_cost || '';
                            document.getElementById('demolition_cost').value = data.demolition_cost || '';
                            document.getElementById('working_capital').value = data.working_capital || '';
                            document.getElementById('tax_payment').value = data.tax_payment || '';
                            document.getElementById('bonus_fund').value = data.bonus_fund || '';
                            document.getElementById('overdraft').value = data.overdraft || '';
                            document.getElementById('back_finance').value = data.back_finance || '';
                            document.getElementById('joint_registration').value = data.joint_registration || '';
                            
                            // 財務条件フィールドを設定
                            document.getElementById('debt_service_years_ok').value = data.debt_service_years_ok || '';
                            document.getElementById('equity_ratio_ok').value = data.equity_ratio_ok || '';
                            document.getElementById('company_rating').value = data.company_rating || '';
                            
                            // その他質問事項フィールドを設定
                            document.getElementById('other_question1').value = data.other_question1 || '';
                            document.getElementById('other_question2').value = data.other_question2 || '';
                            document.getElementById('other_question3').value = data.other_question3 || '';
                            document.getElementById('other_question4').value = data.other_question4 || '';
                            document.getElementById('other_question5').value = data.other_question5 || '';
                            document.getElementById('other_question6').value = data.other_question6 || '';
                            document.getElementById('other_question7').value = data.other_question7 || '';
                            document.getElementById('other_question8').value = data.other_question8 || '';
                            document.getElementById('other_question9').value = data.other_question9 || '';
                            document.getElementById('other_question10').value = data.other_question10 || '';
                            
                            // クライアント情報を設定（管理者の場合のみ）
                            if (data.client_id && document.getElementById('clientRow').style.display !== 'none') {
                                // クライアント一覧が読み込まれていない場合は遅延実行
                                const setClientId = () => {
                                    const clientSelect = document.getElementById('client_id');
                                    if (clientSelect.options.length > 1) {
                                        clientSelect.value = data.client_id;
                                    } else {
                                        setTimeout(setClientId, 100);
                                    }
                                };
                                setClientId();
                            }
                            
                            document.getElementById('formMessage').textContent = '既存データ読み込み完了';
                            setTimeout(() => {
                                document.getElementById('formMessage').textContent = '';
                            }, 1000);
                        } else {
                            document.getElementById('formMessage').textContent = 'データが見つかりません';
                        }
                    })
                    .withFailureHandler(function(error) {
                        document.getElementById('formMessage').textContent = '既存データの読み込みエラー: ' + error.toString();
                    })
                    .getTempLoanInformationData();
            }
            
            // 支店一覧を読み込む関数
            function loadBranches(bankCode, existingBranchName) {
                document.getElementById('formMessage').textContent = '支店一覧を読み込み中...';
                
                google.script.run
                    .withSuccessHandler(function(branches) {
                        branchData = branches; // グローバル変数に保存
                        const branchDatalist = document.getElementById('branch_list');
                        const branchInput = document.getElementById('branch_code');
                        
                        // 既存のオプションをクリア
                        branchDatalist.innerHTML = '';
                        
                        // 支店オプションを追加
                        branches.forEach(function(branch) {
                            const option = document.createElement('option');
                            option.value = branch.name; // 表示用には支店名を使用
                            option.setAttribute('data-code', branch.code); // 支店コードをdata属性に保存
                            branchDatalist.appendChild(option);
                        });
                        
                        // 支店入力フィールドを有効化
                        branchInput.disabled = false;
                        branchInput.placeholder = '支店名を入力または選択';
                        
                        // 既存の支店名がある場合は設定
                        if (existingBranchName) {
                            branchInput.value = existingBranchName;
                            const selectedBranch = branches.find(branch => branch.name === existingBranchName);
                            if (selectedBranch) {
                                branchInput.setAttribute('data-selected-code', selectedBranch.code);
                            }
                        }
                        
                        document.getElementById('formMessage').textContent = '';
                    })
                    .withFailureHandler(function(error) {
                        document.getElementById('formMessage').textContent = '支店一覧の読み込みエラー: ' + error.toString();
                    })
                    .getBranchesFromAPI(bankCode);
            }
            
            // 支店一覧をクリアする関数
            function clearBranches() {
                branchData = [];
                const branchDatalist = document.getElementById('branch_list');
                const branchInput = document.getElementById('branch_code');
                
                branchDatalist.innerHTML = '';
                branchInput.disabled = true;
                branchInput.value = '';
                branchInput.placeholder = '先に銀行を選択してください';
            }
            
            // 管理者権限をチェックしてクライアント選択セクションを表示/非表示
            function checkUserPermission() {
                google.script.run
                    .withSuccessHandler(function(isAdmin) {
                        if (isAdmin) {
                            // 管理者の場合：クライアント選択セクションを表示
                            document.getElementById('clientRow').style.display = '';
                            loadClients();
                        } else {
                            // クライアントの場合：クライアント選択セクションを非表示
                            document.getElementById('clientRow').style.display = 'none';
                            // client_idのrequiredを削除
                            document.getElementById('client_id').required = false;
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.error('権限チェックエラー:', error);
                        // エラーの場合はデフォルトで非表示
                        document.getElementById('clientRow').style.display = 'none';
                        document.getElementById('client_id').required = false;
                    })
                    .isAdminUser();
            }
            
            // クライアント一覧を読み込む
            function loadClients() {
                google.script.run
                    .withSuccessHandler(function(clients) {
                        const clientSelect = document.getElementById('client_id');
                        
                        // 既存のオプションをクリア（最初の選択オプションは保持）
                        clientSelect.innerHTML = '<option value="">クライアントを選択してください</option>';
                        
                        // クライアントオプションを追加
                        clients.forEach(function(client) {
                            const option = document.createElement('option');
                            option.value = client.id;
                            option.textContent = client.name;
                            clientSelect.appendChild(option);
                        });
                        
                        // 既存データの読み込みが完了している場合は、クライアントIDを設定
                        const loanInformationId = document.getElementById('loan_information_id').value;
                        if (loanInformationId && window.tempLoanData && window.tempLoanData.client_id) {
                            clientSelect.value = window.tempLoanData.client_id;
                        }
                    })
                    .withFailureHandler(function(error) {
                        document.getElementById('formMessage').textContent = 'クライアント一覧の読み込みエラー: ' + error.toString();
                    })
                    .fetchAllClients();
            }

            function submitForm() {
                try {
                    // 銀行コードを取得
                    const bankInputElement = document.getElementById('bank_code');
                    const bankInputValue = bankInputElement ? bankInputElement.value : '';
                    let selectedBankCode = '';
                    
                    if (bankInputValue) {
                        // data-selected-code属性から銀行コードを取得
                        selectedBankCode = bankInputElement.getAttribute('data-selected-code') || '';
                        
                        // data-selected-codeがない場合は、銀行名から検索
                        if (!selectedBankCode) {
                            const selectedBank = bankData.find(bank => bank.name === bankInputValue);
                            selectedBankCode = selectedBank ? selectedBank.code : '';
                        }
                    }
                    
                    // 支店コードを取得
                    const branchInputElement = document.getElementById('branch_code');
                    const branchInputValue = branchInputElement ? branchInputElement.value : '';
                    let selectedBranchCode = '';
                    
                    if (branchInputValue) {
                        // data-selected-code属性から支店コードを取得
                        selectedBranchCode = branchInputElement.getAttribute('data-selected-code') || '';
                        
                        // data-selected-codeがない場合は、支店名から検索
                        if (!selectedBranchCode) {
                            const selectedBranch = branchData.find(branch => branch.name === branchInputValue);
                            selectedBranchCode = selectedBranch ? selectedBranch.code : '';
                        }
                    }
                    
                    const formData = {
                        id: document.getElementById('loan_information_id').value,
                        bank_code: selectedBankCode,
                        branch_code: selectedBranchCode,
                        lending_rate: document.getElementById('lending_rate') ? parseFloat(document.getElementById('lending_rate').value) || null : null,
                        loan_fee: document.getElementById('loan_fee') ? parseFloat(document.getElementById('loan_fee').value) || null : null,
                        loan_limit: document.getElementById('loan_limit') ? parseFloat(document.getElementById('loan_limit').value) || null : null,
                        
                        // 融資詳細条件
                        max_loan_amount: document.getElementById('max_loan_amount') ? document.getElementById('max_loan_amount').value : '',
                        total_loan_balance: document.getElementById('total_loan_balance') ? document.getElementById('total_loan_balance').value : '',
                        total_collateral_value: document.getElementById('total_collateral_value') ? document.getElementById('total_collateral_value').value : '',
                        total_bank_risk_amount: document.getElementById('total_bank_risk_amount') ? document.getElementById('total_bank_risk_amount').value : '',
                        max_concurrent_loans: document.getElementById('max_concurrent_loans') ? document.getElementById('max_concurrent_loans').value : '',
                        loan_execution_count: document.getElementById('loan_execution_count') ? document.getElementById('loan_execution_count').value : '',
                        avg_response_days: document.getElementById('avg_response_days') ? document.getElementById('avg_response_days').value : '',
                        avg_response_loan_ratio: document.getElementById('avg_response_loan_ratio') ? document.getElementById('avg_response_loan_ratio').value : '',
                        
                        // 決裁権限情報
                        branch_manager_limit: document.getElementById('branch_manager_limit') ? document.getElementById('branch_manager_limit').value : '',
                        officer_approval_limit: document.getElementById('officer_approval_limit') ? document.getElementById('officer_approval_limit').value : '',
                        minimum_interest_rate: document.getElementById('minimum_interest_rate') ? document.getElementById('minimum_interest_rate').value : '',
                        minimum_fee: document.getElementById('minimum_fee') ? document.getElementById('minimum_fee').value : '',
                        
                        // プロジェクト・物件条件
                        max_project_period: document.getElementById('max_project_period') ? document.getElementById('max_project_period').value : '',
                        wooden_useful_life: document.getElementById('wooden_useful_life') ? document.getElementById('wooden_useful_life').value : '',
                        steel_useful_life: document.getElementById('steel_useful_life') ? document.getElementById('steel_useful_life').value : '',
                        rc_useful_life: document.getElementById('rc_useful_life') ? document.getElementById('rc_useful_life').value : '',
                        target_area: document.getElementById('target_area') ? document.getElementById('target_area').value : '',
                        required_equity_ratio: document.getElementById('required_equity_ratio') ? document.getElementById('required_equity_ratio').value : '',
                        
                        // 物件条件フラグ
                        no_inspection_cert: document.getElementById('no_inspection_cert') ? document.getElementById('no_inspection_cert').value : '',
                        old_earthquake_standard: document.getElementById('old_earthquake_standard') ? document.getElementById('old_earthquake_standard').value : '',
                        floor_area_ratio_over: document.getElementById('floor_area_ratio_over') ? document.getElementById('floor_area_ratio_over').value : '',
                        building_coverage_over: document.getElementById('building_coverage_over') ? document.getElementById('building_coverage_over').value : '',
                        land_only: document.getElementById('land_only') ? document.getElementById('land_only').value : '',
                        leased_land: document.getElementById('leased_land') ? document.getElementById('leased_land').value : '',
                        eviction_case: document.getElementById('eviction_case') ? document.getElementById('eviction_case').value : '',
                        third_party_for: document.getElementById('third_party_for') ? document.getElementById('third_party_for').value : '',
                        
                        // 融資用途フラグ
                        construction_cost: document.getElementById('construction_cost') ? document.getElementById('construction_cost').value : '',
                        demolition_cost: document.getElementById('demolition_cost') ? document.getElementById('demolition_cost').value : '',
                        working_capital: document.getElementById('working_capital') ? document.getElementById('working_capital').value : '',
                        tax_payment: document.getElementById('tax_payment') ? document.getElementById('tax_payment').value : '',
                        bonus_fund: document.getElementById('bonus_fund') ? document.getElementById('bonus_fund').value : '',
                        overdraft: document.getElementById('overdraft') ? document.getElementById('overdraft').value : '',
                        back_finance: document.getElementById('back_finance') ? document.getElementById('back_finance').value : '',
                        joint_registration: document.getElementById('joint_registration') ? document.getElementById('joint_registration').value : '',
                        
                        // 財務条件
                        debt_service_years_ok: document.getElementById('debt_service_years_ok') ? document.getElementById('debt_service_years_ok').value : '',
                        equity_ratio_ok: document.getElementById('equity_ratio_ok') ? document.getElementById('equity_ratio_ok').value : '',
                        company_rating: document.getElementById('company_rating') ? document.getElementById('company_rating').value : '',
                        
                        // その他質問事項
                        other_question1: document.getElementById('other_question1') ? document.getElementById('other_question1').value : '',
                        other_question2: document.getElementById('other_question2') ? document.getElementById('other_question2').value : '',
                        other_question3: document.getElementById('other_question3') ? document.getElementById('other_question3').value : '',
                        other_question4: document.getElementById('other_question4') ? document.getElementById('other_question4').value : '',
                        other_question5: document.getElementById('other_question5') ? document.getElementById('other_question5').value : '',
                        other_question6: document.getElementById('other_question6') ? document.getElementById('other_question6').value : '',
                        other_question7: document.getElementById('other_question7') ? document.getElementById('other_question7').value : '',
                        other_question8: document.getElementById('other_question8') ? document.getElementById('other_question8').value : '',
                        other_question9: document.getElementById('other_question9') ? document.getElementById('other_question9').value : '',
                        other_question10: document.getElementById('other_question10') ? document.getElementById('other_question10').value : '',
                        
                        client_id: document.getElementById('client_id') ? document.getElementById('client_id').value : null
                    };
                    
                    // 必須チェック
                    if (!formData.id) {
                        document.getElementById('formMessage').textContent = 'エラー: 融資情報IDが見つかりません';
                        return;
                    }
                    if (!formData.bank_code) {
                        document.getElementById('formMessage').textContent = 'エラー: 銀行の選択は必須です';
                        return;
                    }
                    
                    // 管理者の場合はクライアント選択が必須
                    const clientRow = document.getElementById('clientRow');
                    if (clientRow && clientRow.style.display !== 'none') {
                        if (!formData.client_id) {
                            document.getElementById('formMessage').textContent = 'エラー: クライアントの選択は必須です';
                            return;
                        }
                    }
                    
                    document.getElementById('formMessage').textContent = '更新中…';
                    
                    // 銀行・支店の詳細を順次取得
                    if (formData.bank_code) {
                        document.getElementById('formMessage').textContent = '銀行詳細を取得中...';
                        
                        google.script.run
                            .withSuccessHandler(function(bankDetail) {
                                // 銀行詳細データを追加
                                if (bankDetail) {
                                    formData.bank_name = bankDetail.name || '';
                                    formData.bank_name_half_kana = bankDetail.halfWidthKana || '';
                                    formData.bank_name_full_kana = bankDetail.fullWidthKana || '';
                                    formData.bank_name_hiragana = bankDetail.hiragana || '';
                                    formData.bank_business_type_code = bankDetail.businessTypeCode || '';
                                    formData.bank_business_type = bankDetail.businessType || '';
                                }
                                
                                // 支店詳細も取得する場合
                                if (formData.branch_code) {
                                    document.getElementById('formMessage').textContent = '支店詳細を取得中...';
                                    
                                    google.script.run
                                        .withSuccessHandler(function(branchDetail) {
                                            // 支店詳細データを追加
                                            if (branchDetail) {
                                                formData.branch_name = branchDetail.name || '';
                                                formData.branch_name_half_kana = branchDetail.halfWidthKana || '';
                                                formData.branch_name_full_kana = branchDetail.fullWidthKana || '';
                                                formData.branch_name_hiragana = branchDetail.hiragana || '';
                                            }
                                            
                                            // 更新処理を実行
                                            updateLoanInformationData(formData);
                                        })
                                        .withFailureHandler(function(error) {
                                            document.getElementById('formMessage').textContent = '支店詳細の取得エラー: ' + error.toString();
                                        })
                                        .getBranchDetailFromAPI(formData.bank_code, formData.branch_code);
                                } else {
                                    // 支店が選択されていない場合は直接更新
                                    updateLoanInformationData(formData);
                                }
                            })
                            .withFailureHandler(function(error) {
                                document.getElementById('formMessage').textContent = '銀行詳細の取得エラー: ' + error.toString();
                            })
                            .getBankDetailFromAPI(formData.bank_code);
                    } else {
                        // 銀行が選択されていない場合は直接更新
                        updateLoanInformationData(formData);
                    }
                } catch (error) {
                    document.getElementById('formMessage').textContent = 'スクリプトエラー: ' + error.message;
                }
            }

            function updateLoanInformationData(formData) {
                document.getElementById('formMessage').textContent = '更新中...';
                
                google.script.run
                    .withSuccessHandler(res => {
                        if (res && res.success) {
                            document.getElementById('formMessage').textContent = '更新完了: ID=' + (res.id || '');
                            setTimeout(() => {
                                google.script.host.close();
                            }, 1000);
                        } else {
                            document.getElementById('formMessage').textContent = 'エラー: ' + (res ? res.message : '不明なエラー');
                        }
                    })
                    .withFailureHandler(err => {
                        document.getElementById('formMessage').textContent = '通信エラー: ' + err.toString();
                    })
                    .updateLoanInformation(formData);
            }
        </script>
    </body>
</html> 