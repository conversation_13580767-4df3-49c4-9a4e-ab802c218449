<!DOCTYPE html>
<html lang="ja">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s14{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s0{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.waffle tbody th{visibility: hidden;width: 1px;display: block;}</style>

    <style type="text/css">
        body .ritz .waffle td:has(input, select, textarea) {
            padding: 0 !important;
        }
        body .ritz .waffle input,
        body .ritz .waffle select,
        body .ritz .waffle textarea {
            min-height: 26px;
            display: flex;
            line-height: 1.5;
            box-sizing: border-box;
            padding: 0 8px;
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.15);
            background-color: #fff !important;
        }
        
        /* 見出し系のセルの背景色を変更 */
        body .ritz .waffle .s1,
        body .ritz .waffle .s3,
        body .ritz .waffle .s6 {
            background-color: #efefef !important;
        }
        
        /* disabled状態のスタイル */
        input:disabled, select:disabled {
            background-color: #f8f8f8 !important;
            color: #999 !important;
            cursor: not-allowed !important;
        }
        
        /* ボタンの統一スタイル */
        input[type="submit"], button[type="submit"], button {
            background-color: #0A4484;
            color: #FFFFFF;
            font-size: 14px;
            font-weight: normal;
            border-radius: 3px;
            border: none;
            padding: 8px 20px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.2s ease;
        }
        
        /* ホバー時のスタイル */
        input[type="submit"]:hover, button[type="submit"]:hover, button:hover {
            background-color: #083a6f;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        /* クリック時のスタイル */
        input[type="submit"]:active, button[type="submit"]:active, button:active {
            background-color: #062a52;
            box-shadow: 0 1px 4px rgba(0,0,0,0.3);
            transform: translateY(0);
        }
        
        /* ローディング表示用のスタイル */
        #formMessage {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }
        #formMessage::before {
            content: '';
            width: 16px;
            height: 16px;
            margin-bottom: 8px;
            border: 2px solid #CCC;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        #formMessage::after {
            content: '';
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.5);
            z-index: -1;
        }
        #formMessage:empty {
            display: none !important;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

    </style>
</head>
<body>
    <div class="ritz grid-container" dir="ltr">
        <form id="userForm" onsubmit="submitForm(); return false;">
            <input type="hidden" id="userId" name="user_id">
            <input type="hidden" id="profileId" name="id">
            
            <table class="waffle" cellspacing="0" cellpadding="0">
                <thead>
                    <tr>
                        <th class="row-header freezebar-origin-ltr"></th>
                        <th id="1836490454C0" style="width:2px;" class="column-headers-background"></th>
                        <th id="1836490454C1" style="width:100px;" class="column-headers-background"></th>
                        <th id="1836490454C2" style="width:100px;" class="column-headers-background"></th>
                        <th id="1836490454C3" style="width:100px;" class="column-headers-background"></th>
                        <th id="1836490454C4" style="width:100px;" class="column-headers-background"></th>
                        <th id="1836490454C5" style="width:100px;" class="column-headers-background"></th>
                        <th id="1836490454C6" style="width:100px;" class="column-headers-background"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="height: 20px">
                        <th id="1836490454R0" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">1</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" colspan="5">ユーザー編集フォーム</td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R1" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">2</div>
                        </th>
                        <td class="s2"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R2" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">3</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" rowspan="5" style="vertical-align: middle;">基本情報</td>
                        <td class="s6" dir="ltr">ユーザー名<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="text" id="name" name="name" required style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="ユーザー名を入力（必須）">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R3" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">4</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s6" dir="ltr">メールアドレス</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="email" id="email" name="email" disabled style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="メールアドレス（変更不可）">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R4" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">5</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s6" dir="ltr">パスワード</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="password" id="password" name="password" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="変更する場合のみ入力">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R5" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">6</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s6" dir="ltr">権限<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <select id="role" name="role" required style="width: 100%; border: none; background: transparent; text-align: center;" onchange="toggleClientSelection()">
                                <option value="">権限を選択してください</option>
                                <option value="1">管理者</option>
                                <option value="2">クライアント</option>
                            </select>
                        </td>
                    </tr>
                    <tr style="height: 20px" id="clientRow">
                        <th id="1836490454R6" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">7</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s6" dir="ltr">クライアント<span id="clientRequired" style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <select id="client_id" name="client_id" style="width: 100%; border: none; background: transparent; text-align: center;">
                                <option value="">クライアントを選択してください</option>
                            </select>
                        </td>
                    </tr>

                </tbody>
            </table>
            <div style="text-align: left; margin-top: 20px;">
                <button type="submit">更新する</button>
                <div id="formMessage" style="margin-top: 10px; font-weight: bold;"></div>
            </div>
        </form>
    </div>

    <script>
        // ページ読み込み時の処理
        document.addEventListener('DOMContentLoaded', function() {
            loadUserData();
            loadClients();
        });

        // 既存のユーザーデータを読み込み
        function loadUserData() {
            const formMessage = document.getElementById('formMessage');
            formMessage.textContent = 'ユーザーデータを読み込み中...';
            formMessage.className = 'loading';
            
            google.script.run
                .withSuccessHandler(function(userData) {
                    if (userData) {
                        // フォームに既存データを設定
                        document.getElementById('userId').value = userData.user_id || '';
                        document.getElementById('profileId').value = userData.profile_id || '';
                        document.getElementById('name').value = userData.user_name || '';
                        document.getElementById('email').value = userData.user_email || '';
                        document.getElementById('role').value = userData.role || '';
                        document.getElementById('client_id').value = userData.client_id || '';
                        
                        // 権限に応じてクライアント選択を設定
                        toggleClientSelection();
                        
                        formMessage.textContent = '';
                        formMessage.className = '';
                    } else {
                        formMessage.textContent = 'ユーザーデータが見つかりません';
                        formMessage.className = 'error';
                    }
                })
                .withFailureHandler(function(error) {
                    formMessage.textContent = 'ユーザーデータの読み込みエラー: ' + error.toString();
                    formMessage.className = 'error';
                })
                .getTempUserData();
        }

        // クライアント一覧を読み込む
        function loadClients() {
            google.script.run
                .withSuccessHandler(function(clients) {
                    const clientSelect = document.getElementById('client_id');
                    const currentValue = clientSelect.value; // 現在の値を保存
                    
                    // 既存のオプションをクリア（最初の選択オプションは保持）
                    clientSelect.innerHTML = '<option value="">クライアントを選択してください</option>';
                    
                    // クライアントオプションを追加
                    clients.forEach(function(client) {
                        const option = document.createElement('option');
                        option.value = client.id;
                        option.textContent = client.name;
                        clientSelect.appendChild(option);
                    });
                    
                    // 以前の値を復元
                    if (currentValue) {
                        clientSelect.value = currentValue;
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('クライアント一覧の読み込みエラー:', error);
                })
                .fetchAllClients();
        }

        // 権限選択時にクライアント選択のenable/disableを切り替え
        function toggleClientSelection() {
            const roleSelect = document.getElementById('role');
            const clientSelect = document.getElementById('client_id');
            const clientRequired = document.getElementById('clientRequired');
            
            if (roleSelect.value === '1') {
                // 管理者の場合：クライアント選択を無効化・非必須・空にする
                clientSelect.disabled = true;
                clientSelect.required = false;
                clientSelect.value = '';
                clientRequired.style.display = 'none';
            } else if (roleSelect.value === '2') {
                // クライアントの場合：クライアント選択を有効化・必須
                clientSelect.disabled = false;
                clientSelect.required = true;
                clientRequired.style.display = 'inline';
            } else {
                // 未選択の場合：クライアント選択を無効化・非必須・空にする
                clientSelect.disabled = true;
                clientSelect.required = false;
                clientSelect.value = '';
                clientRequired.style.display = 'none';
            }
        }

        function submitForm() {
            try {
                const formData = {
                    id: document.getElementById('profileId').value,
                    user_id: document.getElementById('userId').value,
                    name: document.getElementById('name').value.trim(),
                    password: document.getElementById('password').value.trim() || null, // 空の場合はnull
                    role: parseInt(document.getElementById('role').value),
                    client_id: document.getElementById('client_id').value || null
                };
                
                // 必須チェック
                const formMessage = document.getElementById('formMessage');
                if (!formData.id) {
                    formMessage.textContent = 'エラー: プロフィールIDが取得できません';
                    formMessage.className = 'error';
                    return;
                }
                if (!formData.name) {
                    formMessage.textContent = 'エラー: ユーザー名は必須です';
                    formMessage.className = 'error';
                    return;
                }
                if (!formData.role || (formData.role !== 1 && formData.role !== 2)) {
                    formMessage.textContent = 'エラー: 権限は必須です（管理者または クライアント）';
                    formMessage.className = 'error';
                    return;
                }
                
                // クライアント権限の場合はクライアント選択が必須
                if (formData.role === 2 && !formData.client_id) {
                    formMessage.textContent = 'エラー: クライアント権限の場合はクライアントの選択が必須です';
                    formMessage.className = 'error';
                    return;
                }
                
                formMessage.textContent = '更新中…';
                formMessage.className = 'loading';
                
                // 更新処理を実行
                google.script.run
                    .withSuccessHandler(res => {
                        if (res && res.success) {
                            formMessage.textContent = '更新完了: ID=' + (res.id || '');
                            formMessage.className = 'success';
                            setTimeout(() => {
                                google.script.host.close();
                            }, 1000);
                        } else {
                            formMessage.textContent = 'エラー: ' + (res ? res.message : '不明なエラー');
                            formMessage.className = 'error';
                        }
                    })
                    .withFailureHandler(err => {
                        formMessage.textContent = '通信エラー: ' + err.toString();
                        formMessage.className = 'error';
                    })
                    .updateUser(formData);
                    
            } catch (error) {
                const formMessage = document.getElementById('formMessage');
                formMessage.textContent = 'スクリプトエラー: ' + error.message;
                formMessage.className = 'error';
            }
        }
    </script>
</body>
</html>
