/**
 * ISO形式の日時文字列をY-m-d H:i:s形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d H:i:s形式の日時文字列
 */
function formatDateTime(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * 銀行担当者一覧を取得（bank_representativesテーブルとclientsテーブルを結合）
 * @param {string=} representativeName 部分検索する representative_name。省略時は絞り込みなし
 * @param {string=} branchName 部分検索する関連する支店名。省略時は絞り込みなし
 * @param {string=} bankName 部分検索する関連する銀行名。省略時は絞り込みなし
 * @param {string=} clientName 部分検索するクライアント名。省略時は絞り込みなし
 * @return {Object[]} Supabase から返ってきた銀行担当者配列
 */
function fetchMyBankRepresentatives(representativeName, branchName, bankName, clientName) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken) {
      const errorMsg = 'SUPABASE_ACCESS_TOKEN が設定されていません。';
      SpreadsheetApp.getUi().alert(
        '設定エラー',
        errorMsg + '\n\n管理者にお問い合わせください。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // PostgreSQL RPC関数を使用して銀行担当者情報を取得
    const payload = {
      search_representative_name: representativeName || null,
      search_bank_name: bankName || null,
      search_branch_name: branchName || null,
      search_client_name: clientName || null,
      filter_client_id: (clientId && clientId.trim() !== '') ? clientId : null
    };

    const url = `${supabaseUrl}/rest/v1/rpc/get_bank_representatives_with_clients`;
    const res = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      const errorMsg = '銀行担当者取得エラー: ' + (err.message || JSON.stringify(err));
      SpreadsheetApp.getUi().alert(
        '銀行担当者データ取得エラー',
        'Supabaseからの銀行担当者データ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    // 既にダイアログ表示済みでない場合のみ表示
    if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('銀行担当者取得エラー')) {
      SpreadsheetApp.getUi().alert(
        '予期しないエラー',
        '銀行担当者データの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
    throw error;
  }
}

/**
 * 検索ボタンに割り当てる関数。
 * 担当者名、銀行名、支店名、クライアント名を部分検索し、結果をシートに出力します
 */
function searchBankRepresentatives() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const representativeName = sheet.getRange('D4').getDisplayValue().trim();
  const bankName = sheet.getRange('D5').getDisplayValue().trim();
  const branchName = sheet.getRange('D6').getDisplayValue().trim();
  const clientName = sheet.getRange('D7').getDisplayValue().trim();
  const startRow = 17;
  const idCol = 1;   // A 列
  const dataStartCol = 3;  // C 列
  const numCols = 9;  // C〜K の列数（融資に対する姿勢を追加）
  const lastRow = sheet.getMaxRows();

  try {
    const representatives = fetchMyBankRepresentatives(representativeName, branchName, bankName, clientName);

    // ─── クリア ───
    sheet.getRange(startRow, idCol, lastRow - (startRow - 1), 1).clearContent();
    sheet.getRange(startRow, dataStartCol, lastRow - (startRow - 1), numCols).clearContent();

    if (representatives.length === 0) {
      sheet.getRange(startRow, dataStartCol).setValue('該当なし');
      return;
    }

    // ─── 出力用配列を作成 ───
    // A列用：id
    const ids = representatives.map(r => [r.id]);

    // C〜K列用：各フィールド（担当者名、銀行名、支店名、役職、携帯、メールアドレス、融資に対する姿勢、備考、クライアント名）
    const values = representatives.map(r => [
      r.representative_name || '',
      r.bank_name || '',   
      r.branch_name || '', 
      r.position || '',
      r.mobile_phone || '',      
      r.email_address || '',
      r.lending_attitude || '',  
      r.remarks || '',
      r.client_name || '', // RPC関数の戻り値から直接取得
    ]);

    // ─── 一括書き込み ───
    sheet
      .getRange(startRow, idCol, ids.length, 1)
      .setValues(ids);

    sheet
      .getRange(startRow, dataStartCol, values.length, numCols)
      .setValues(values);

  } catch (e) {
    // エラーダイアログを表示
    SpreadsheetApp.getUi().alert(
      '銀行担当者検索エラー',
      '銀行担当者データの取得に失敗しました。\n\nエラー詳細:\n' + e.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    
    // シートにもエラー表示
    sheet.getRange(startRow, dataStartCol).setValue('❌ エラー: ' + e.message);
  }
}

/**
 * 登録モーダルを表示
 */
function createModalBankRepresentatives() {
  const html = HtmlService
    .createHtmlOutputFromFile('CreateModalBankRepresentative')
    .setWidth(980)   // 横幅
    .setHeight(1020); // 高さ

  SpreadsheetApp
    .getUi()
    .showModalDialog(html, '銀行担当者登録モーダル');
}

/**
 * HTML から呼び出される登録関数
 * @param {Object} formData 登録フォームのデータ
 */
function registerBankRepresentative(formData) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'アクセストークンが未設定' };
  }
  
  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // ペイロード作成
  const payload = Object.assign({}, formData);
  
  // 管理者の場合はフォームから送られたclient_idを使用、クライアントの場合は自動設定
  if (clientId && clientId.trim() !== '') {
    // クライアントユーザーの場合：自動的にclient_idを設定
    payload.client_id = clientId;
  } else {
    // 管理者の場合：フォームからclient_idが送られていることを期待
    if (!formData.client_id) {
      return { success: false, message: '管理者権限での登録にはクライアントIDの指定が必要です' };
    }
  }
  
  // デバッグ用：ペイロードの内容をログ出力
  console.log('registerBankRepresentative - formData:', JSON.stringify(formData));
  console.log('registerBankRepresentative - payload:', JSON.stringify(payload));

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/bank_representatives`,
    {
      method: 'post',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: 'return=representation'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const result = JSON.parse(res.getContentText());
  
  console.log('registerBankRepresentative - response code:', code);
  console.log('registerBankRepresentative - response:', JSON.stringify(result));
  
  if (code === 201 && Array.isArray(result) && result.length > 0) {
    // 検索結果を更新
    searchBankRepresentatives();
    return { success: true, id: result[0].id };
  }
  
  return { success: false, message: result.message || JSON.stringify(result) };
}

/**
 * 更新モーダルを表示
 */
function updateModalBankRepresentatives() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const activeRange = sheet.getActiveRange();
  const selectedRow = activeRange.getRow();
  
  // A列からIDを取得
  const representativeId = sheet.getRange(selectedRow, 1).getValue();
  
  if (!representativeId) {
    SpreadsheetApp.getUi().alert(
      '選択エラー',
      '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return;
  }
  
  try {
    // Supabaseからデータを取得
    const representativeData = fetchBankRepresentativeById(representativeId);
    
    if (!representativeData) {
      SpreadsheetApp.getUi().alert(
        '銀行担当者が見つかりません',
        '指定されたIDの銀行担当者が見つかりません。\n\n担当者ID: ' + representativeId,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }
    
    // 取得したデータを一時保存
    const docProps = PropertiesService.getDocumentProperties();
    docProps.setProperty('TEMP_BANK_REPRESENTATIVE_DATA', JSON.stringify(representativeData));
    
    const html = HtmlService
      .createHtmlOutputFromFile('UpdateModalBankRepresentative')
      .setWidth(980)   // 横幅
      .setHeight(1020); // 高さ

    SpreadsheetApp
      .getUi()
      .showModalDialog(html, '銀行担当者編集モーダル');
      
  } catch (error) {
    SpreadsheetApp.getUi().alert(
      '銀行担当者データ取得エラー',
      '銀行担当者データの取得に失敗しました。\n\nエラー詳細:\n' + error.message + '\n\n担当者ID: ' + representativeId,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * IDで特定の銀行担当者データを取得
 * @param {string} representativeId 銀行担当者ID
 * @return {Object} 銀行担当者データ
 */
function fetchBankRepresentativeById(representativeId) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
  }

  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // PostgreSQL RPC関数を使用して銀行担当者情報を取得
  const payload = {
    p_representative_id: representativeId,
    filter_client_id: (clientId && clientId.trim() !== '') ? clientId : null
  };

  const url = `${supabaseUrl}/rest/v1/rpc/get_bank_representative_by_id`;
  const res = UrlFetchApp.fetch(url, {
    method: 'POST',
    contentType: 'application/json',
    headers: {
      apikey: apiKey,
      Authorization: 'Bearer ' + accessToken
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  });

  if (res.getResponseCode() !== 200) {
    const err = JSON.parse(res.getContentText());
    throw new Error('銀行担当者データ取得エラー: ' + (err.message || JSON.stringify(err)));
  }
  
  const result = JSON.parse(res.getContentText());
  return result.length > 0 ? result[0] : null;
}

/**
 * HTML側から呼び出される関数：一時保存された銀行担当者データを取得
 * @return {Object} 銀行担当者データ
 */
function getTempBankRepresentativeData() {
  const docProps = PropertiesService.getDocumentProperties();
  const dataStr = docProps.getProperty('TEMP_BANK_REPRESENTATIVE_DATA');
  
  if (!dataStr) {
    return null;
  }
  
  // 一時データを削除
  docProps.deleteProperty('TEMP_BANK_REPRESENTATIVE_DATA');
  
  return JSON.parse(dataStr);
}

/**
 * 全てのクライアントを取得（セレクトボックス用）
 * @return {Object[]} クライアント配列
 */
function fetchAllClients() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
      throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // clientsテーブルから全てのクライアントを取得
    const url = `${supabaseUrl}/rest/v1/clients?select=id,name&order=name.asc`;
    const res = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('クライアント取得エラー: ' + (err.message || JSON.stringify(err)));
    }
    
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    throw error;
  }
}

/**
 * HTML から呼び出される更新関数
 * @param {Object} formData 更新フォームのデータ
 */
function updateBankRepresentative(formData) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'アクセストークンが未設定' };
  }
  
  if (!formData.id) {
    return { success: false, message: '銀行担当者IDが指定されていません' };
  }
  
  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // ペイロード作成（idは除外）
  const payload = Object.assign({}, formData);
  delete payload.id; // idは更新対象から除外
  payload.updated_at = new Date().toISOString(); // 現在時刻を更新時刻として追加

  // 管理者の場合はclient_idでフィルタリングしない
  let filter = `id=eq.${formData.id}`;
  if (clientId && clientId.trim() !== '') {
    filter = `client_id=eq.${clientId}&${filter}`;
    payload.client_id = clientId; // クライアントユーザーの場合はclient_idを確実に設定
  }

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/bank_representatives?${filter}`,
    {
      method: 'PATCH',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: 'return=representation'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const result = JSON.parse(res.getContentText());
  
  if (code === 200 && Array.isArray(result) && result.length > 0) {
    // 検索結果を更新
    searchBankRepresentatives();
    return { success: true, id: result[0].id };
  }

  return { success: false, message: result.message || JSON.stringify(result) };
}