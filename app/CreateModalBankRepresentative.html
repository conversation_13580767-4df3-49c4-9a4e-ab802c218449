<!DOCTYPE html>
<html lang="ja">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s14{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s0{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.waffle tbody th{visibility: hidden;width: 1px;display: block;}</style>

    <style type="text/css">
        body .ritz .waffle td:has(input, select, textarea) {
            padding: 0 !important;
        }
        body .ritz .waffle input,
        body .ritz .waffle select,
        body .ritz .waffle textarea {
            min-height: 26px;
            display: flex;
            line-height: 1.5;
            box-sizing: border-box;
            padding: 0 8px;
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.15);
            background-color: #fff !important;
        }
        
        /* 見出し系のセルの背景色を変更 */
        body .ritz .waffle .s1,
        body .ritz .waffle .s3,
        body .ritz .waffle .s6,
        body .ritz .waffle .s9 {
            background-color: #efefef !important;
        }
        
        /* datalist用のスタイル調整 */
        input[list] {
            cursor: pointer;
        }
        
        /* disabled状態のスタイル */
        input:disabled {
            background-color: #f8f8f8 !important;
            color: #999 !important;
            cursor: not-allowed !important;
        }
        
        /* ボタンの統一スタイル */
        input[type="submit"], button[type="submit"], button {
            background-color: #0A4484;
            color: #FFFFFF;
            font-size: 14px;
            font-weight: normal;
            border-radius: 3px;
            border: none;
            padding: 8px 20px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.2s ease;
        }
        
        /* ホバー時のスタイル */
        input[type="submit"]:hover, button[type="submit"]:hover, button:hover {
            background-color: #083a6f;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        /* クリック時のスタイル */
        input[type="submit"]:active, button[type="submit"]:active, button:active {
            background-color: #062a52;
            box-shadow: 0 1px 4px rgba(0,0,0,0.3);
            transform: translateY(0);
        }

        /* ローディング表示用のスタイル */
        #formMessage {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }
        #formMessage::before {
            content: '';
            width: 16px;
            height: 16px;
            margin-bottom: 8px;
            border: 2px solid #CCC;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        #formMessage::after {
            content: '';
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.5);
            z-index: -1;
        }
        #formMessage:empty {
            display: none !important;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

    </style>
</head>
<body>
    <div class="ritz grid-container" dir="ltr">
        <form id="bankRepresentativeForm" onsubmit="submitForm(); return false;">
        <table class="waffle" cellspacing="0" cellpadding="0">
            <thead>
                <tr>
                    <th class="row-header freezebar-origin-ltr"></th>
                    <th id="1836490454C0" style="width:2px;" class="column-headers-background"></th>
                    <th id="1836490454C1" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C2" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C3" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C4" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C5" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C6" style="width:100px;" class="column-headers-background"></th>
                </tr>
            </thead>
            <tbody>
                    <tr style="height: 20px">
                        <th id="1836490454R0" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">1</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" colspan="5">銀行担当者情報登録フォーム</td>
                    </tr>
                                    <tr style="height: 20px">
                        <th id="1836490454R1" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">2</div>
                        </th>
                        <td class="s2"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                    </tr>
                    <tr style="height: 20px; display: none;" id="clientRow">
                        <th id="1836490454R2_0" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">3</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr">クライアント</td>
                        <td class="s6" dir="ltr">クライアント名<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <select id="client_id" name="client_id" required style="width: 100%; border: none; background: transparent; text-align: center;">
                                <option value="">クライアントを選択してください</option>
                            </select>
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R2" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">4</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" rowspan="2" style="vertical-align: middle;">基本情報</td>
                        <td class="s6" dir="ltr">担当者名</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="text" id="representative_name" name="representative_name" required style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="担当者名を入力">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R2_1" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">5</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">役職</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="text" id="position" name="position" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="役職を入力">
                        </td>
                    </tr>
                                    <tr style="height: 20px">
                        <th id="1836490454R3" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">6</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" rowspan="1" style="vertical-align: middle;">銀行情報</td>
                        <td class="s6" dir="ltr">銀行名称</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="text" id="bank_code" name="bank_code" list="bank_list" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="銀行名を入力または選択">
                            <datalist id="bank_list">
                            </datalist>
                        </td>
                    </tr>
                <tr style="height: 20px">
                    <th id="1836490454R5" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">7</div>
                    </th>
                    <td class="s2"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R5_1" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">8</div>
                    </th>
                    <td class="s0"></td>
                    <td class="s1" dir="ltr" rowspan="1" style="vertical-align: middle;">支店情報</td>
                    <td class="s6" dir="ltr">支店名称</td>
                    <td class="s4" dir="ltr" colspan="3">
                        <input type="text" id="branch_code" name="branch_code" list="branch_list" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="支店名を入力または選択" disabled>
                        <datalist id="branch_list">
                        </datalist>
                    </td>
                </tr>

                <tr style="height: 20px">
                    <th id="1836490454R5_6" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">9</div>
                    </th>
                    <td class="s2"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R6" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">10</div>
                    </th>
                    <td class="s0"></td>
                    <td class="s1" dir="ltr" rowspan="3" style="vertical-align: middle;">連絡先情報</td>
                    <td class="s9" dir="ltr">携帯</td>
                    <td class="s4" dir="ltr" colspan="3">
                        <input type="tel" id="mobile_phone" name="mobile_phone" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="携帯番号を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R7" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">11</div>
                    </th>
                    <td class="s0"></td>
                    <td class="s9" dir="ltr">連絡先</td>
                    <td class="s4" dir="ltr" colspan="3">
                        <input type="tel" id="contact_phone" name="contact_phone" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="連絡先番号を入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R8" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">12</div>
                    </th>
                    <td class="s0"></td>
                    <td class="s9" dir="ltr">メールアドレス</td>
                    <td class="s4" dir="ltr" colspan="3">
                        <input type="email" id="email_address" name="email_address" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="メールアドレスを入力">
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R8_1" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">13</div>
                    </th>
                    <td class="s2"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                    <td class="s5"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R9" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">14</div>
                    </th>
                    <td class="s0"></td>
                    <td class="s1" dir="ltr" rowspan="4" style="vertical-align: middle;">その他</td>
                    <td class="s9" dir="ltr" rowspan="2" style="vertical-align: middle;">融資に対する姿勢</td>
                    <td class="s4" dir="ltr" colspan="3" rowspan="2" style="vertical-align: middle;">
                        <textarea id="lending_attitude" name="lending_attitude" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="融資に対する姿勢を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R10" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">15</div>
                    </th>
                    <td class="s2"></td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R11" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">16</div>
                    </th>
                    <td class="s0"></td>
                    <td class="s9" dir="ltr" rowspan="2" style="vertical-align: middle;">備考</td>
                    <td class="s4" dir="ltr" colspan="3" rowspan="2" style="vertical-align: middle;">
                        <textarea id="remarks" name="remarks" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="備考を入力"></textarea>
                    </td>
                </tr>
                <tr style="height: 20px">
                    <th id="1836490454R12" style="height: 20px;" class="row-headers-background">
                        <div class="row-header-wrapper" style="line-height: 20px">17</div>
                    </th>
                    <td class="s0"></td>
                </tr>
            </tbody>
        </table>
        <div style="text-align: left; margin-top: 20px;">
            <button type="submit">登録する</button>
            <div id="formMessage" style="margin-top: 10px; font-weight: bold;"></div>
        </div>
    </form>
</div>

        <script>
            // 銀行・支店データを保持するグローバル変数
            let bankData = [];
            let branchData = [];
            
            // ページ読み込み時の処理
            document.addEventListener('DOMContentLoaded', function() {
                const formMessage = document.getElementById('formMessage');
                formMessage.className = 'loading';
                formMessage.textContent = '初期化中...';
                
                // 管理者かどうかをチェック
                checkUserPermission();
                
                // 銀行一覧を取得してdatalistに設定
                google.script.run
                    .withSuccessHandler(function(banks) {
                        bankData = banks; // グローバル変数に保存
                        const datalistElement = document.getElementById('bank_list');
                        
                        // 既存のオプションをクリア
                        datalistElement.innerHTML = '';
                        
                        // 銀行オプションを追加
                        banks.forEach(function(bank) {
                            const option = document.createElement('option');
                            option.value = bank.name; // 表示用には銀行名を使用
                            option.setAttribute('data-code', bank.code); // 銀行コードをdata属性に保存
                            datalistElement.appendChild(option);
                        });
                        
                        formMessage.className = '';
                        formMessage.textContent = '';
                    })
                    .withFailureHandler(function(error) {
                        formMessage.className = 'error';
                        formMessage.textContent = '銀行一覧の読み込みエラー: ' + error.toString();
                    })
                    .getBanksFromAPI();
                
                // 支店フィールドの初期状態を設定
                document.getElementById('branch_code').disabled = true;
                document.getElementById('branch_code').placeholder = '先に銀行を選択してください';
                
                // 銀行入力フィールドの変更イベント
                document.getElementById('bank_code').addEventListener('input', function() {
                    const inputValue = this.value;
                    const selectedBank = bankData.find(bank => bank.name === inputValue);
                    
                    if (selectedBank) {
                        // 選択された銀行のコードをdata属性に保存
                        this.setAttribute('data-selected-code', selectedBank.code);
                        // 支店一覧を取得
                        loadBranches(selectedBank.code);
                    } else {
                        // マッチしない場合はdata属性をクリア
                        this.removeAttribute('data-selected-code');
                        // 支店一覧をクリア
                        clearBranches();
                    }
                });
                
                // 支店入力フィールドの変更イベント
                document.getElementById('branch_code').addEventListener('input', function() {
                    const inputValue = this.value;
                    const selectedBranch = branchData.find(branch => branch.name === inputValue);
                    
                    if (selectedBranch) {
                        // 選択された支店のコードをdata属性に保存
                        this.setAttribute('data-selected-code', selectedBranch.code);
                    } else {
                        // マッチしない場合はdata属性をクリア
                        this.removeAttribute('data-selected-code');
                    }
                });
            });
            
            // 管理者権限をチェックしてクライアント選択セクションを表示/非表示
            function checkUserPermission() {
                google.script.run
                    .withSuccessHandler(function(isAdmin) {
                        if (isAdmin) {
                            // 管理者の場合：クライアント選択セクションを表示
                            document.getElementById('clientRow').style.display = '';
                            loadClients();
                        } else {
                            // クライアントの場合：クライアント選択セクションを非表示
                            document.getElementById('clientRow').style.display = 'none';
                            // client_idのrequiredを削除
                            document.getElementById('client_id').required = false;
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.error('権限チェックエラー:', error);
                        // エラーの場合はデフォルトで非表示
                        document.getElementById('clientRow').style.display = 'none';
                        document.getElementById('client_id').required = false;
                    })
                    .isAdminUser();
            }
            
            // クライアント一覧を読み込む
            function loadClients() {
                google.script.run
                    .withSuccessHandler(function(clients) {
                        const clientSelect = document.getElementById('client_id');
                        
                        // 既存のオプションをクリア（最初の選択オプションは保持）
                        clientSelect.innerHTML = '<option value="">クライアントを選択してください</option>';
                        
                        // クライアントオプションを追加
                        clients.forEach(function(client) {
                            const option = document.createElement('option');
                            option.value = client.id;
                            option.textContent = client.name;
                            clientSelect.appendChild(option);
                        });
                    })
                    .withFailureHandler(function(error) {
                        const formMessage = document.getElementById('formMessage');
                        formMessage.className = 'error';
                        formMessage.textContent = 'クライアント一覧の読み込みエラー: ' + error.toString();
                    })
                    .fetchAllClients();
            }
            
            // 支店一覧を読み込む関数
            function loadBranches(bankCode) {
                const formMessage = document.getElementById('formMessage');
                formMessage.className = 'loading';
                formMessage.textContent = '支店一覧を読み込み中...';
                
                google.script.run
                    .withSuccessHandler(function(branches) {
                        branchData = branches; // グローバル変数に保存
                        const branchDatalist = document.getElementById('branch_list');
                        const branchInput = document.getElementById('branch_code');
                        
                        // 既存のオプションをクリア
                        branchDatalist.innerHTML = '';
                        
                        // 支店オプションを追加
                        branches.forEach(function(branch) {
                            const option = document.createElement('option');
                            option.value = branch.name; // 表示用には支店名を使用
                            option.setAttribute('data-code', branch.code); // 支店コードをdata属性に保存
                            branchDatalist.appendChild(option);
                        });
                        
                        // 支店入力フィールドを有効化
                        branchInput.disabled = false;
                        branchInput.placeholder = '支店名を入力または選択';
                        
                        formMessage.className = '';
                        formMessage.textContent = '';
                    })
                    .withFailureHandler(function(error) {
                        formMessage.className = 'error';
                        formMessage.textContent = '支店一覧の読み込みエラー: ' + error.toString();
                    })
                    .getBranchesFromAPI(bankCode);
            }
            
            // 支店一覧をクリアする関数
            function clearBranches() {
                branchData = [];
                const branchDatalist = document.getElementById('branch_list');
                const branchInput = document.getElementById('branch_code');
                
                branchDatalist.innerHTML = '';
                branchInput.disabled = true;
                branchInput.value = '';
                branchInput.placeholder = '先に銀行を選択してください';
            }



            function submitForm() {
                try {
                    // 銀行コードを取得
                    const bankInputElement = document.getElementById('bank_code');
                    const bankInputValue = bankInputElement ? bankInputElement.value : '';
                    let selectedBankCode = '';
                    
                    if (bankInputValue) {
                        // data-selected-code属性から銀行コードを取得
                        selectedBankCode = bankInputElement.getAttribute('data-selected-code') || '';
                        
                        // data-selected-codeがない場合は、銀行名から検索
                        if (!selectedBankCode) {
                            const selectedBank = bankData.find(bank => bank.name === bankInputValue);
                            selectedBankCode = selectedBank ? selectedBank.code : '';
                        }
                    }
                    
                    // 支店コードを取得
                    const branchInputElement = document.getElementById('branch_code');
                    const branchInputValue = branchInputElement ? branchInputElement.value : '';
                    let selectedBranchCode = '';
                    
                    if (branchInputValue) {
                        // data-selected-code属性から支店コードを取得
                        selectedBranchCode = branchInputElement.getAttribute('data-selected-code') || '';
                        
                        // data-selected-codeがない場合は、支店名から検索
                        if (!selectedBranchCode) {
                            const selectedBranch = branchData.find(branch => branch.name === branchInputValue);
                            selectedBranchCode = selectedBranch ? selectedBranch.code : '';
                        }
                    }
                    
                    const formData = {
                        representative_name: document.getElementById('representative_name') ? document.getElementById('representative_name').value : '',
                        bank_code: selectedBankCode,
                        branch_code: selectedBranchCode,
                        position: document.getElementById('position') ? document.getElementById('position').value : '',
                        mobile_phone: document.getElementById('mobile_phone') ? document.getElementById('mobile_phone').value : '',
                        contact_phone: document.getElementById('contact_phone') ? document.getElementById('contact_phone').value : '',
                        email_address: document.getElementById('email_address') ? document.getElementById('email_address').value : '',
                        lending_attitude: document.getElementById('lending_attitude') ? document.getElementById('lending_attitude').value : '',
                        remarks: document.getElementById('remarks') ? document.getElementById('remarks').value : '',
                        client_id: document.getElementById('client_id') ? document.getElementById('client_id').value : null
                    };
                    

                    
                    const formMessage = document.getElementById('formMessage');
                    
                    // 必須チェック
                    if (!formData.representative_name.trim()) {
                        formMessage.className = 'error';
                        formMessage.textContent = 'エラー: 担当者名は必須です';
                        return;
                    }
                    
                    // 管理者の場合はクライアント選択が必須
                    const clientRow = document.getElementById('clientRow');
                    if (clientRow && clientRow.style.display !== 'none') {
                        if (!formData.client_id) {
                            formMessage.className = 'error';
                            formMessage.textContent = 'エラー: クライアントの選択は必須です';
                            return;
                        }
                    }
                    
                    formMessage.className = 'loading';
                    formMessage.textContent = '送信中…';
                    
                    // 銀行・支店の詳細を順次取得
                    if (formData.bank_code) {
                        formMessage.className = 'loading';
                        formMessage.textContent = '銀行詳細を取得中...';
                        
                        google.script.run
                            .withSuccessHandler(function(bankDetail) {
                                // 銀行詳細データを追加
                                if (bankDetail) {
                                    formData.bank_name = bankDetail.name || '';
                                    formData.bank_name_half_kana = bankDetail.halfWidthKana || '';
                                    formData.bank_name_full_kana = bankDetail.fullWidthKana || '';
                                    formData.bank_name_hiragana = bankDetail.hiragana || '';
                                    formData.bank_business_type_code = bankDetail.businessTypeCode || '';
                                    formData.bank_business_type = bankDetail.businessType || '';
                                }
                                
                                // 支店詳細も取得する場合
                                if (formData.branch_code) {
                                    formMessage.className = 'loading';
                                    formMessage.textContent = '支店詳細を取得中...';
                                    
                                    google.script.run
                                        .withSuccessHandler(function(branchDetail) {
                                            // 支店詳細データを追加
                                            if (branchDetail) {
                                                formData.branch_name = branchDetail.name || '';
                                                formData.branch_name_half_kana = branchDetail.halfWidthKana || '';
                                                formData.branch_name_full_kana = branchDetail.fullWidthKana || '';
                                                formData.branch_name_hiragana = branchDetail.hiragana || '';
                                            }
                                            
                                            // 登録処理を実行
                                            registerBankRepresentative(formData);
                                        })
                                        .withFailureHandler(function(error) {
                                            formMessage.className = 'error';
                                            formMessage.textContent = '支店詳細の取得エラー: ' + error.toString();
                                        })
                                        .getBranchDetailFromAPI(formData.bank_code, formData.branch_code);
                                } else {
                                    // 支店が選択されていない場合は直接登録
                                    registerBankRepresentative(formData);
                                }
                            })
                            .withFailureHandler(function(error) {
                                formMessage.className = 'error';
                                formMessage.textContent = '銀行詳細の取得エラー: ' + error.toString();
                            })
                            .getBankDetailFromAPI(formData.bank_code);
                    } else {
                        // 銀行が選択されていない場合は直接登録
                        registerBankRepresentative(formData);
                    }
                } catch (error) {
                    const formMessage = document.getElementById('formMessage');
                    formMessage.className = 'error';
                    formMessage.textContent = 'スクリプトエラー: ' + error.message;
                }
            }

            function registerBankRepresentative(formData) {
                const formMessage = document.getElementById('formMessage');
                formMessage.className = 'loading';
                formMessage.textContent = '登録中...';
                
                google.script.run
                    .withSuccessHandler(res => {
                        if (res && res.success) {
                            formMessage.className = 'success';
                            formMessage.textContent = '登録完了: ID=' + (res.id || '');
                            setTimeout(() => {
                                google.script.host.close();
                            }, 1000);
                        } else {
                            formMessage.className = 'error';
                            formMessage.textContent = 'エラー: ' + (res ? res.message : '不明なエラー');
                        }
                    })
                    .withFailureHandler(err => {
                        formMessage.className = 'error';
                        formMessage.textContent = '通信エラー: ' + err.toString();
                    })
                    .registerBankRepresentative(formData);
            }
        </script>
    </body>
</html> 