/**
 * ISO形式の日時文字列をY-m-d H:i:s形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d H:i:s形式の日時文字列
 */
function formatDateTime(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * 物件一覧を取得（properties + short_properties + long_properties + clients結合）
 * @param {string=} loanBank 部分検索する借入銀行名。省略時は絞り込みなし
 * @param {string=} clientName 部分検索するクライアント名。省略時は絞り込みなし
 * @param {number=} loanRatio 借入割合での検索。省略時は絞り込みなし
 * @param {string=} progressStatus 部分検索する進捗状況。省略時は絞り込みなし
 * @param {boolean=} includeDeleted 削除済みデータも含めるかどうか。trueなら全て、falseなら削除されていないもののみ
 * @return {Object[]} Supabase から返ってきた物件配列
 */
function fetchMyProperties(loanBank, clientName, loanRatio, progressStatus, includeDeleted) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken) {
      const errorMsg = 'SUPABASE_ACCESS_TOKEN が設定されていません。';
      SpreadsheetApp.getUi().alert(
        '設定エラー',
        errorMsg + '\n\n管理者にお問い合わせください。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // PostgreSQL RPC関数を使用して物件情報を取得
    const payload = {
      search_loan_bank: loanBank || null,
      search_client_name: clientName || null,
      search_loan_ratio: loanRatio || null,
      search_progress_status: progressStatus || null,
      include_deleted: includeDeleted || false
    };

    const url = `${supabaseUrl}/rest/v1/rpc/get_properties_with_details`;
    const res = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      const errorMsg = '物件取得エラー: ' + (err.message || JSON.stringify(err));
      SpreadsheetApp.getUi().alert(
        '物件データ取得エラー',
        'Supabaseからの物件データ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    // 既にダイアログ表示済みでない場合のみ表示
    if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('物件取得エラー')) {
      SpreadsheetApp.getUi().alert(
        '予期しないエラー',
        '物件データの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
    throw error;
  }
}


/**
 * 検索ボタンに割り当てる関数。
 * D4:借入銀行、D5:借入割合、D6:クライアント名、C7~C10:進捗ステータスチェックボックス、D12:削除フラグで検索し、結果をシートに出力します
 */
function searchProperties() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const loanBank = sheet.getRange('D4').getDisplayValue().trim();          // 借入銀行
  const loanRatio = sheet.getRange('D5').getValue();                       // 借入割合（数値）
  const clientName = sheet.getRange('D6').getDisplayValue().trim();        // クライアント名
  
  // C7~C10のチェックボックスをチェックして、対応するD7~D10の文字列を進捗ステータスとして収集
  const progressStatuses = [];
  for (let i = 7; i <= 10; i++) {
    const isChecked = sheet.getRange(`C${i}`).getValue();
    if (isChecked === true || isChecked === 'TRUE' || isChecked === 1) {
      const statusText = sheet.getRange(`D${i}`).getDisplayValue().trim();
      if (statusText) {
        progressStatuses.push(statusText);
      }
    }
  }
  const progressStatus = progressStatuses.length > 0 ? progressStatuses.join(',') : '';
  
  const includeDeletedRaw = sheet.getRange('D12').getValue();              // 削除チェックボックスの値を取得
  // 文字列値をboolean型に安全に変換
  const includeDeleted = includeDeletedRaw === true || 
                        includeDeletedRaw === 'true' || 
                        includeDeletedRaw === 1 || 
                        includeDeletedRaw === '1' ||
                        includeDeletedRaw === 'yes' ||
                        includeDeletedRaw === 'on';
  
  const startRow = 19;
  const idCol = 1;   // A 列
  const dataStartCol = 3;  // C 列
     const numCols = 23;  // C〜Y の列数（property_display_search_grouping.mdの項目数）
  const lastRow = sheet.getMaxRows();

  try {
    const props = fetchMyProperties(loanBank, clientName, loanRatio || null, progressStatus, includeDeleted);

    // ─── クリア ───
    sheet.getRange(startRow, idCol, lastRow - (startRow - 1), 1).clearContent();
    sheet.getRange(startRow, dataStartCol, lastRow - (startRow - 1), numCols).clearContent();

    if (props.length === 0) {
      sheet.getRange(startRow, dataStartCol).setValue('該当なし');
      return;
    }

    // ─── 出力用配列を作成 ───
    // A列用：property_id
    const ids = props.map(r => [r.property_id]);

         // C〜Y列用：一覧表示項目（指定された順序）
    const values = props.map(r => [
      r.sales_method || '',                    // 1. 販売手法
      r.property_type || '',                   // 2. 物件種別
      r.address || '',                         // 3. 住所
      r.fixed_asset_tax_land || '',           // 4. 固定資産税評価額（土地）
      r.fixed_asset_tax_building || '',       // 5. 固定資産税評価額（建物）
      r.fixed_asset_tax_total || '',          // 6. 固定資産税評価額合計
      r.land_area_sqm || '',                   // 7. 土地面積（㎡）
      r.building_area_sqm || '',               // 8. 建物延べ面積(㎡）
      r.structure || '',                       // 9. 構造
      r.construction_date || '',               // 10. 築年月日
      r.building_age || '',                    // 11. 築年数
      r.purchase_price || '',                  // 12. 購入金額（税込）
      r.loan_bank_name || '',                  // 13. 借入銀行
      r.loan_amount || '',                     // 14. 借入金額
      r.loan_period || '',                     // 15. 借入期間
      r.loan_interest_rate || '',              // 16. 借入金利
      r.loan_fee_rate || '',                   // 17. 借入手数料率
      r.bank_collateral_value || '',          // 18. 銀行担保評価額
      r.bank_risk_amount || '',                // 19. 銀行リスク額
      r.loan_ratio || '',                      // 20. 借入割合
      r.progress_status || '',                 // 21. 進捗状況
      r.closing_date || '',                    // 22. 決済日
      r.client_name || '',                     // 23. クライアント名
    ]);

    // ─── 一括書き込み ───
    sheet
      .getRange(startRow, idCol, ids.length, 1)
      .setValues(ids);

    sheet
      .getRange(startRow, dataStartCol, values.length, numCols)
      .setValues(values);

  } catch (e) {
    // エラーダイアログを表示
    SpreadsheetApp.getUi().alert(
      '物件検索エラー',
      '物件データの取得に失敗しました。\n\nエラー詳細:\n' + e.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    
    // シートにもエラー表示
    sheet.getRange(startRow, dataStartCol).setValue('❌ エラー: ' + e.message);
  }
}


// 登録モーダルを表示
function createModalProperties() {
  const html = HtmlService
    .createHtmlOutputFromFile('CreateModalProperty')
    .setWidth(980)   // 横幅
    .setHeight(1020); // 高さ

  SpreadsheetApp
    .getUi()
    .showModalDialog(html, '物件登録モーダル');
}


// 更新モーダルを表示
function updateModalProperties() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const activeRange = sheet.getActiveRange();
  const selectedRow = activeRange.getRow();
  
  // A列からIDを取得
  const propertyId = sheet.getRange(selectedRow, 1).getValue();
  
  if (!propertyId) {
    SpreadsheetApp.getUi().alert(
      '選択エラー',
      '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return;
  }
  
  try {
    // Supabaseからデータを取得
    const propertyData = fetchPropertyById(propertyId);
    
    if (!propertyData) {
      SpreadsheetApp.getUi().alert(
        '物件が見つかりません',
        '指定されたIDの物件が見つかりません。\n\n物件ID: ' + propertyId,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }
    
    // 取得したデータを一時保存
    const docProps = PropertiesService.getDocumentProperties();
    docProps.setProperty('TEMP_PROPERTY_DATA', JSON.stringify(propertyData));
    
    const html = HtmlService
      .createHtmlOutputFromFile('UpdateModalProperty')
      .setWidth(980)   // 横幅
      .setHeight(1020); // 高さ

    SpreadsheetApp
      .getUi()
      .showModalDialog(html, '物件編集モーダル');
      
  } catch (error) {
    SpreadsheetApp.getUi().alert(
      '物件データ取得エラー',
      '物件データの取得に失敗しました。\n\nエラー詳細:\n' + error.message + '\n\n物件ID: ' + propertyId,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * IDで特定の物件データを取得
 * @param {string} propertyId 物件ID
 * @return {Object} 物件データ
 */
function fetchPropertyById(propertyId) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
  }

  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  // PostgreSQL RPC関数を使用して物件情報を取得
  const payload = {
    p_property_id: propertyId
  };

  const url = `${supabaseUrl}/rest/v1/rpc/get_property_by_id`;
  const res = UrlFetchApp.fetch(url, {
    method: 'POST',
    contentType: 'application/json',
    headers: {
      apikey: apiKey,
      Authorization: 'Bearer ' + accessToken
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  });

  if (res.getResponseCode() !== 200) {
    const err = JSON.parse(res.getContentText());
    throw new Error('物件データ取得エラー: ' + (err.message || JSON.stringify(err)));
  }
  
  const result = JSON.parse(res.getContentText());
  return result.length > 0 ? result[0] : null;
}

/**
 * HTML側から呼び出される関数：一時保存された物件データを取得
 * @return {Object} 物件データ
 */
function getTempPropertyData() {
  try {
    // 現在選択されているセルから物件IDを取得
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
    const activeRange = sheet.getActiveRange();
    const row = activeRange.getRow();
    
    // ヘッダー行（1-3行目）はスキップ
    if (row <= 3) {
      return null;
    }
    
    // A列（1列目）から物件IDを取得
    const propertyId = sheet.getRange(row, 1).getValue();
    
    if (propertyId && propertyId.toString().trim() !== '') {
      const result = propertyId.toString();
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.error('getTempPropertyData エラー:', error);
    return null;
  }
}

/**
 * 全てのクライアントを取得（セレクトボックス用）
 * @return {Object[]} クライアント配列
 */
function fetchAllClients() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
      throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // clientsテーブルから全てのクライアントを取得
    const url = `${supabaseUrl}/rest/v1/clients?select=id,name&order=name.asc`;
    const res = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('クライアント取得エラー: ' + (err.message || JSON.stringify(err)));
    }
    
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    throw error;
  }
}

/**
 * 選択セル行のA列IDを取得して物件を論理削除する関数
 */
function deleteSelectedProperty() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const activeRange = sheet.getActiveRange();
  const selectedRow = activeRange.getRow();
  
  // A列からIDを取得
  const propertyId = sheet.getRange(selectedRow, 1).getValue();
  
  if (!propertyId) {
    SpreadsheetApp.getUi().alert(
      '選択エラー',
      '選択された行にIDが見つかりません。\nA列にIDがある行を選択してください。',
      SpreadsheetApp.getUi().ButtonSet.OK
    );
    return;
  }
  
  // 削除確認ダイアログ
  const ui = SpreadsheetApp.getUi();
  const response = ui.alert(
    '物件削除の確認',
    '選択された物件を削除してもよろしいですか？\n\n物件ID: ' + propertyId + '\n\n※この操作は論理削除のため、後で復元可能です。',
    ui.ButtonSet.YES_NO
  );
  
  if (response !== ui.Button.YES) {
    return; // ユーザーがキャンセルした場合
  }
  
  try {
    const result = softDeleteProperty(propertyId);
    
    if (result.success) {
      ui.alert(
        '削除完了',
        '物件の削除が完了しました。\n\n物件ID: ' + propertyId,
        ui.ButtonSet.OK
      );
      
      // 削除後に検索結果を更新
      searchProperties();
    } else {
      ui.alert(
        '削除エラー',
        '物件の削除に失敗しました。\n\nエラー詳細:\n' + result.message + '\n\n物件ID: ' + propertyId,
        ui.ButtonSet.OK
      );
    }
    
  } catch (error) {
    ui.alert(
      '削除エラー',
      '物件の削除中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message + '\n\n物件ID: ' + propertyId,
      ui.ButtonSet.OK
    );
  }
}

/**
 * Supabaseの論理削除関数を呼び出す
 * @param {string} propertyId 削除する物件のID
 * @return {Object} 削除結果
 */
function softDeleteProperty(propertyId) {
  const docProps = PropertiesService.getDocumentProperties();
  const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
  const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
  
  if (!accessToken) {
    return { success: false, message: 'SUPABASE_ACCESS_TOKEN が設定されていません。' };
  }

  const scriptProps = PropertiesService.getScriptProperties();
  const supabaseUrl = scriptProps.getProperty('API_URL');
  const apiKey = scriptProps.getProperty('API_KEY');

  const res = UrlFetchApp.fetch(
    `${supabaseUrl}/rest/v1/rpc/soft_delete_property`,
    {
      method: 'POST',
      contentType: 'application/json',
      headers: {
        apikey: apiKey,
        Authorization: `Bearer ${accessToken}`
      },
      payload: JSON.stringify({
        property_uuid: propertyId
      }),
      muteHttpExceptions: true
    }
  );
  
  const code = res.getResponseCode();
  const responseText = res.getContentText();
  
  if (code === 200) {
    const result = JSON.parse(responseText);
    if (result === true) {
      return { success: true };
    } else {
      return { success: false, message: '削除に失敗しました。物件が見つからないか、既に削除済みの可能性があります。' };
    }
  } else {
    let errorMessage;
    try {
      const errorResult = JSON.parse(responseText);
      errorMessage = errorResult.message || JSON.stringify(errorResult);
    } catch (e) {
      errorMessage = responseText;
    }
    return { success: false, message: errorMessage };
  }
}

/**
 * 複数テーブル対応の物件更新関数
 * @param {Object} updateData フォームから送信された更新データ
 * @return {Object} 更新結果
 */
function getPropertyByIdForUpdate(propertyId) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
      console.error('getPropertyByIdForUpdate - SUPABASE_ACCESS_TOKEN が設定されていません');
      return { error: 'SUPABASE_ACCESS_TOKEN が設定されていません' };
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');



    // 更新用の詳細データ取得RPC関数を呼び出し（全詳細情報含む）
    const res = UrlFetchApp.fetch(
      `${supabaseUrl}/rest/v1/rpc/get_property_with_all_details`,
      {
        method: 'POST',
        contentType: 'application/json',
        headers: {
          apikey: apiKey,
          Authorization: `Bearer ${accessToken}`
        },
        payload: JSON.stringify({
          p_property_id: propertyId
        }),
        muteHttpExceptions: true
      }
    );

    const code = res.getResponseCode();
    const responseText = res.getContentText();
    


    if (code === 200) {
      const result = JSON.parse(responseText);

      
      // RPC関数はJSONを直接返すので、そのまま返す
      if (result === null || result === undefined) {
        console.error('RPC関数がnullを返しました');
        return { error: '指定された物件IDのデータが見つかりません: ' + propertyId };
      }
      
      return result;
    } else {
      console.error('物件データ取得エラー - レスポンスコード:', code);
      console.error('物件データ取得エラー - レスポンス内容:', responseText);
      
      // エラー情報を含むオブジェクトを返す
      let errorMessage = `HTTP ${code}エラー`;
      try {
        const errorResult = JSON.parse(responseText);
        errorMessage = errorResult.message || errorResult.hint || JSON.stringify(errorResult);
      } catch (e) {
        errorMessage = responseText;
      }
      
      return { error: `データベースエラー: ${errorMessage}` };
    }

  } catch (error) {
    console.error('getPropertyByIdForUpdate エラー:', error);
    return { error: `予期しないエラー: ${error.message}` };
  }
}

function updatePropertyWithDetails(updateData) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken) {
      return { success: false, message: 'SUPABASE_ACCESS_TOKEN が設定されていません。' };
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');



    // RPC関数のパラメータを構築
    const rpcParams = Object.assign({}, updateData.propertyData);
    
    // 短期・長期物件データと相談銀行データをJSONBとして追加
    if (updateData.shortPropertyData) {
      rpcParams.p_short_property_data = updateData.shortPropertyData;
    }
    
    if (updateData.longPropertyData) {
      rpcParams.p_long_property_data = updateData.longPropertyData;
    }
    
    if (updateData.consultationBanks && updateData.consultationBanks.length > 0) {
      rpcParams.p_consultation_banks = updateData.consultationBanks;
    }

    // パラメータ名をRPC関数に合わせて調整
    const rpcPayload = {};
    Object.keys(rpcParams).forEach(key => {
      const rpcKey = key.startsWith('p_') ? key : `p_${key}`;
      rpcPayload[rpcKey] = rpcParams[key];
    });



                    // 更新用RPC関数を呼び出し
                const res = UrlFetchApp.fetch(
                  `${supabaseUrl}/rest/v1/rpc/update_property_with_details`,
      {
        method: 'POST',
        contentType: 'application/json',
        headers: {
          apikey: apiKey,
          Authorization: `Bearer ${accessToken}`
        },
        payload: JSON.stringify(rpcPayload),
        muteHttpExceptions: true
      }
    );

    const code = res.getResponseCode();
    const responseText = res.getContentText();
    


    if (code === 200) {
      const result = JSON.parse(responseText);
      
      if (result.success || result === true) {
        // 成功時は検索結果を更新
        searchProperties();
        return {
          success: true,
          property_id: updateData.propertyData.id,
          message: result.message || '物件データが正常に更新されました'
        };
      } else {
        return {
          success: false,
          message: result.message || '更新に失敗しました'
        };
      }
    } else {
      let errorMessage;
      try {
        const errorResult = JSON.parse(responseText);
        errorMessage = errorResult.message || JSON.stringify(errorResult);
      } catch (e) {
        errorMessage = responseText;
      }
      return {
        success: false,
        message: `更新エラー (${code}): ${errorMessage}`
      };
    }

  } catch (error) {
    console.error('updatePropertyWithDetails エラー:', error);
    return {
      success: false,
      message: `予期しないエラー: ${error.message}`
    };
  }
}

/**
 * 複数テーブル対応の物件登録関数
 * @param {Object} registrationData フォームから送信された全データ
 * @return {Object} 登録結果
 */
function registerPropertyWithDetails(registrationData) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken) {
      return { success: false, message: 'SUPABASE_ACCESS_TOKEN が設定されていません。' };
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');



    // 新しいRPC関数の仕様に合わせてパラメータを構築
    const rpcPayload = {
      // プロパティ基本情報
      p_type: registrationData.propertyData.type,
      p_purchase_date: registrationData.propertyData.purchase_date,
      p_address: registrationData.propertyData.address,
      p_land_area_sqm: registrationData.propertyData.land_area_sqm,
      p_fixed_asset_tax_land: registrationData.propertyData.fixed_asset_tax_land,
      p_building_area_sqm: registrationData.propertyData.building_area_sqm,
      p_structure: registrationData.propertyData.structure,
      p_construction_date: registrationData.propertyData.construction_date,
      p_building_age: registrationData.propertyData.building_age,
      p_fixed_asset_tax_building: registrationData.propertyData.fixed_asset_tax_building,
      p_purchase_price: registrationData.propertyData.purchase_price,
      
      // 融資情報
      p_working_capital_id: registrationData.propertyData.working_capital_id,
      p_loan_amount: registrationData.propertyData.loan_amount,
      p_loan_period: registrationData.propertyData.loan_period,
      p_loan_interest_rate: registrationData.propertyData.loan_interest_rate,
      p_loan_fee_rate: registrationData.propertyData.loan_fee_rate,
      
      // 売却予定情報
      p_planned_sale_amount: registrationData.propertyData.planned_sale_amount,
      p_planned_sale_month: registrationData.propertyData.planned_sale_month,
      
      // 銀行評価情報
      p_bank_collateral_value: registrationData.propertyData.bank_collateral_value,
      p_bank_risk_amount: registrationData.propertyData.bank_risk_amount,
      
      
      // その他情報
      p_remarks: registrationData.propertyData.remarks,
      p_loan_ratio: registrationData.propertyData.loan_ratio,
      p_fixed_asset_tax_total: registrationData.propertyData.fixed_asset_tax_total,
      p_progress_status: registrationData.propertyData.progress_status,
      p_closing_date: registrationData.propertyData.closing_date,
      
      // JSON データ
      p_short_property_data: registrationData.shortPropertyData,
      p_long_property_data: registrationData.longPropertyData,
      p_consultation_banks: registrationData.consultationBanks
    };



    // RPC関数を呼び出し
    const res = UrlFetchApp.fetch(
      `${supabaseUrl}/rest/v1/rpc/create_property_with_details`,
      {
        method: 'POST',
        contentType: 'application/json',
        headers: {
          apikey: apiKey,
          Authorization: `Bearer ${accessToken}`
        },
        payload: JSON.stringify(rpcPayload),
        muteHttpExceptions: true
      }
    );

    const code = res.getResponseCode();
    const responseText = res.getContentText();
    


    if (code === 200) {
      const result = JSON.parse(responseText);
      if (result.success) {
        // 成功時は検索結果を更新
        searchProperties();
        return {
          success: true,
          property_id: result.property_id,
          message: result.message || '物件データが正常に登録されました'
        };
      } else {
        return {
          success: false,
          message: result.message || '登録に失敗しました'
        };
      }
    } else {
      let errorMessage;
      try {
        const errorResult = JSON.parse(responseText);
        errorMessage = errorResult.message || JSON.stringify(errorResult);
      } catch (e) {
        errorMessage = responseText;
      }
      return {
        success: false,
        message: `登録エラー (${code}): ${errorMessage}`
      };
    }

  } catch (error) {
    console.error('registerPropertyWithDetails エラー:', error);
    return {
      success: false,
      message: `予期しないエラー: ${error.message}`
    };
  }
}

/**
 * working_capitalテーブルから銀行情報を取得
 * @return {Object[]} working_capital配列
 */
function fetchWorkingCapitalBanks() {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    
    if (!accessToken) {
      throw new Error('SUPABASE_ACCESS_TOKEN が設定されていません。');
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // working_capitalテーブルから銀行情報を取得
    const url = `${supabaseUrl}/rest/v1/working_capital?select=id,bank_name&order=bank_name.asc`;
    const res = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      throw new Error('運転資金データ取得エラー: ' + (err.message || JSON.stringify(err)));
    }
    
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    throw error;
  }
}

/**
 * 銀行名から対応するworking_capital_idを取得
 * @param {string} bankName 銀行名
 * @return {string|null} working_capital_id
 */
function getWorkingCapitalIdByBankName(bankName) {
  try {
    if (!bankName || bankName.trim() === '') {
      return null;
    }

    const workingCapitals = fetchWorkingCapitalBanks();
    const found = workingCapitals.find(wc => wc.bank_name === bankName.trim());
    
    return found ? found.id : null;
    
  } catch (error) {
    console.error('getWorkingCapitalIdByBankName エラー:', error);
    return null;
  }
}