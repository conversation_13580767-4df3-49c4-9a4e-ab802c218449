/**
 * ISO形式の日時文字列をY-m-d H:i:s形式に変換
 * @param {string} isoString ISO形式の日時文字列
 * @return {string} Y-m-d H:i:s形式の日時文字列
 */
function formatDateTime(isoString) {
  if (!isoString) return '';
  
  try {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    return isoString; // 変換に失敗した場合は元の値を返す
  }
}

/**
 * 保有物件一覧を作成する関数
 * ステータス毎（取得予定、取得済み、販売中、売却済み）に分けて出力
 * searchPropertiesと同様の検索条件を適用
 */
function createPropertyOverview() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
    const term = sheet.getRange('D4').getDisplayValue().trim();
    const address = sheet.getRange('D5').getDisplayValue().trim();
    const includeDeleted = sheet.getRange('D11').getValue(); // 削除チェックボックスの値を取得
    
    // C6~C9のチェックボックスの状態を確認し、trueの場合はD列の値を取得
    const statusFilters = [];
    for (let row = 6; row <= 9; row++) {
      const checkbox = sheet.getRange(`C${row}`).getValue();
      if (checkbox === true) {
        const statusValue = sheet.getRange(`D${row}`).getDisplayValue().trim();
        if (statusValue) {
          statusFilters.push(statusValue);
        }
      }
    }
    
    // 物件データを取得（検索条件を適用）
    const properties = fetchMyPropertiesForOverview(term, address, includeDeleted, statusFilters);
    
    if (properties.length === 0) {
      SpreadsheetApp.getUi().alert(
        '物件データなし',
        '表示する物件データがありません。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      return;
    }
    
    // 新しいシートを作成または既存シートを取得
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let overviewSheet;
    
    try {
      overviewSheet = spreadsheet.getSheetByName('保有物件一覧');
      // 既存シートがある場合はクリア
      overviewSheet.clear();
    } catch (e) {
      // シートが存在しない場合は新規作成
      overviewSheet = spreadsheet.insertSheet('保有物件一覧');
    }
    
    // ステータス毎に物件を分類
    const statusCategories = {
      '取得予定': [],
      '取得済み': [],
      '販売中': [],
      '売却済み': []
    };
    
    // 物件をステータス毎に分類
    properties.forEach(property => {
      const status = property.status || '';
      if (statusCategories.hasOwnProperty(status)) {
        statusCategories[status].push(property);
      } else {
        // 未分類のステータスは「取得予定」に分類
        statusCategories['取得予定'].push(property);
      }
    });
    
    let currentRow = 1;
    
    // ステータス毎に出力
    Object.keys(statusCategories).forEach(status => {
      const statusProperties = statusCategories[status];
      
      if (statusProperties.length > 0) {
        // 取得価格の合計を計算
        const totalAcquisitionPrice = statusProperties.reduce((sum, property) => {
          return sum + (property.acquisition_price || 0);
        }, 0);
        
        // ステータス名と合計金額のヘッダー（1行目）
        overviewSheet.getRange(currentRow, 1).setValue(`${status}：取得価格合計 ${totalAcquisitionPrice.toLocaleString()}万円`);
        overviewSheet.getRange(currentRow, 1, 1, 14).merge();
        overviewSheet.getRange(currentRow, 1).setFontWeight('bold');
        overviewSheet.getRange(currentRow, 1).setBackground('#e6f3ff');
        currentRow++;
        
        // フィールドヘッダー（2行目）
        const headers = [
          '物件名', '物件仕様', '住所', '土地面積(㎡)', '建物面積(㎡)', '構造', '築年数',
          '銀行名', '取得価格(万円)', '取得月', '予定売却金額(万円)', '予定売却月', '実売却金額(万円)', '実売却月'
        ];
        
        overviewSheet.getRange(currentRow, 1, 1, headers.length).setValues([headers]);
        overviewSheet.getRange(currentRow, 1, 1, headers.length).setFontWeight('bold');
        overviewSheet.getRange(currentRow, 1, 1, headers.length).setBackground('#f0f0f0');
        currentRow++;
        
        // データ行を出力
        const dataRows = statusProperties.map(property => [
          property.property_name || '',
          property.property_spec || '',
          property.address || '',
          property.land_area_sqm || 0,
          property.building_area_sqm || 0,
          property.structure || '',
          property.year_built || '',
          property.bank_name || '',
          property.acquisition_price || 0,
          property.acquisition_month || '',
          property.planned_sale_price || 0,
          property.planned_sale_month || '',
          property.actual_sale_price || 0,
          property.actual_sale_month || ''
        ]);
        
        if (dataRows.length > 0) {
          overviewSheet.getRange(currentRow, 1, dataRows.length, headers.length).setValues(dataRows);
          currentRow += dataRows.length;
        }
        
        // ステータス間の空行
        currentRow += 2;
      }
    });
    
    // 列幅の調整（セル内容の最大文字数に基づく）
    adjustColumnWidthsByContent(overviewSheet, 14, currentRow - 1);
    
    // 完了メッセージとPDF出力オプション
    const ui = SpreadsheetApp.getUi();
    const response = ui.alert(
      '一覧作成完了',
      '「保有物件一覧」シートに物件一覧を作成しました。\n\n総物件数: ' + properties.length + '件\n\nPDFファイルも作成しますか？',
      ui.ButtonSet.YES_NO
    );
    
    if (response === ui.Button.YES) {
      // PDF出力処理を実行
      try {
        const pdfUrl = createUrlForPdf(SpreadsheetApp.getActiveSpreadsheet(), '保有物件一覧');
        
        // PDFを直接別タブで開く
        openPdfDirectly(pdfUrl);
        
      } catch (pdfError) {
        ui.alert(
          'PDF出力エラー',
          'PDF出力の準備中にエラーが発生しました。\n\nエラー詳細:\n' + pdfError.message,
          ui.ButtonSet.OK
        );
      }
    }
    
  } catch (error) {
    SpreadsheetApp.getUi().alert(
      '一覧作成エラー',
      '保有物件一覧の作成に失敗しました。\n\nエラー詳細:\n' + error.message,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  }
}

/**
 * PDF出力用のURLを作成する
 * @param {GoogleAppsScript.Spreadsheet.Spreadsheet} spreadsheet 出力対象のスプレッドシート
 * @param {string} sheetName 出力対象のシート名
 * @return {string} PDF出力用のURL
 */
function createUrlForPdf(spreadsheet, sheetName) {
  const params = {
    'exportFormat': 'pdf',
    'format': 'pdf',
    'gid': spreadsheet.getSheetByName(sheetName).getSheetId(), // 指定されたシート名のIDを取得
    'size': 'A4', // 用紙サイズ:A4
    'portrait': false, // 用紙向き:横向き
    'fitw': true, // 幅を用紙に合わせる
    'horizontal_alignment': 'CENTER', // 水平方向:中央
    'gridlines': false, // グリッドライン:非表示
  }
  const query = Object.keys(params).map(function(key) {
    return encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
  }).join('&');
  return `https://docs.google.com/spreadsheets/d/${spreadsheet.getId()}/export?${query}`;
}

/**
 * PDF URLをダイアログに表示してコピー可能にする
 * @param {string} pdfUrl PDF出力用のURL
 */
function openPdfDirectly(pdfUrl) {
  // 一時的にpdfUrlを保存
  PropertiesService.getDocumentProperties().setProperty('TEMP_PDF_URL', pdfUrl);
  
  // HTMLダイアログを表示
  const html = HtmlService.createHtmlOutputFromFile('PdfLinkDialog')
    .setWidth(500)
    .setHeight(200);
  
  SpreadsheetApp.getUi().showModalDialog(html, 'PDF URL');
}

/**
 * HTMLから呼び出される関数：保存されたPDF URLを取得
 * @return {string} PDF URL
 */
function getTempPdfUrl() {
  const pdfUrl = PropertiesService.getDocumentProperties().getProperty('TEMP_PDF_URL');
  // 取得後は削除
  PropertiesService.getDocumentProperties().deleteProperty('TEMP_PDF_URL');
  return pdfUrl;
}

/**
 * セル内容の最大文字数に基づいて列幅を調整する
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 対象のシート
 * @param {number} numCols 調整する列数
 * @param {number} numRows 調整対象の行数
 */
function adjustColumnWidthsByContent(sheet, numCols, numRows) {
  try {
    // 各列の最大文字数を計算
    for (let col = 1; col <= numCols; col++) {
      let maxLength = 0;
      
      // 該当列の全セルの文字数をチェック
      for (let row = 1; row <= numRows; row++) {
        const cellValue = sheet.getRange(row, col).getDisplayValue();
        if (cellValue) {
          // 日本語文字（全角）と英数字（半角）を考慮した文字数計算
          const length = calculateDisplayLength(cellValue);
          maxLength = Math.max(maxLength, length);
        }
      }
      
      // 最小幅と最大幅を設定（ピクセル単位）
      const minWidth = 80;  // 最小80ピクセル
      const maxWidth = 300; // 最大300ピクセル
      
      // 文字数に基づく幅計算（1文字あたり約8ピクセル + 余白20ピクセル）
      let calculatedWidth = Math.max(minWidth, maxLength * 8 + 20);
      calculatedWidth = Math.min(calculatedWidth, maxWidth);
      
      // 列幅を設定
      sheet.setColumnWidth(col, calculatedWidth);
    }
  } catch (error) {
    console.error('列幅調整エラー:', error.message);
    // エラーが発生した場合は従来の自動調整にフォールバック
    for (let col = 1; col <= numCols; col++) {
      sheet.autoResizeColumn(col);
    }
  }
}

/**
 * 文字列の表示幅を計算する（全角文字は2、半角文字は1として計算）
 * @param {string} text 計算対象の文字列
 * @return {number} 表示幅
 */
function calculateDisplayLength(text) {
  if (!text) return 0;
  
  let length = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charAt(i);
    // 全角文字（ひらがな、カタカナ、漢字、全角記号など）は2文字分
    if (char.match(/[^\x00-\x7F]/)) {
      length += 2;
    } else {
      // 半角文字は1文字分
      length += 1;
    }
  }
  return length;
}

/**
 * 自身の client_id に紐づく properties テーブルのデータを取得して返す（保有物件一覧用）
 * @param {string=} propertyName 部分検索する property_name。省略時は絞り込みなし
 * @param {string=} address 部分検索する address。省略時は絞り込みなし
 * @param {boolean=} includeDeleted 削除済みデータも含めるかどうか。trueなら全て、falseなら削除されていないもののみ
 * @param {string[]=} statusFilters statusで絞り込むための配列。省略時は絞り込みなし
 * @return {Object[]} Supabase から返ってきたプロパティ配列
 */
function fetchMyPropertiesForOverview(propertyName, address, includeDeleted, statusFilters) {
  try {
    const docProps = PropertiesService.getDocumentProperties();
    const accessToken = docProps.getProperty('SUPABASE_ACCESS_TOKEN');
    const clientId = docProps.getProperty('SUPABASE_CLIENT_ID');
    
    if (!accessToken || !clientId) {
      const errorMsg = 'SUPABASE_ACCESS_TOKEN または SUPABASE_CLIENT_ID が設定されていません。';
      SpreadsheetApp.getUi().alert(
        '設定エラー',
        errorMsg + '\n\n管理者にお問い合わせください。',
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }

    const scriptProps = PropertiesService.getScriptProperties();
    const supabaseUrl = scriptProps.getProperty('API_URL');
    const apiKey = scriptProps.getProperty('API_KEY');

    // 部分検索フィルター（ilike + %ワイルドカード）
    let filter = `client_id=eq.${clientId}`;
    if (propertyName) {
      const term = encodeURIComponent(propertyName);
      filter += `&property_name=ilike.%25${term}%25`;
    }
    
    // 住所での部分検索フィルターを追加
    if (address) {
      const addressTerm = encodeURIComponent(address);
      filter += `&address=ilike.%25${addressTerm}%25`;
    }
    
    // statusによるフィルタリング
    if (statusFilters && statusFilters.length > 0) {
      if (statusFilters.length === 1) {
        // 単一の値の場合
        filter += `&status=eq.${encodeURIComponent(statusFilters[0])}`;
      } else {
        // 複数の値の場合はin演算子を使用
        const statusList = statusFilters.map(status => encodeURIComponent(status)).join(',');
        filter += `&status=in.(${statusList})`;
      }
    }
    
    // 削除フラグによるフィルタリング
    if (!includeDeleted) {
      // 削除されていないもののみ（deleted_atがnull）
      filter += `&deleted_at=is.null`;
    }
    // includeDeletedがtrueの場合は何も追加しない（全て取得）

    // ステータス順、物件名順でソート
    filter += `&order=status.asc,property_name.asc`;

    const url = `${supabaseUrl}/rest/v1/properties?select=*&${filter}`;
    const res = UrlFetchApp.fetch(url, {
      method: 'get',
      headers: {
        apikey: apiKey,
        Authorization: 'Bearer ' + accessToken
      },
      muteHttpExceptions: true
    });

    if (res.getResponseCode() !== 200) {
      const err = JSON.parse(res.getContentText());
      const errorMsg = 'properties 取得エラー: ' + (err.message || JSON.stringify(err));
      SpreadsheetApp.getUi().alert(
        '物件データ取得エラー',
        'Supabaseからの物件データ取得に失敗しました。\n\nエラー詳細:\n' + errorMsg,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
      throw new Error(errorMsg);
    }
    
    return JSON.parse(res.getContentText());
    
  } catch (error) {
    // 既にダイアログ表示済みでない場合のみ表示
    if (!error.message.includes('SUPABASE_ACCESS_TOKEN') && !error.message.includes('properties 取得エラー')) {
      SpreadsheetApp.getUi().alert(
        '予期しないエラー',
        '物件データの取得中に予期しないエラーが発生しました。\n\nエラー詳細:\n' + error.message,
        SpreadsheetApp.getUi().ButtonSet.OK
      );
    }
    throw error;
  }
} 