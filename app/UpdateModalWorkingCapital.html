<!DOCTYPE html>
<html lang="ja">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s14{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:center;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s0{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:right;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:right;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Aptos Narrow",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:2px 3px 2px 3px;}.waffle tbody th{visibility: hidden;width: 1px;display: block;}</style>

    <style type="text/css">
        body .ritz .waffle td:has(input, select, textarea) {
            padding: 0 !important;
        }
        body .ritz .waffle input,
        body .ritz .waffle select,
        body .ritz .waffle textarea {
            min-height: 26px;
            display: flex;
            line-height: 1.5;
            box-sizing: border-box;
            padding: 0 8px;
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.15);
            background-color: #fff !important;
        }
        
        /* 見出し系のセルの背景色を変更 */
        body .ritz .waffle .s1,
        body .ritz .waffle .s3,
        body .ritz .waffle .s6,
        body .ritz .waffle .s9 {
            background-color: #efefef !important;
        }
        
        /* datalist用のスタイル調整 */
        input[list] {
            cursor: pointer;
        }
        
        /* disabled状態のスタイル */
        input:disabled {
            background-color: #f8f8f8 !important;
            color: #999 !important;
            cursor: not-allowed !important;
        }
        
        /* ボタンの統一スタイル */
        input[type="submit"], button[type="submit"], button {
            background-color: #0A4484;
            color: #FFFFFF;
            font-size: 14px;
            font-weight: normal;
            border-radius: 3px;
            border: none;
            padding: 8px 20px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.2s ease;
        }
        
        /* ホバー時のスタイル */
        input[type="submit"]:hover, button[type="submit"]:hover, button:hover {
            background-color: #083a6f;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        /* クリック時のスタイル */
        input[type="submit"]:active, button[type="submit"]:active, button:active {
            background-color: #062a52;
            box-shadow: 0 1px 4px rgba(0,0,0,0.3);
            transform: translateY(0);
        }
        
        /* ローディング表示用のスタイル */
        #formMessage {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }
        #formMessage::before {
            content: '';
            width: 16px;
            height: 16px;
            margin-bottom: 8px;
            border: 2px solid #CCC;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        #formMessage::after {
            content: '';
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.5);
            z-index: -1;
        }
        #formMessage:empty {
            display: none !important;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="ritz grid-container" dir="ltr">
        <form id="workingCapitalForm" onsubmit="submitForm(); return false;">
            <input type="hidden" id="working_capital_id" name="working_capital_id">
        <table class="waffle" cellspacing="0" cellpadding="0">
            <thead>
                <tr>
                    <th class="row-header freezebar-origin-ltr"></th>
                    <th id="1836490454C0" style="width:2px;" class="column-headers-background"></th>
                    <th id="1836490454C1" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C2" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C3" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C4" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C5" style="width:100px;" class="column-headers-background"></th>
                    <th id="1836490454C6" style="width:100px;" class="column-headers-background"></th>
                </tr>
            </thead>
            <tbody>
                    <tr style="height: 20px">
                        <th id="1836490454R0" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">1</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" colspan="5">運転資金情報編集フォーム</td>
                    </tr>
                                    <tr style="height: 20px">
                        <th id="1836490454R1" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">2</div>
                        </th>
                        <td class="s2"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                    </tr>
                    <tr style="height: 20px; display: none;" id="clientRow">
                        <th id="1836490454R2_0" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">3</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr">クライアント</td>
                        <td class="s6" dir="ltr">クライアント名<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <select id="client_id" name="client_id" required style="width: 100%; border: none; background: transparent; text-align: center;">
                                <option value="">クライアントを選択してください</option>
                            </select>
                        </td>
                    </tr>
                    <!-- 銀行情報 -->
                    <tr style="height: 20px">
                        <th id="1836490454R3" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">4</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" rowspan="1" style="vertical-align: middle;">銀行情報</td>
                        <td class="s6" dir="ltr">銀行名称</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="text" id="bank_code" name="bank_code" list="bank_list" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="銀行名を入力または選択">
                            <datalist id="bank_list">
                            </datalist>
                        </td>
                    </tr>


                    <tr style="height: 20px">
                        <th id="1836490454R5_6" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">6</div>
                        </th>
                        <td class="s2"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                    </tr>
                    <!-- 借入情報 -->
                    <tr style="height: 20px">
                        <th id="1836490454R6" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">7</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" rowspan="7" style="vertical-align: middle;">借入情報</td>
                        <td class="s6" dir="ltr">当初借入金額<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="number" id="initial_loan_amount" name="initial_loan_amount" required min="0" step="1" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="金額を入力（円）">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R7" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">8</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">現在借入残高<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="number" id="current_loan_balance" name="current_loan_balance" required min="0" step="1" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="金額を入力（円）">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R8" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">9</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">元本返済額<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="number" id="principal_repayment" name="principal_repayment" required min="0" step="1" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="金額を入力（円）">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R9" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">10</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">借入日<span style="color: red;">*</span></td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="date" id="loan_date" name="loan_date" required style="width: 100%; border: none; background: transparent; text-align: center;">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R10" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">11</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">返済期日</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="date" id="repayment_due_date" name="repayment_due_date" style="width: 100%; border: none; background: transparent; text-align: center;">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R11" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">12</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">借入形態</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <select id="loan_type" name="loan_type" style="width: 100%; border: none; background: transparent; text-align: center;">
                                <option value="">借入形態を選択</option>
                                <option value="プロパー">プロパー</option>
                                <option value="保証協会">保証協会</option>
                            </select>
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R12" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">13</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s9" dir="ltr">借入金利</td>
                        <td class="s4" dir="ltr" colspan="3">
                            <input type="number" id="loan_interest_rate" name="loan_interest_rate" min="0" max="100" step="0.01" style="width: 100%; border: none; background: transparent; text-align: center;" placeholder="金利を入力（%）">
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R12_1" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">14</div>
                        </th>
                        <td class="s2"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                        <td class="s5"></td>
                    </tr>
                    <!-- その他 -->
                    <tr style="height: 20px">
                        <th id="1836490454R13" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">15</div>
                        </th>
                        <td class="s0"></td>
                        <td class="s1" dir="ltr" rowspan="2" style="vertical-align: middle;">その他</td>
                        <td class="s9" dir="ltr" rowspan="2" style="vertical-align: middle;">備考</td>
                        <td class="s4" dir="ltr" colspan="3" rowspan="2" style="vertical-align: middle;">
                            <textarea id="remarks" name="remarks" style="width: 100%; border: none; background: transparent; text-align: left; resize: vertical; min-height: 40px;" placeholder="備考を入力"></textarea>
                        </td>
                    </tr>
                    <tr style="height: 20px">
                        <th id="1836490454R14" style="height: 20px;" class="row-headers-background">
                            <div class="row-header-wrapper" style="line-height: 20px">16</div>
                        </th>
                        <td class="s0"></td>
                    </tr>
            </tbody>
        </table>
        <div style="text-align: left; margin-top: 20px;">
            <button type="submit">更新する</button>
            <div id="formMessage" style="margin-top: 10px; font-weight: bold;"></div>
        </div>
    </form>
</div>

        <script>
            // 銀行データを保持するグローバル変数
            let bankData = [];
            let originalData = null;
            
            // ページ読み込み時の処理
            document.addEventListener('DOMContentLoaded', function() {
                document.getElementById('formMessage').textContent = 'データ読み込み中...';
                
                // 管理者かどうかをチェック
                checkUserPermission();
                
                // 銀行一覧と運転資金データを並行して取得
                Promise.all([
                    new Promise((resolve, reject) => {
                        google.script.run
                            .withSuccessHandler(resolve)
                            .withFailureHandler(reject)
                            .getBanksFromAPI();
                    }),
                    new Promise((resolve, reject) => {
                        google.script.run
                            .withSuccessHandler(resolve)
                            .withFailureHandler(reject)
                            .getTempWorkingCapitalData();
                    })
                ]).then(([banks, data]) => {
                    bankData = banks;
                    originalData = data;
                    
                    // 銀行一覧をdatalistに設定
                    const datalistElement = document.getElementById('bank_list');
                    datalistElement.innerHTML = '';
                    
                    banks.forEach(function(bank) {
                        const option = document.createElement('option');
                        option.value = bank.name;
                        option.setAttribute('data-code', bank.code);
                        datalistElement.appendChild(option);
                    });
                    
                    // 運転資金データを設定
                    if (data) {
                        document.getElementById('working_capital_id').value = data.id || '';
                        document.getElementById('initial_loan_amount').value = data.initial_loan_amount || '';
                        document.getElementById('current_loan_balance').value = data.current_loan_balance || '';
                        document.getElementById('principal_repayment').value = data.principal_repayment || '';
                        document.getElementById('loan_date').value = data.loan_date || '';
                        document.getElementById('repayment_due_date').value = data.repayment_due_date || '';
                        document.getElementById('loan_type').value = data.loan_type || '';
                        document.getElementById('loan_interest_rate').value = data.loan_interest_rate || '';
                        document.getElementById('remarks').value = data.remarks || '';
                        
                        // 銀行情報を設定
                        if (data.bank_name) {
                            document.getElementById('bank_code').value = data.bank_name;
                            document.getElementById('bank_code').setAttribute('data-selected-code', data.bank_code || '');
                        }
                        
                        document.getElementById('formMessage').textContent = 'データ読み込み完了';
                        setTimeout(() => {
                            document.getElementById('formMessage').textContent = '';
                        }, 1000);
                    } else {
                        document.getElementById('formMessage').textContent = 'データが見つかりません';
                    }
                }).catch(error => {
                    document.getElementById('formMessage').textContent = 'データ読み込みエラー: ' + error.toString();
                });
                

                
                // 銀行入力フィールドの変更イベント
                document.getElementById('bank_code').addEventListener('input', function() {
                    const inputValue = this.value;
                    const selectedBank = bankData.find(bank => bank.name === inputValue);
                    
                    if (selectedBank) {
                        this.setAttribute('data-selected-code', selectedBank.code);
                    } else {
                        this.removeAttribute('data-selected-code');
                    }
                });
                

            });
            

            

            

            
            // 管理者権限をチェックしてクライアント選択セクションを表示/非表示
            function checkUserPermission() {
                google.script.run
                    .withSuccessHandler(function(isAdmin) {
                        if (isAdmin) {
                            // 管理者の場合：クライアント選択セクションを表示
                            document.getElementById('clientRow').style.display = '';
                            loadClients();
                        } else {
                            // クライアントの場合：クライアント選択セクションを非表示
                            document.getElementById('clientRow').style.display = 'none';
                            // client_idのrequiredを削除
                            document.getElementById('client_id').required = false;
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.error('権限チェックエラー:', error);
                        // エラーの場合はデフォルトで非表示
                        document.getElementById('clientRow').style.display = 'none';
                        document.getElementById('client_id').required = false;
                    })
                    .isAdminUser();
            }
            
            // クライアント一覧を読み込む
            function loadClients() {
                google.script.run
                    .withSuccessHandler(function(clients) {
                        const clientSelect = document.getElementById('client_id');
                        
                        // 既存のオプションをクリア（最初の選択オプションは保持）
                        clientSelect.innerHTML = '<option value="">クライアントを選択してください</option>';
                        
                        // クライアントオプションを追加
                        clients.forEach(function(client) {
                            const option = document.createElement('option');
                            option.value = client.id;
                            option.textContent = client.name;
                            clientSelect.appendChild(option);
                        });
                        
                        // 既存データのクライアントIDを設定
                        if (originalData && originalData.client_id) {
                            clientSelect.value = originalData.client_id;
                        }
                    })
                    .withFailureHandler(function(error) {
                        document.getElementById('formMessage').textContent = 'クライアント一覧の読み込みエラー: ' + error.toString();
                    })
                    .fetchAllClients();
            }

            function submitForm() {
                try {
                    // 銀行コードを取得
                    const bankInputElement = document.getElementById('bank_code');
                    const bankInputValue = bankInputElement ? bankInputElement.value : '';
                    let selectedBankCode = '';
                    
                    if (bankInputValue) {
                        selectedBankCode = bankInputElement.getAttribute('data-selected-code') || '';
                        
                        if (!selectedBankCode) {
                            const selectedBank = bankData.find(bank => bank.name === bankInputValue);
                            selectedBankCode = selectedBank ? selectedBank.code : '';
                        }
                    }
                    

                    
                    const formData = {
                        id: document.getElementById('working_capital_id').value,
                        bank_code: selectedBankCode,
                        initial_loan_amount: document.getElementById('initial_loan_amount') ? parseFloat(document.getElementById('initial_loan_amount').value) || 0 : 0,
                        current_loan_balance: document.getElementById('current_loan_balance') ? parseFloat(document.getElementById('current_loan_balance').value) || 0 : 0,
                        principal_repayment: document.getElementById('principal_repayment') ? parseFloat(document.getElementById('principal_repayment').value) || 0 : 0,
                        loan_date: document.getElementById('loan_date') ? document.getElementById('loan_date').value : '',
                        repayment_due_date: document.getElementById('repayment_due_date') ? document.getElementById('repayment_due_date').value : null,
                        loan_type: document.getElementById('loan_type') ? document.getElementById('loan_type').value : '',
                        loan_interest_rate: document.getElementById('loan_interest_rate') ? parseFloat(document.getElementById('loan_interest_rate').value) || null : null,
                        remarks: document.getElementById('remarks') ? document.getElementById('remarks').value : '',
                        client_id: document.getElementById('client_id') ? document.getElementById('client_id').value : null
                    };
                    
                    // 必須チェック
                    if (!formData.id) {
                        document.getElementById('formMessage').textContent = 'エラー: 運転資金IDが見つかりません';
                        return;
                    }
                    if (!formData.loan_date) {
                        document.getElementById('formMessage').textContent = 'エラー: 借入日は必須です';
                        return;
                    }
                    
                    // 管理者の場合はクライアント選択が必須
                    const clientRow = document.getElementById('clientRow');
                    if (clientRow && clientRow.style.display !== 'none') {
                        if (!formData.client_id) {
                            document.getElementById('formMessage').textContent = 'エラー: クライアントの選択は必須です';
                            return;
                        }
                    }
                    
                    document.getElementById('formMessage').textContent = '更新中…';
                    
                    // 銀行の詳細を取得
                    if (formData.bank_code) {
                        document.getElementById('formMessage').textContent = '銀行詳細を取得中...';
                        
                        google.script.run
                            .withSuccessHandler(function(bankDetail) {
                                // 銀行詳細データを追加
                                if (bankDetail) {
                                    formData.bank_name = bankDetail.name || '';
                                    formData.bank_name_half_kana = bankDetail.halfWidthKana || '';
                                    formData.bank_name_full_kana = bankDetail.fullWidthKana || '';
                                    formData.bank_name_hiragana = bankDetail.hiragana || '';
                                    formData.bank_business_type_code = bankDetail.businessTypeCode || '';
                                    formData.bank_business_type = bankDetail.businessType || '';
                                }
                                
                                // 更新処理を実行
                                updateWorkingCapital(formData);
                            })
                            .withFailureHandler(function(error) {
                                document.getElementById('formMessage').textContent = '銀行詳細の取得エラー: ' + error.toString();
                            })
                            .getBankDetailFromAPI(formData.bank_code);
                    } else {
                        // 銀行が選択されていない場合は直接更新
                        updateWorkingCapital(formData);
                    }
                } catch (error) {
                    document.getElementById('formMessage').textContent = 'スクリプトエラー: ' + error.message;
                }
            }

            function updateWorkingCapital(formData) {
                document.getElementById('formMessage').textContent = '更新中...';
                
                google.script.run
                    .withSuccessHandler(res => {
                        if (res && res.success) {
                            document.getElementById('formMessage').textContent = '更新完了: ID=' + (res.id || '');
                            setTimeout(() => {
                                google.script.host.close();
                            }, 1000);
                        } else {
                            document.getElementById('formMessage').textContent = 'エラー: ' + (res ? res.message : '不明なエラー');
                        }
                    })
                    .withFailureHandler(err => {
                        document.getElementById('formMessage').textContent = '通信エラー: ' + err.toString();
                    })
                    .updateWorkingCapital(formData);
            }
        </script>
    </body>
</html>
