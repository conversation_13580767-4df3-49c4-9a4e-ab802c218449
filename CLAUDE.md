# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Language Settings

**IMPORTANT: Always respond in Japanese (日本語) when working with this codebase.**
This is a Japanese business application and all communication should be in Japanese to maintain consistency with the project context and user expectations.

## Project Overview

This is a Japanese property/borrower management prototype system (融資先DBプロトタイプ) for "フォートラスト" company. The system is built as a **Google Apps Script (GAS) application** that integrates with **Supabase** as the backend database, using **Google Sheets** as the primary user interface.

## Technology Stack

- **Google Apps Script (GAS)** - Main application platform
- **JavaScript (ES5/V8 runtime)** - Primary programming language
- **HTML** - Modal dialogs and UI components
- **Supabase** - Backend database (PostgreSQL) and authentication
- **Google Sheets** - Frontend interface and data display
- **@google/clasp** - Google Apps Script CLI tool

## Common Commands

### Setup Commands
```bash
# Install Google Apps Script CLI (one-time setup)
npm install -g @google/clasp

# Authenticate with Google (one-time setup)
clasp login

# Pull latest code from GAS
clasp pull

# Deploy code changes to GAS
clasp push
```

### Development Workflow
1. Make changes to local files
2. Run `clasp push` to deploy to Google Apps Script
3. Test functionality in the Google Sheets interface
4. Commit changes to git

## Architecture Overview

### Core Structure
```
app/
├── *.js (11 modules)     # Core business logic
├── *.html (11 components) # Modal dialogs for data entry
├── appsscript.json       # GAS project configuration
└── package.json          # Dependencies

migration/
└── *.sql                 # Database schema files
```

### Key Application Modules

**Core JavaScript Files:**
- `common.js` - Shared utilities and admin user detection
- `login.js` - Supabase authentication and session management
- `properties.js` - Property management (main functionality)
- `user.js`, `bank.js`, `loan_informations.js` - Entity management
- `pdf_*.js` - PDF generation for reports

**HTML Modal Pattern:**
- Each entity has Create/Update modal pairs
- Consistent UI pattern across all data entry forms
- Integration with Google Sheets for seamless user experience

### Database Architecture

**Multi-tenant Design:**
- Admin users (no client_id) have full access
- Client users are restricted to their own data via client_id filtering
- Role-based access control through profiles table

**Core Tables:**
- `properties` - Main property information
- `short_properties`/`long_properties` - Property type specializations
- `clients`, `users`, `profiles` - User management
- `bank_representatives`, `loan_information` - Financial tracking

### Property Management System

**Dual Property Types:**
- **Short-term (売買/trading)** - Buy/sell transactions
- **Long-term (賃貸/rental)** - Rental property management

**Key Features:**
- Complex financial calculations (loan ratios, taxes, fees)
- Building age auto-calculation from construction dates
- Progress tracking and status management
- Bank integration and loan information tracking
- PDF report generation for analysis

### Development Guidelines

**Important Constraints:**
- Never modify technology stack versions without approval
- Avoid UI/UX design changes (layout, colors, fonts) without approval
- No unauthorized changes beyond explicit instructions
- Maintain existing code patterns and naming conventions

**Architecture Patterns:**
- Google Sheets as primary frontend interface
- Modal-based data entry through HTML components
- Supabase REST API for all database operations
- JWT-based authentication with session management
- Client-based data isolation for multi-tenancy

**Code Conventions:**
- ES5 JavaScript (GAS runtime limitation)
- Consistent modal naming: `{Entity}Modal{Action}.html`
- Shared utilities in `common.js`
- Database operations through Supabase REST API

### Integration Points

**Supabase Integration:**
- Authentication via JWT tokens
- REST API calls for all CRUD operations
- Row Level Security (RLS) for data isolation

**Google Workspace Integration:**
- SpreadsheetApp for Sheets interaction
- UrlFetchApp for HTTP requests to Supabase
- Web App deployment for modal serving

**PDF Generation:**
- Multiple report types (overview, simulation, cash flow)
- Japanese formatting and localization

### Current Development Branch

- Main development on `feature/nishiyama` branch
- Recent focus on property registration and bank-related functionality
- Active development in CreateModalProperty.html and UpdateModalProperty.html