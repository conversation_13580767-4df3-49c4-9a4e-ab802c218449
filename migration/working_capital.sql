-- =====================================
-- 運転資金テーブル
-- =====================================
CREATE TABLE public.working_capital (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 運転資金のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）

    -- 銀行情報
    bank_code                varchar      DEFAULT NULL,                          -- 銀行コード(API)
    bank_name                varchar      DEFAULT NULL,                          -- 銀行名
    bank_name_half_kana      varchar      DEFAULT NULL,                          -- 銀行名半角カタカナ
    bank_name_full_kana      varchar      DEFAULT NULL,                          -- 銀行名全角カタカナ
    bank_name_hiragana       varchar      DEFAULT NULL,                          -- 銀行名ひらがな
    bank_business_type_code  varchar      DEFAULT NULL,                          -- 銀行事業形態コード
    bank_business_type       varchar      DEFAULT NULL,                          -- 銀行事業形態種別

    -- 支店情報
    branch_code              varchar      DEFAULT NULL,                          -- 支店コード(API)
    branch_name              varchar      DEFAULT NULL,                          -- 支店名
    branch_name_half_kana    varchar      DEFAULT NULL,                          -- 支店名半角カタカナ
    branch_name_full_kana    varchar      DEFAULT NULL,                          -- 支店名全角カタカナ
    branch_name_hiragana     varchar      DEFAULT NULL,                          -- 支店名ひらがな

    -- 借入情報
    initial_loan_amount      numeric NOT NULL DEFAULT 0,                            -- 当初借入金額
    current_loan_balance     numeric NOT NULL DEFAULT 0,                  -- 現在借入残高
    principal_repayment      numeric NOT NULL DEFAULT 0,                           -- 元本返済額
    loan_date               date         NOT NULL,                              -- 借入日
    repayment_due_date      date         DEFAULT NULL,                          -- 返済期日
    loan_type               varchar      DEFAULT NULL,                          -- 借入形態
    loan_interest_rate      numeric  DEFAULT NULL,                         -- 借入金利

    -- 備考
    remarks                 text         DEFAULT NULL,                          -- 備考

    -- ステータス管理
    deleted_at              timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at              timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at              timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にする
ALTER TABLE public.working_capital ENABLE ROW LEVEL SECURITY;

-- =====================================
-- 運転資金テーブル用 RLS ポリシー
-- =====================================

-- SELECT: 管理者は全て参照可能、クライアントは自分が所属するクライアントに紐づく運転資金情報のみ参照可能
CREATE POLICY "Admins can view all, users can view working capital for their clients." 
ON public.working_capital
FOR SELECT 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- INSERT: 管理者は任意のクライアントに運転資金を追加可能、クライアントは自分が所属するクライアントにのみ追加可能
CREATE POLICY "Admins can insert for any client, users can insert working capital for their clients." 
ON public.working_capital
FOR INSERT 
WITH CHECK (
    -- 管理者の場合は任意のclient_idで作成可能
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- UPDATE: 管理者は全ての運転資金を更新可能、クライアントは自分が所属するクライアントに紐づく運転資金のみ更新可能
CREATE POLICY "Admins can update all, users can update working capital for their clients." 
ON public.working_capital
FOR UPDATE 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- =====================================
-- 運転資金テーブル用更新日時自動更新トリガー
-- =====================================

-- 運転資金テーブル更新日トリガー作成
-- working_capital テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
-- （update_modified_column関数はft.sqlで既に定義済みのため、ここでは作成しません）
-- CREATE TRIGGER handle_updated_at_working_capital
-- BEFORE UPDATE ON working_capital              -- 更新"前"に発火
-- FOR EACH ROW                                  -- 行単位で実行
-- EXECUTE FUNCTION update_modified_column();    -- ft.sqlで定義済みの関数を実行

-- =====================================
-- 運転資金取得用 RPC 関数
-- =====================================

-- 関数が既に存在する場合は削除（正確なパラメータ型を指定）
DROP FUNCTION IF EXISTS public.get_working_capital_with_clients(VARCHAR, VARCHAR, UUID);
DROP FUNCTION IF EXISTS public.get_working_capital_by_id(UUID, UUID);

-- 運転資金一覧取得関数（working_capital + clients結合）
CREATE OR REPLACE FUNCTION public.get_working_capital_with_clients(
    search_bank_name VARCHAR DEFAULT NULL,       -- 銀行名での部分検索
    search_client_name VARCHAR DEFAULT NULL,     -- クライアント名での部分検索
    filter_client_id UUID DEFAULT NULL          -- クライアントIDでのフィルタ（権限制御用）
)
RETURNS TABLE (
    id UUID,
    client_id UUID,
    client_name VARCHAR,
    bank_code VARCHAR,
    bank_name VARCHAR,
    bank_name_half_kana VARCHAR,
    bank_name_full_kana VARCHAR,
    bank_name_hiragana VARCHAR,
    bank_business_type_code VARCHAR,
    bank_business_type VARCHAR,
    branch_code VARCHAR,
    branch_name VARCHAR,
    branch_name_half_kana VARCHAR,
    branch_name_full_kana VARCHAR,
    branch_name_hiragana VARCHAR,
    initial_loan_amount NUMERIC,
    current_loan_balance NUMERIC,
    principal_repayment NUMERIC,
    loan_date DATE,
    repayment_due_date DATE,
    loan_type VARCHAR,
    loan_interest_rate NUMERIC,
    remarks TEXT,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id AS id,
        wc.client_id AS client_id,
        c.name AS client_name,
        wc.bank_code AS bank_code,
        wc.bank_name AS bank_name,
        wc.bank_name_half_kana AS bank_name_half_kana,
        wc.bank_name_full_kana AS bank_name_full_kana,
        wc.bank_name_hiragana AS bank_name_hiragana,
        wc.bank_business_type_code AS bank_business_type_code,
        wc.bank_business_type AS bank_business_type,
        wc.branch_code AS branch_code,
        wc.branch_name AS branch_name,
        wc.branch_name_half_kana AS branch_name_half_kana,
        wc.branch_name_full_kana AS branch_name_full_kana,
        wc.branch_name_hiragana AS branch_name_hiragana,
        wc.initial_loan_amount AS initial_loan_amount,
        wc.current_loan_balance AS current_loan_balance,
        wc.principal_repayment AS principal_repayment,
        wc.loan_date AS loan_date,
        wc.repayment_due_date AS repayment_due_date,
        wc.loan_type AS loan_type,
        wc.loan_interest_rate AS loan_interest_rate,
        wc.remarks AS remarks,
        wc.deleted_at AS deleted_at,
        wc.created_at AS created_at,
        wc.updated_at AS updated_at
    FROM
        public.working_capital AS wc
    LEFT JOIN
        public.clients AS c ON wc.client_id = c.id
    WHERE
        -- 銀行名での部分検索
        (search_bank_name IS NULL OR search_bank_name = '' OR wc.bank_name ILIKE '%' || search_bank_name || '%')
        AND
        -- クライアント名での部分検索
        (search_client_name IS NULL OR search_client_name = '' OR c.name ILIKE '%' || search_client_name || '%')
        AND
        -- クライアントIDでのフィルタ（権限制御用）
        (filter_client_id IS NULL OR wc.client_id = filter_client_id)
        AND
        -- 削除されていないレコードのみ
        wc.deleted_at IS NULL
    ORDER BY
        wc.updated_at DESC;
END;
$$;

-- 特定運転資金取得関数（IDベース）
CREATE OR REPLACE FUNCTION public.get_working_capital_by_id(
    p_working_capital_id UUID,
    filter_client_id UUID DEFAULT NULL  -- クライアントIDでのフィルタ（権限制御用）
)
RETURNS TABLE (
    id UUID,
    client_id UUID,
    client_name VARCHAR,
    bank_code VARCHAR,
    bank_name VARCHAR,
    bank_name_half_kana VARCHAR,
    bank_name_full_kana VARCHAR,
    bank_name_hiragana VARCHAR,
    bank_business_type_code VARCHAR,
    bank_business_type VARCHAR,
    branch_code VARCHAR,
    branch_name VARCHAR,
    branch_name_half_kana VARCHAR,
    branch_name_full_kana VARCHAR,
    branch_name_hiragana VARCHAR,
    initial_loan_amount NUMERIC,
    current_loan_balance NUMERIC,
    principal_repayment NUMERIC,
    loan_date DATE,
    repayment_due_date DATE,
    loan_type VARCHAR,
    loan_interest_rate NUMERIC,
    remarks TEXT,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id AS id,
        wc.client_id AS client_id,
        c.name AS client_name,
        wc.bank_code AS bank_code,
        wc.bank_name AS bank_name,
        wc.bank_name_half_kana AS bank_name_half_kana,
        wc.bank_name_full_kana AS bank_name_full_kana,
        wc.bank_name_hiragana AS bank_name_hiragana,
        wc.bank_business_type_code AS bank_business_type_code,
        wc.bank_business_type AS bank_business_type,
        wc.branch_code AS branch_code,
        wc.branch_name AS branch_name,
        wc.branch_name_half_kana AS branch_name_half_kana,
        wc.branch_name_full_kana AS branch_name_full_kana,
        wc.branch_name_hiragana AS branch_name_hiragana,
        wc.initial_loan_amount AS initial_loan_amount,
        wc.current_loan_balance AS current_loan_balance,
        wc.principal_repayment AS principal_repayment,
        wc.loan_date AS loan_date,
        wc.repayment_due_date AS repayment_due_date,
        wc.loan_type AS loan_type,
        wc.loan_interest_rate AS loan_interest_rate,
        wc.remarks AS remarks,
        wc.deleted_at AS deleted_at,
        wc.created_at AS created_at,
        wc.updated_at AS updated_at
    FROM
        public.working_capital AS wc
    LEFT JOIN
        public.clients AS c ON wc.client_id = c.id
    WHERE
        wc.id = p_working_capital_id
        AND
        -- クライアントIDでのフィルタ（権限制御用）
        (filter_client_id IS NULL OR wc.client_id = filter_client_id)
        AND
        -- 削除されていないレコードのみ
        wc.deleted_at IS NULL;
END;
$$;

-- この関数を public ロールから実行できるように権限を付与します。
-- service_role キーで呼び出す場合は必須ではありませんが、念のため設定しておくと良いでしょう。
-- GRANT EXECUTE ON FUNCTION public.get_working_capital_with_clients(VARCHAR, VARCHAR, UUID) TO public;
-- GRANT EXECUTE ON FUNCTION public.get_working_capital_by_id(UUID, UUID) TO public;
