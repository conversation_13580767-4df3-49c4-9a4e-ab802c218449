-- =====================================
-- クライアントテーブル
-- =====================================
CREATE TABLE public.clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- ここを UUID に変更
    name CHARACTER VARYING NULL,
    address CHARACTER VARYING NULL,
    phone_number CHARACTER VARYING NULL,
    remarks TEXT NULL,
    deleted_at timestamp with time zone DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW()
);

-- RLS（行レベルセキュリティ）を有効にするのを忘れないでください
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;