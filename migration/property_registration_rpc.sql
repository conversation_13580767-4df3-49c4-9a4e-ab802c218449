-- =====================================
-- 物件登録用RPC関数（全フィールド対応）
-- =====================================

-- 既存の関数を削除（もし存在する場合）
DROP FUNCTION IF EXISTS public.create_property_with_details;

-- 複数テーブル物件登録関数（全フィールド対応）
CREATE OR REPLACE FUNCTION create_property_with_details(
    -- プロパティ基本情報
    p_type smallint DEFAULT 1,
    p_purchase_date date DEFAULT NULL,
    p_address varchar DEFAULT NULL,
    p_land_area_sqm numeric DEFAULT NULL,
    p_fixed_asset_tax_land numeric DEFAULT NULL,
    p_building_area_sqm numeric DEFAULT NULL,
    p_structure varchar DEFAULT NULL,
    p_construction_date date DEFAULT NULL,
    p_building_age numeric DEFAULT NULL,
    p_fixed_asset_tax_building numeric DEFAULT NULL,
    p_purchase_price numeric DEFAULT NULL,
    
    -- 融資情報
    p_working_capital_id uuid DEFAULT NULL,
    p_loan_amount numeric DEFAULT NULL,
    p_loan_period numeric DEFAULT NULL,
    p_loan_interest_rate numeric DEFAULT NULL,
    p_loan_fee_rate numeric DEFAULT NULL,
    
    -- 売却予定情報
    p_planned_sale_amount numeric DEFAULT NULL,
    p_planned_sale_month numeric DEFAULT NULL,
    
    -- 銀行評価情報
    p_bank_collateral_value numeric DEFAULT NULL,
    p_bank_risk_amount numeric DEFAULT NULL,
    
    
    -- その他情報
    p_remarks text DEFAULT NULL,
    p_loan_ratio numeric DEFAULT NULL,
    p_fixed_asset_tax_total numeric DEFAULT NULL,
    p_progress_status varchar DEFAULT NULL,
    p_closing_date date DEFAULT NULL,
    
    -- 短期物件詳細（JSONBとして渡す）
    p_short_property_data jsonb DEFAULT NULL,
    
    -- 長期物件詳細（JSONBとして渡す）
    p_long_property_data jsonb DEFAULT NULL,
    
    -- 相談銀行情報（配列として渡す）
    p_consultation_banks jsonb DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
    v_property_id UUID;
    v_client_id UUID;
    v_short_property_id UUID;
    v_long_property_id UUID;
    v_consultation_bank jsonb;
    v_working_capital_id UUID;
    v_result jsonb;
BEGIN
    -- 現在のユーザーのclient_idを取得
    SELECT client_id INTO v_client_id 
    FROM public.profiles 
    WHERE id = auth.uid();
    
    IF v_client_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'ユーザーのクライアントIDが見つかりません'
        );
    END IF;
    
    -- properties テーブルに挿入（全フィールド対応）
    INSERT INTO public.properties (
        client_id,
        type,
        purchase_date,
        address,
        land_area_sqm,
        fixed_asset_tax_land,
        building_area_sqm,
        structure,
        construction_date,
        building_age,
        fixed_asset_tax_building,
        purchase_price,
        working_capital_id,
        loan_amount,
        loan_period,
        loan_interest_rate,
        loan_fee_rate,
        planned_sale_amount,
        planned_sale_month,
        bank_collateral_value,
        bank_risk_amount,
        remarks,
        loan_ratio,
        fixed_asset_tax_total,
        progress_status,
        closing_date
    ) VALUES (
        v_client_id,
        p_type,
        p_purchase_date,
        p_address,
        p_land_area_sqm,
        p_fixed_asset_tax_land,
        p_building_area_sqm,
        p_structure,
        p_construction_date,
        p_building_age,
        p_fixed_asset_tax_building,
        p_purchase_price,
        p_working_capital_id,
        p_loan_amount,
        p_loan_period,
        p_loan_interest_rate,
        p_loan_fee_rate,
        p_planned_sale_amount,
        p_planned_sale_month,
        p_bank_collateral_value,
        p_bank_risk_amount,
        p_remarks,
        p_loan_ratio,
        p_fixed_asset_tax_total,
        p_progress_status,
        p_closing_date
    ) RETURNING id INTO v_property_id;
    
    -- 短期物件の場合（全フィールド対応）
    IF p_type = 1 AND p_short_property_data IS NOT NULL THEN
        INSERT INTO public.short_properties (
            client_id,
            property_id,
            sales_method,
            nearest_station,
            progress_status,
            property_type,
            station_distance_min,
            lot_number,
            map_url,
            land_area_tsubo,
            land_use_zone,
            building_coverage_ratio,
            floor_area_ratio,
            road_width,
            road_setback,
            floor_plan,
            building_floors,
            room_number,
            consumption_tax_amount,
            down_payment_tax_incl,
            down_payment_tax_incl_month,
            balance_payment_tax_incl,
            balance_payment_tax_incl_month,
            brokerage_fee_purchase,
            brokerage_fee_purchase_month,
            registration_fee_purchase,
            registration_fee_purchase_month,
            real_estate_tax_purchase,
            real_estate_tax_purchase_month,
            stamp_duty_purchase,
            stamp_duty_purchase_month,
            loan_fee_purchase,
            loan_fee_purchase_month,
            property_tax,
            property_tax_month,
            fire_insurance,
            fire_insurance_month,
            survey_cost,
            survey_cost_month,
            demolition_cost,
            demolition_cost_month,
            renovation_cost,
            renovation_cost_month,
            management_fee,
            management_fee_month,
            other_land_expenses,
            other_land_expenses_month,
            other_building_expenses,
            other_building_expenses_month,
            other_expenses,
            other_expenses_month,
            brokerage_fee_sale,
            brokerage_fee_sale_month,
            advertising_fee_sale,
            advertising_fee_sale_month,
            bank_expenses_sale,
            bank_expenses_sale_month,
            mortgage_cancellation_fee,
            mortgage_cancellation_fee_month,
            registration_fee_sale,
            registration_fee_sale_month,
            total_cost,
            employee_commission_sale,
            responsible_employee,
            total_units,
            purchase_annual_rent,
            sale_estimated_annual_rent,
            sale_estimated_yield,
            loan_repayment_amount,
            loan_repayment_month,
            down_payment_sale_tax_incl,
            down_payment_sale_tax_incl_month,
            balance_payment_sale_tax_incl,
            balance_payment_sale_tax_incl_month,
            best_sale_amount,
            best_sale_yield,
            planned_profit_amount,
            planned_profit_rate,
            best_profit_amount,
            best_profit_rate
        ) VALUES (
            v_client_id,
            v_property_id,
            (p_short_property_data->>'sales_method')::varchar,
            (p_short_property_data->>'nearest_station')::varchar,
            (p_short_property_data->>'progress_status')::varchar,
            (p_short_property_data->>'property_type')::varchar,
            (p_short_property_data->>'station_distance_min')::numeric,
            (p_short_property_data->>'lot_number')::varchar,
            (p_short_property_data->>'map_url')::varchar,
            (p_short_property_data->>'land_area_tsubo')::numeric,
            (p_short_property_data->>'land_use_zone')::varchar,
            (p_short_property_data->>'building_coverage_ratio')::numeric,
            (p_short_property_data->>'floor_area_ratio')::numeric,
            (p_short_property_data->>'road_width')::numeric,
            (p_short_property_data->>'road_setback')::numeric,
            (p_short_property_data->>'floor_plan')::varchar,
            (p_short_property_data->>'building_floors')::varchar,
            (p_short_property_data->>'room_number')::varchar,
            (p_short_property_data->>'consumption_tax_amount')::numeric,
            (p_short_property_data->>'down_payment_tax_incl')::numeric,
            (p_short_property_data->>'down_payment_tax_incl_month')::numeric,
            (p_short_property_data->>'balance_payment_tax_incl')::numeric,
            (p_short_property_data->>'balance_payment_tax_incl_month')::numeric,
            (p_short_property_data->>'brokerage_fee_purchase')::numeric,
            (p_short_property_data->>'brokerage_fee_purchase_month')::numeric,
            (p_short_property_data->>'registration_fee_purchase')::numeric,
            (p_short_property_data->>'registration_fee_purchase_month')::numeric,
            (p_short_property_data->>'real_estate_tax_purchase')::numeric,
            (p_short_property_data->>'real_estate_tax_purchase_month')::numeric,
            (p_short_property_data->>'stamp_duty_purchase')::numeric,
            (p_short_property_data->>'stamp_duty_purchase_month')::numeric,
            (p_short_property_data->>'loan_fee_purchase')::numeric,
            (p_short_property_data->>'loan_fee_purchase_month')::numeric,
            (p_short_property_data->>'property_tax')::numeric,
            (p_short_property_data->>'property_tax_month')::numeric,
            (p_short_property_data->>'fire_insurance')::numeric,
            (p_short_property_data->>'fire_insurance_month')::numeric,
            (p_short_property_data->>'survey_cost')::numeric,
            (p_short_property_data->>'survey_cost_month')::numeric,
            (p_short_property_data->>'demolition_cost')::numeric,
            (p_short_property_data->>'demolition_cost_month')::numeric,
            (p_short_property_data->>'renovation_cost')::numeric,
            (p_short_property_data->>'renovation_cost_month')::numeric,
            (p_short_property_data->>'management_fee')::numeric,
            (p_short_property_data->>'management_fee_month')::numeric,
            (p_short_property_data->>'other_land_expenses')::numeric,
            (p_short_property_data->>'other_land_expenses_month')::numeric,
            (p_short_property_data->>'other_building_expenses')::numeric,
            (p_short_property_data->>'other_building_expenses_month')::numeric,
            (p_short_property_data->>'other_expenses')::numeric,
            (p_short_property_data->>'other_expenses_month')::numeric,
            (p_short_property_data->>'brokerage_fee_sale')::numeric,
            (p_short_property_data->>'brokerage_fee_sale_month')::numeric,
            (p_short_property_data->>'advertising_fee_sale')::numeric,
            (p_short_property_data->>'advertising_fee_sale_month')::numeric,
            (p_short_property_data->>'bank_expenses_sale')::numeric,
            (p_short_property_data->>'bank_expenses_sale_month')::numeric,
            (p_short_property_data->>'mortgage_cancellation_fee')::numeric,
            (p_short_property_data->>'mortgage_cancellation_fee_month')::numeric,
            (p_short_property_data->>'registration_fee_sale')::numeric,
            (p_short_property_data->>'registration_fee_sale_month')::numeric,
            (p_short_property_data->>'total_cost')::numeric,
            (p_short_property_data->>'employee_commission_sale')::numeric,
            (p_short_property_data->>'responsible_employee')::varchar,
            (p_short_property_data->>'total_units')::numeric,
            (p_short_property_data->>'purchase_annual_rent')::numeric,
            (p_short_property_data->>'sale_estimated_annual_rent')::numeric,
            (p_short_property_data->>'sale_estimated_yield')::numeric,
            (p_short_property_data->>'loan_repayment_amount')::numeric,
            (p_short_property_data->>'loan_repayment_month')::numeric,
            (p_short_property_data->>'down_payment_sale_tax_incl')::numeric,
            (p_short_property_data->>'down_payment_sale_tax_incl_month')::numeric,
            (p_short_property_data->>'balance_payment_sale_tax_incl')::numeric,
            (p_short_property_data->>'balance_payment_sale_tax_incl_month')::numeric,
            (p_short_property_data->>'best_sale_amount')::numeric,
            (p_short_property_data->>'best_sale_yield')::numeric,
            (p_short_property_data->>'planned_profit_amount')::numeric,
            (p_short_property_data->>'planned_profit_rate')::numeric,
            (p_short_property_data->>'best_profit_amount')::numeric,
            (p_short_property_data->>'best_profit_rate')::numeric
        ) RETURNING id INTO v_short_property_id;
    END IF;
    
    -- 長期物件の場合（全フィールド対応）
    IF p_type = 2 AND p_long_property_data IS NOT NULL THEN
        INSERT INTO public.long_properties (
            client_id,
            property_id,
            property_name,
            current_rental_income,
            full_occupancy_income,
            yield_rate,
            useful_life,
            economic_useful_life,
            repair_history,
            management_fee,
            repair_cost,
            property_tax,
            utility_cost,
            depreciation,
            other_expenses,
            current_balance,
            full_occupancy_balance,
            repair_fund_total,
            current_loan_balance,
            annual_principal_payment,
            annual_interest_payment,
            collateral_status,
            is_deleted
        ) VALUES (
            v_client_id,
            v_property_id,
            (p_long_property_data->>'property_name')::varchar,
            (p_long_property_data->>'current_rental_income')::numeric,
            (p_long_property_data->>'full_occupancy_income')::numeric,
            (p_long_property_data->>'yield_rate')::numeric,
            (p_long_property_data->>'useful_life')::numeric,
            (p_long_property_data->>'economic_useful_life')::numeric,
            (p_long_property_data->>'repair_history')::varchar,
            (p_long_property_data->>'management_fee')::numeric,
            (p_long_property_data->>'repair_cost')::numeric,
            (p_long_property_data->>'property_tax')::numeric,
            (p_long_property_data->>'utility_cost')::numeric,
            (p_long_property_data->>'depreciation')::numeric,
            (p_long_property_data->>'other_expenses')::numeric,
            (p_long_property_data->>'current_balance')::numeric,
            (p_long_property_data->>'full_occupancy_balance')::numeric,
            (p_long_property_data->>'repair_fund_total')::numeric,
            (p_long_property_data->>'current_loan_balance')::numeric,
            (p_long_property_data->>'annual_principal_payment')::numeric,
            (p_long_property_data->>'annual_interest_payment')::numeric,
            (p_long_property_data->>'collateral_status')::varchar,
            COALESCE((p_long_property_data->>'is_deleted')::boolean, false)
        ) RETURNING id INTO v_long_property_id;
    END IF;
    
    -- 相談銀行情報の挿入（複数対応）
    IF p_consultation_banks IS NOT NULL AND jsonb_array_length(p_consultation_banks) > 0 THEN
        FOR v_consultation_bank IN SELECT * FROM jsonb_array_elements(p_consultation_banks)
        LOOP
            INSERT INTO public.consultation_banks (
                client_id,
                property_id,
                bank_code,
                bank_name,
                bank_name_half_kana,
                bank_name_full_kana,
                bank_name_hiragana,
                bank_business_type_code,
                bank_business_type,
                consultation_date,
                response_date,
                response_days,
                response_amount,
                response_interest_rate,
                response_loan_ratio,
                remarks
            ) VALUES (
                v_client_id,
                v_property_id,
                (v_consultation_bank->>'bank_code')::varchar,
                (v_consultation_bank->>'bank_name')::varchar,
                (v_consultation_bank->>'bank_name_half_kana')::varchar,
                (v_consultation_bank->>'bank_name_full_kana')::varchar,
                (v_consultation_bank->>'bank_name_hiragana')::varchar,
                (v_consultation_bank->>'bank_business_type_code')::varchar,
                (v_consultation_bank->>'bank_business_type')::varchar,
                (v_consultation_bank->>'consultation_date')::date,
                (v_consultation_bank->>'response_date')::date,
                (v_consultation_bank->>'response_days')::integer,
                (v_consultation_bank->>'response_amount')::numeric,
                (v_consultation_bank->>'response_interest_rate')::numeric,
                (v_consultation_bank->>'response_loan_ratio')::numeric,
                (v_consultation_bank->>'remarks')::text
            ) RETURNING id INTO v_working_capital_id;
        END LOOP;
    END IF;
    
    -- 結果を返す
    v_result := jsonb_build_object(
        'success', true,
        'property_id', v_property_id,
        'short_property_id', v_short_property_id,
        'long_property_id', v_long_property_id,
        'message', '物件データが正常に登録されました'
    );
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'エラーが発生しました: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS権限の設定
GRANT EXECUTE ON FUNCTION create_property_with_details TO authenticated;