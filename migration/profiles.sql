-- =====================================
-- ユーザーとクライアントを紐づけるテーブル
-- 
-- ロール:
-- 1: 管理者 - 全てのプロフィールの閲覧・更新・作成が可能
-- 2: クライアント（デフォルト） - 自分のプロフィールのみ閲覧・更新可能
-- =====================================
CREATE TABLE public.profiles (
    id UUID NOT NULL REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    -- client_id を追加。public.clients テーブルの id を参照し、
    -- 参照先のクライアントが削除された場合は NULL に設定されます。
    client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL DEFAULT NULL,
    name CHARACTER VARYING NULL,
    -- ロール: 1=管理者, 2=クライアント（デフォルト）
    role SMALLINT NOT NULL DEFAULT 2,
    -- 備考
    remarks text DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- シンプルなプロフィールアクセス権限（自分のプロフィールのみ）
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

-- ユーザーは自分のプロフィールを更新可能
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- プロフィール作成は認証済みユーザーが可能（初回登録用）
CREATE POLICY "Authenticated users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);