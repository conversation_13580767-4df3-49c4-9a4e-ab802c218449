-- =====================================
-- 相談銀行情報テーブル
-- =====================================
CREATE TABLE public.consultation_banks (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 相談銀行情報のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE, -- 物件ID（外部キー）

    -- 銀行情報
    bank_code                varchar      DEFAULT NULL,                          -- 銀行コード(API)
    bank_name                varchar      DEFAULT NULL,                          -- 銀行名
    bank_name_half_kana      varchar      DEFAULT NULL,                          -- 銀行名半角カタカナ
    bank_name_full_kana      varchar      DEFAULT NULL,                          -- 銀行名全角カタカナ
    bank_name_hiragana       varchar      DEFAULT NULL,                          -- 銀行名ひらがな
    bank_business_type_code  varchar      DEFAULT NULL,                          -- 銀行事業形態コード
    bank_business_type       varchar      DEFAULT NULL,                          -- 銀行事業形態種別

    -- 相談・回答情報
    consultation_date        date         DEFAULT NULL,                          -- 相談日
    response_date            date         DEFAULT NULL,                          -- 回答日
    response_days            integer      DEFAULT NULL,                          -- 回答日数
    response_amount          numeric      DEFAULT NULL,                          -- 回答金額
    response_interest_rate   numeric      DEFAULT NULL,                          -- 回答金利
    response_loan_ratio      numeric      DEFAULT NULL,                          -- 回答融資割合（%）

    -- 備考
    remarks                  text         DEFAULT NULL,                          -- 備考

    -- ステータス管理
    deleted_at               timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at               timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at               timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にする
ALTER TABLE public.consultation_banks ENABLE ROW LEVEL SECURITY;

-- =====================================
-- 相談銀行情報テーブル用 RLS ポリシー
-- =====================================

-- SELECT: 管理者は全て参照可能、クライアントは自分が所属するクライアントに紐づく相談銀行情報のみ参照可能
CREATE POLICY "Admins can view all, users can view consultation banks for their clients." 
ON public.consultation_banks
FOR SELECT 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- INSERT: 管理者は任意のクライアントに相談銀行情報を追加可能、クライアントは自分が所属するクライアントにのみ追加可能
CREATE POLICY "Admins can insert for any client, users can insert consultation banks for their clients." 
ON public.consultation_banks
FOR INSERT 
WITH CHECK (
    -- 管理者の場合は任意のclient_idで作成可能
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- UPDATE: 管理者は全ての相談銀行情報を更新可能、クライアントは自分が所属するクライアントに紐づく相談銀行情報のみ更新可能
CREATE POLICY "Admins can update all, users can update consultation banks for their clients." 
ON public.consultation_banks
FOR UPDATE 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- =====================================
-- 相談銀行情報テーブル用更新日時自動更新トリガー
-- =====================================

-- 相談銀行情報テーブル更新日トリガー作成
-- consultation_banks テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
-- （update_modified_column関数はft.sqlで既に定義済みのため、ここでは作成しません）
-- CREATE TRIGGER handle_updated_at_consultation_banks
-- BEFORE UPDATE ON consultation_banks           -- 更新"前"に発火
-- FOR EACH ROW                                  -- 行単位で実行
-- EXECUTE FUNCTION update_modified_column();    -- ft.sqlで定義済みの関数を実行