-- =====================================
-- 融資情報テーブル
-- =====================================
CREATE TABLE public.loan_informations (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 融資情報のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）

    -- 銀行情報
    bank_code                varchar      DEFAULT NULL,                          -- 銀行コード(API)
    bank_name                varchar      DEFAULT NULL,                          -- 銀行名
    bank_name_half_kana      varchar      DEFAULT NULL,                          -- 銀行名半角カタカナ
    bank_name_full_kana      varchar      DEFAULT NULL,                          -- 銀行名全角カタカナ
    bank_name_hiragana       varchar      DEFAULT NULL,                          -- 銀行名ひらがな
    bank_business_type_code  varchar      DEFAULT NULL,                          -- 銀行事業形態コード
    bank_business_type       varchar      DEFAULT NULL,                          -- 銀行事業形態種別

    -- 支店情報
    branch_code              varchar      DEFAULT NULL,                          -- 支店コード(API)
    branch_name              varchar      DEFAULT NULL,                          -- 支店名
    branch_name_half_kana    varchar      DEFAULT NULL,                          -- 支店名半角カタカナ
    branch_name_full_kana    varchar      DEFAULT NULL,                          -- 支店名全角カタカナ
    branch_name_hiragana     varchar      DEFAULT NULL,                          -- 支店名ひらがな

    -- 融資情報
    lending_rate             decimal(5,3) DEFAULT NULL,                          -- 貸出金利（%、例: 2.500）
    loan_fee                 numeric DEFAULT NULL,                               -- 融資手数料（金額）
    loan_limit               numeric DEFAULT NULL,                               -- 融資枠（金額）

    -- 融資詳細条件
    max_loan_amount          varchar      DEFAULT NULL,                          -- 総額いくらまで融資可能か
    total_loan_balance       varchar      DEFAULT NULL,                          -- 借入残高合計
    total_collateral_value   varchar      DEFAULT NULL,                          -- 担保評価合計
    total_bank_risk_amount   varchar      DEFAULT NULL,                          -- 銀行リスク額合計
    max_concurrent_loans     varchar      DEFAULT NULL,                          -- 同時に何件融資可能か
    loan_execution_count     varchar      DEFAULT NULL,                          -- 融資取組件数
    avg_response_days        varchar      DEFAULT NULL,                          -- 平均回答日数
    avg_response_loan_ratio  varchar      DEFAULT NULL,                          -- 平均回答融資割合（%）

    -- 決裁権限情報
    branch_manager_limit     varchar      DEFAULT NULL,                          -- 支店長権限額
    officer_approval_limit   varchar      DEFAULT NULL,                          -- 担当役員の決裁権限
    minimum_interest_rate    varchar      DEFAULT NULL,                          -- 下限金利（%）
    minimum_fee              varchar      DEFAULT NULL,                          -- 下限手数料

    -- プロジェクト・物件条件
    max_project_period       varchar      DEFAULT NULL,                          -- 最長PJ期間
    wooden_useful_life       varchar      DEFAULT NULL,                          -- 木造耐用年数〇年
    steel_useful_life        varchar      DEFAULT NULL,                          -- 鉄骨耐用年数〇年
    rc_useful_life           varchar      DEFAULT NULL,                          -- RC耐用年数〇年
    target_area              varchar      DEFAULT NULL,                          -- エリア
    required_equity_ratio    varchar      DEFAULT NULL,                          -- 自己資金何％必要

    -- 物件条件フラグ
    no_inspection_cert       varchar      DEFAULT NULL,                          -- 検査済証なし
    old_earthquake_standard  varchar      DEFAULT NULL,                          -- 旧耐震
    floor_area_ratio_over    varchar      DEFAULT NULL,                          -- 容積率オーバー
    building_coverage_over   varchar      DEFAULT NULL,                          -- 建蔽率オーバー
    land_only                varchar      DEFAULT NULL,                          -- 底地
    leased_land              varchar      DEFAULT NULL,                          -- 借地
    eviction_case            varchar      DEFAULT NULL,                          -- 立ち退き
    third_party_for          varchar      DEFAULT NULL,                          -- 3為

    -- 融資用途フラグ
    construction_cost        varchar      DEFAULT NULL,                          -- 建築費用
    demolition_cost          varchar      DEFAULT NULL,                          -- 解体費用
    working_capital          varchar      DEFAULT NULL,                          -- 運転資金
    tax_payment              varchar      DEFAULT NULL,                          -- 納税費用
    bonus_fund               varchar      DEFAULT NULL,                          -- 賞与資金
    overdraft                varchar      DEFAULT NULL,                          -- 当座貸越
    back_finance             varchar      DEFAULT NULL,                          -- バックファイナンス
    joint_registration       varchar      DEFAULT NULL,                          -- 連棟登記

    -- 財務条件
    debt_service_years_ok    varchar      DEFAULT NULL,                          -- 債務償還年数〇年ならOK
    equity_ratio_ok          varchar      DEFAULT NULL,                          -- 自己資本比率〇％ならOK
    company_rating           varchar      DEFAULT NULL,                          -- 自社の格付

    -- その他質問事項
    other_question1          text         DEFAULT NULL,                          -- その他の質問1
    other_question2          text         DEFAULT NULL,                          -- その他の質問2
    other_question3          text         DEFAULT NULL,                          -- その他の質問3
    other_question4          text         DEFAULT NULL,                          -- その他の質問4
    other_question5          text         DEFAULT NULL,                          -- その他の質問5
    other_question6          text         DEFAULT NULL,                          -- その他の質問6
    other_question7          text         DEFAULT NULL,                          -- その他の質問7
    other_question8          text         DEFAULT NULL,                          -- その他の質問8
    other_question9          text         DEFAULT NULL,                          -- その他の質問9
    other_question10         text         DEFAULT NULL,                          -- その他の質問10

    -- ステータス管理
    deleted_at               timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at               timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at               timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にする
ALTER TABLE public.loan_informations ENABLE ROW LEVEL SECURITY;

-- =====================================
-- 融資情報テーブル用 RLS ポリシー
-- =====================================

-- SELECT: 管理者は全て参照可能、クライアントは自分が所属するクライアントに紐づく融資情報のみ参照可能
CREATE POLICY "Admins can view all, users can view loan information for their clients." 
ON public.loan_informations
FOR SELECT 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- INSERT: 管理者は任意のクライアントに融資情報を追加可能、クライアントは自分が所属するクライアントにのみ追加可能
CREATE POLICY "Admins can insert for any client, users can insert loan information for their clients." 
ON public.loan_informations
FOR INSERT 
WITH CHECK (
    -- 管理者の場合は任意のclient_idで作成可能
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- UPDATE: 管理者は全ての融資情報を更新可能、クライアントは自分が所属するクライアントに紐づく融資情報のみ更新可能
CREATE POLICY "Admins can update all, users can update loan information for their clients." 
ON public.loan_informations
FOR UPDATE 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- =====================================
-- 融資情報テーブル用更新日時自動更新トリガー
-- =====================================

-- 融資情報テーブル更新日トリガー作成
-- loan_informations テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
-- （update_modified_column関数はft.sqlで既に定義済みのため、ここでは作成しません）
CREATE TRIGGER handle_updated_at_loan_informations
BEFORE UPDATE ON loan_informations              -- 更新"前"に発火
FOR EACH ROW                                    -- 行単位で実行
EXECUTE FUNCTION update_modified_column();      -- ft.sqlで定義済みの関数を実行

-- =====================================
-- 融資情報取得用 RPC 関数
-- =====================================

-- 関数が既に存在する場合は削除（正確なパラメータ型を指定）
DROP FUNCTION IF EXISTS public.get_loan_informations_with_clients(VARCHAR, VARCHAR, VARCHAR, UUID);
DROP FUNCTION IF EXISTS public.get_loan_information_by_id(UUID);
DROP FUNCTION IF EXISTS public.get_loan_information_by_id(UUID, UUID);

-- 融資情報一覧取得関数（loan_informations + clients結合）
CREATE OR REPLACE FUNCTION public.get_loan_informations_with_clients(
    search_bank_name VARCHAR DEFAULT NULL,            -- 銀行名での部分検索
    search_branch_name VARCHAR DEFAULT NULL,          -- 支店名での部分検索
    search_client_name VARCHAR DEFAULT NULL,          -- クライアント名での部分検索
    filter_client_id UUID DEFAULT NULL               -- クライアントIDでのフィルタ（権限制御用）
)
RETURNS TABLE (
    id UUID,
    client_id UUID,
    client_name VARCHAR,
    bank_code VARCHAR,
    bank_name VARCHAR,
    bank_name_half_kana VARCHAR,
    bank_name_full_kana VARCHAR,
    bank_name_hiragana VARCHAR,
    bank_business_type_code VARCHAR,
    bank_business_type VARCHAR,
    branch_code VARCHAR,
    branch_name VARCHAR,
    branch_name_half_kana VARCHAR,
    branch_name_full_kana VARCHAR,
    branch_name_hiragana VARCHAR,
    lending_rate DECIMAL,
    loan_fee NUMERIC,
    loan_limit NUMERIC,
    max_loan_amount VARCHAR,
    total_loan_balance VARCHAR,
    total_collateral_value VARCHAR,
    total_bank_risk_amount VARCHAR,
    max_concurrent_loans VARCHAR,
    loan_execution_count VARCHAR,
    avg_response_days VARCHAR,
    avg_response_loan_ratio VARCHAR,
    branch_manager_limit VARCHAR,
    officer_approval_limit VARCHAR,
    minimum_interest_rate VARCHAR,
    minimum_fee VARCHAR,
    max_project_period VARCHAR,
    wooden_useful_life VARCHAR,
    steel_useful_life VARCHAR,
    rc_useful_life VARCHAR,
    target_area VARCHAR,
    required_equity_ratio VARCHAR,
    no_inspection_cert VARCHAR,
    old_earthquake_standard VARCHAR,
    floor_area_ratio_over VARCHAR,
    building_coverage_over VARCHAR,
    land_only VARCHAR,
    leased_land VARCHAR,
    eviction_case VARCHAR,
    third_party_for VARCHAR,
    construction_cost VARCHAR,
    demolition_cost VARCHAR,
    working_capital VARCHAR,
    tax_payment VARCHAR,
    bonus_fund VARCHAR,
    overdraft VARCHAR,
    back_finance VARCHAR,
    joint_registration VARCHAR,
    debt_service_years_ok VARCHAR,
    equity_ratio_ok VARCHAR,
    company_rating VARCHAR,
    other_question1 TEXT,
    other_question2 TEXT,
    other_question3 TEXT,
    other_question4 TEXT,
    other_question5 TEXT,
    other_question6 TEXT,
    other_question7 TEXT,
    other_question8 TEXT,
    other_question9 TEXT,
    other_question10 TEXT,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        li.id AS id,
        li.client_id AS client_id,
        c.name AS client_name,
        li.bank_code AS bank_code,
        li.bank_name AS bank_name,
        li.bank_name_half_kana AS bank_name_half_kana,
        li.bank_name_full_kana AS bank_name_full_kana,
        li.bank_name_hiragana AS bank_name_hiragana,
        li.bank_business_type_code AS bank_business_type_code,
        li.bank_business_type AS bank_business_type,
        li.branch_code AS branch_code,
        li.branch_name AS branch_name,
        li.branch_name_half_kana AS branch_name_half_kana,
        li.branch_name_full_kana AS branch_name_full_kana,
        li.branch_name_hiragana AS branch_name_hiragana,
        li.lending_rate AS lending_rate,
        li.loan_fee AS loan_fee,
        li.loan_limit AS loan_limit,
        li.max_loan_amount AS max_loan_amount,
        li.total_loan_balance AS total_loan_balance,
        li.total_collateral_value AS total_collateral_value,
        li.total_bank_risk_amount AS total_bank_risk_amount,
        li.max_concurrent_loans AS max_concurrent_loans,
        li.loan_execution_count AS loan_execution_count,
        li.avg_response_days AS avg_response_days,
        li.avg_response_loan_ratio AS avg_response_loan_ratio,
        li.branch_manager_limit AS branch_manager_limit,
        li.officer_approval_limit AS officer_approval_limit,
        li.minimum_interest_rate AS minimum_interest_rate,
        li.minimum_fee AS minimum_fee,
        li.max_project_period AS max_project_period,
        li.wooden_useful_life AS wooden_useful_life,
        li.steel_useful_life AS steel_useful_life,
        li.rc_useful_life AS rc_useful_life,
        li.target_area AS target_area,
        li.required_equity_ratio AS required_equity_ratio,
        li.no_inspection_cert AS no_inspection_cert,
        li.old_earthquake_standard AS old_earthquake_standard,
        li.floor_area_ratio_over AS floor_area_ratio_over,
        li.building_coverage_over AS building_coverage_over,
        li.land_only AS land_only,
        li.leased_land AS leased_land,
        li.eviction_case AS eviction_case,
        li.third_party_for AS third_party_for,
        li.construction_cost AS construction_cost,
        li.demolition_cost AS demolition_cost,
        li.working_capital AS working_capital,
        li.tax_payment AS tax_payment,
        li.bonus_fund AS bonus_fund,
        li.overdraft AS overdraft,
        li.back_finance AS back_finance,
        li.joint_registration AS joint_registration,
        li.debt_service_years_ok AS debt_service_years_ok,
        li.equity_ratio_ok AS equity_ratio_ok,
        li.company_rating AS company_rating,
        li.other_question1 AS other_question1,
        li.other_question2 AS other_question2,
        li.other_question3 AS other_question3,
        li.other_question4 AS other_question4,
        li.other_question5 AS other_question5,
        li.other_question6 AS other_question6,
        li.other_question7 AS other_question7,
        li.other_question8 AS other_question8,
        li.other_question9 AS other_question9,
        li.other_question10 AS other_question10,
        li.deleted_at AS deleted_at,
        li.created_at AS created_at,
        li.updated_at AS updated_at
    FROM
        public.loan_informations AS li
    LEFT JOIN
        public.clients AS c ON li.client_id = c.id
    WHERE
        -- 銀行名での部分検索
        (search_bank_name IS NULL OR search_bank_name = '' OR li.bank_name ILIKE '%' || search_bank_name || '%')
        AND
        -- 支店名での部分検索
        (search_branch_name IS NULL OR search_branch_name = '' OR li.branch_name ILIKE '%' || search_branch_name || '%')
        AND
        -- クライアント名での部分検索
        (search_client_name IS NULL OR search_client_name = '' OR c.name ILIKE '%' || search_client_name || '%')
        AND
        -- クライアントIDでのフィルタ（権限制御用）
        (filter_client_id IS NULL OR li.client_id = filter_client_id)
        AND
        -- 削除されていないレコードのみ
        li.deleted_at IS NULL
    ORDER BY
        li.updated_at DESC;
END;
$$;

-- 特定融資情報取得関数（IDベース）
CREATE OR REPLACE FUNCTION public.get_loan_information_by_id(
    p_loan_information_id UUID,
    filter_client_id UUID DEFAULT NULL  -- クライアントIDでのフィルタ（権限制御用）
)
RETURNS TABLE (
    id UUID,
    client_id UUID,
    client_name VARCHAR,
    bank_code VARCHAR,
    bank_name VARCHAR,
    bank_name_half_kana VARCHAR,
    bank_name_full_kana VARCHAR,
    bank_name_hiragana VARCHAR,
    bank_business_type_code VARCHAR,
    bank_business_type VARCHAR,
    branch_code VARCHAR,
    branch_name VARCHAR,
    branch_name_half_kana VARCHAR,
    branch_name_full_kana VARCHAR,
    branch_name_hiragana VARCHAR,
    lending_rate DECIMAL,
    loan_fee NUMERIC,
    loan_limit NUMERIC,
    max_loan_amount VARCHAR,
    total_loan_balance VARCHAR,
    total_collateral_value VARCHAR,
    total_bank_risk_amount VARCHAR,
    max_concurrent_loans VARCHAR,
    loan_execution_count VARCHAR,
    avg_response_days VARCHAR,
    avg_response_loan_ratio VARCHAR,
    branch_manager_limit VARCHAR,
    officer_approval_limit VARCHAR,
    minimum_interest_rate VARCHAR,
    minimum_fee VARCHAR,
    max_project_period VARCHAR,
    wooden_useful_life VARCHAR,
    steel_useful_life VARCHAR,
    rc_useful_life VARCHAR,
    target_area VARCHAR,
    required_equity_ratio VARCHAR,
    no_inspection_cert VARCHAR,
    old_earthquake_standard VARCHAR,
    floor_area_ratio_over VARCHAR,
    building_coverage_over VARCHAR,
    land_only VARCHAR,
    leased_land VARCHAR,
    eviction_case VARCHAR,
    third_party_for VARCHAR,
    construction_cost VARCHAR,
    demolition_cost VARCHAR,
    working_capital VARCHAR,
    tax_payment VARCHAR,
    bonus_fund VARCHAR,
    overdraft VARCHAR,
    back_finance VARCHAR,
    joint_registration VARCHAR,
    debt_service_years_ok VARCHAR,
    equity_ratio_ok VARCHAR,
    company_rating VARCHAR,
    other_question1 TEXT,
    other_question2 TEXT,
    other_question3 TEXT,
    other_question4 TEXT,
    other_question5 TEXT,
    other_question6 TEXT,
    other_question7 TEXT,
    other_question8 TEXT,
    other_question9 TEXT,
    other_question10 TEXT,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        li.id AS id,
        li.client_id AS client_id,
        c.name AS client_name,
        li.bank_code AS bank_code,
        li.bank_name AS bank_name,
        li.bank_name_half_kana AS bank_name_half_kana,
        li.bank_name_full_kana AS bank_name_full_kana,
        li.bank_name_hiragana AS bank_name_hiragana,
        li.bank_business_type_code AS bank_business_type_code,
        li.bank_business_type AS bank_business_type,
        li.branch_code AS branch_code,
        li.branch_name AS branch_name,
        li.branch_name_half_kana AS branch_name_half_kana,
        li.branch_name_full_kana AS branch_name_full_kana,
        li.branch_name_hiragana AS branch_name_hiragana,
        li.lending_rate AS lending_rate,
        li.loan_fee AS loan_fee,
        li.loan_limit AS loan_limit,
        li.max_loan_amount AS max_loan_amount,
        li.total_loan_balance AS total_loan_balance,
        li.total_collateral_value AS total_collateral_value,
        li.total_bank_risk_amount AS total_bank_risk_amount,
        li.max_concurrent_loans AS max_concurrent_loans,
        li.loan_execution_count AS loan_execution_count,
        li.avg_response_days AS avg_response_days,
        li.avg_response_loan_ratio AS avg_response_loan_ratio,
        li.branch_manager_limit AS branch_manager_limit,
        li.officer_approval_limit AS officer_approval_limit,
        li.minimum_interest_rate AS minimum_interest_rate,
        li.minimum_fee AS minimum_fee,
        li.max_project_period AS max_project_period,
        li.wooden_useful_life AS wooden_useful_life,
        li.steel_useful_life AS steel_useful_life,
        li.rc_useful_life AS rc_useful_life,
        li.target_area AS target_area,
        li.required_equity_ratio AS required_equity_ratio,
        li.no_inspection_cert AS no_inspection_cert,
        li.old_earthquake_standard AS old_earthquake_standard,
        li.floor_area_ratio_over AS floor_area_ratio_over,
        li.building_coverage_over AS building_coverage_over,
        li.land_only AS land_only,
        li.leased_land AS leased_land,
        li.eviction_case AS eviction_case,
        li.third_party_for AS third_party_for,
        li.construction_cost AS construction_cost,
        li.demolition_cost AS demolition_cost,
        li.working_capital AS working_capital,
        li.tax_payment AS tax_payment,
        li.bonus_fund AS bonus_fund,
        li.overdraft AS overdraft,
        li.back_finance AS back_finance,
        li.joint_registration AS joint_registration,
        li.debt_service_years_ok AS debt_service_years_ok,
        li.equity_ratio_ok AS equity_ratio_ok,
        li.company_rating AS company_rating,
        li.other_question1 AS other_question1,
        li.other_question2 AS other_question2,
        li.other_question3 AS other_question3,
        li.other_question4 AS other_question4,
        li.other_question5 AS other_question5,
        li.other_question6 AS other_question6,
        li.other_question7 AS other_question7,
        li.other_question8 AS other_question8,
        li.other_question9 AS other_question9,
        li.other_question10 AS other_question10,
        li.deleted_at AS deleted_at,
        li.created_at AS created_at,
        li.updated_at AS updated_at
    FROM
        public.loan_informations AS li
    LEFT JOIN
        public.clients AS c ON li.client_id = c.id
    WHERE
        li.id = p_loan_information_id
        AND
        -- クライアントIDでのフィルタ（権限制御用）
        (filter_client_id IS NULL OR li.client_id = filter_client_id)
        AND
        -- 削除されていないレコードのみ
        li.deleted_at IS NULL;
END;
$$;

-- この関数を public ロールから実行できるように権限を付与します。
-- service_role キーで呼び出す場合は必須ではありませんが、念のため設定しておくと良いでしょう。
-- GRANT EXECUTE ON FUNCTION public.get_loan_informations_with_clients(VARCHAR, VARCHAR, VARCHAR, UUID) TO public;
-- GRANT EXECUTE ON FUNCTION public.get_loan_information_by_id(UUID, UUID) TO public;