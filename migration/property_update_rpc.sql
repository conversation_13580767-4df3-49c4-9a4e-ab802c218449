-- =====================================
-- 物件更新用RPC関数
-- =====================================

-- 既存の関数を削除（もし存在する場合）
DROP FUNCTION IF EXISTS public.update_property_with_details;

-- 物件情報を更新するRPC関数
-- 基本物件情報、短期/長期物件情報、相談銀行情報を一括更新
CREATE OR REPLACE FUNCTION update_property_with_details(
    p_id UUID,
    p_type INT DEFAULT NULL,
    p_purchase_date DATE DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_land_area_sqm NUMERIC DEFAULT NULL,
    p_fixed_asset_tax_land NUMERIC DEFAULT NULL,
    p_building_area_sqm NUMERIC DEFAULT NULL,
    p_structure TEXT DEFAULT NULL,
    p_construction_date DATE DEFAULT NULL,
    p_building_age INT DEFAULT NULL,
    p_fixed_asset_tax_building NUMERIC DEFAULT NULL,
    p_purchase_price NUMERIC DEFAULT NULL,
    p_working_capital_id UUID DEFAULT NULL,
    p_loan_amount NUMERIC DEFAULT NULL,
    p_loan_period INT DEFAULT NULL,
    p_loan_interest_rate NUMERIC DEFAULT NULL,
    p_loan_fee_rate NUMERIC DEFAULT NULL,
    p_planned_sale_amount NUMERIC DEFAULT NULL,
    p_planned_sale_month INT DEFAULT NULL,
    p_bank_collateral_value NUMERIC DEFAULT NULL,
    p_bank_risk_amount NUMERIC DEFAULT NULL,
    p_remarks TEXT DEFAULT NULL,
    p_loan_ratio NUMERIC DEFAULT NULL,
    p_fixed_asset_tax_total NUMERIC DEFAULT NULL,
    p_progress_status VARCHAR DEFAULT NULL,
    p_closing_date DATE DEFAULT NULL,
    p_short_property_data JSONB DEFAULT NULL,
    p_long_property_data JSONB DEFAULT NULL,
    p_consultation_banks JSONB DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_property_id UUID;
    v_client_id UUID;
    v_short_property_id UUID;
    v_long_property_id UUID;
    v_consultation_bank JSONB;
    v_result JSONB;
BEGIN
    -- 物件IDの存在確認
    SELECT id INTO v_property_id 
    FROM public.properties 
    WHERE id = p_id;
    
    IF v_property_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', '指定された物件が見つかりません'
        );
    END IF;

    -- クライアントIDの取得
    SELECT client_id INTO v_client_id
    FROM public.properties 
    WHERE id = p_id;

    -- 基本物件情報を更新
    UPDATE public.properties SET
        type = COALESCE(p_type, type),
        purchase_date = COALESCE(p_purchase_date, purchase_date),
        address = COALESCE(p_address, address),
        land_area_sqm = COALESCE(p_land_area_sqm, land_area_sqm),
        fixed_asset_tax_land = COALESCE(p_fixed_asset_tax_land, fixed_asset_tax_land),
        building_area_sqm = COALESCE(p_building_area_sqm, building_area_sqm),
        structure = COALESCE(p_structure, structure),
        construction_date = COALESCE(p_construction_date, construction_date),
        building_age = COALESCE(p_building_age, building_age),
        fixed_asset_tax_building = COALESCE(p_fixed_asset_tax_building, fixed_asset_tax_building),
        purchase_price = COALESCE(p_purchase_price, purchase_price),
        working_capital_id = COALESCE(p_working_capital_id, working_capital_id),
        loan_amount = COALESCE(p_loan_amount, loan_amount),
        loan_period = COALESCE(p_loan_period, loan_period),
        loan_interest_rate = COALESCE(p_loan_interest_rate, loan_interest_rate),
        loan_fee_rate = COALESCE(p_loan_fee_rate, loan_fee_rate),
        planned_sale_amount = COALESCE(p_planned_sale_amount, planned_sale_amount),
        planned_sale_month = COALESCE(p_planned_sale_month, planned_sale_month),
        bank_collateral_value = COALESCE(p_bank_collateral_value, bank_collateral_value),
        bank_risk_amount = COALESCE(p_bank_risk_amount, bank_risk_amount),
        remarks = COALESCE(p_remarks, remarks),
        loan_ratio = COALESCE(p_loan_ratio, loan_ratio),
        fixed_asset_tax_total = COALESCE(p_fixed_asset_tax_total, fixed_asset_tax_total),
        progress_status = COALESCE(p_progress_status, progress_status),
        closing_date = COALESCE(p_closing_date, closing_date),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_id;

    -- 短期物件情報の更新（type=1の場合）
    IF p_type = 1 OR (p_type IS NULL AND (SELECT type FROM public.properties WHERE id = p_id) = 1) THEN
        -- 短期物件データがある場合は更新
        IF p_short_property_data IS NOT NULL THEN
            -- 既存レコードの確認
            SELECT id INTO v_short_property_id 
            FROM public.short_properties 
            WHERE property_id = p_id;

            IF v_short_property_id IS NOT NULL THEN
                -- 既存レコードを更新
                UPDATE public.short_properties SET
                    sales_method = COALESCE((p_short_property_data->>'sales_method')::TEXT, sales_method),
                    nearest_station = COALESCE((p_short_property_data->>'nearest_station')::TEXT, nearest_station),
                    progress_status = COALESCE((p_short_property_data->>'progress_status')::TEXT, progress_status),
                    property_type = COALESCE((p_short_property_data->>'property_type')::TEXT, property_type),
                    station_distance_min = COALESCE((p_short_property_data->>'station_distance_min')::NUMERIC, station_distance_min),
                    lot_number = COALESCE((p_short_property_data->>'lot_number')::TEXT, lot_number),
                    map_url = COALESCE((p_short_property_data->>'map_url')::TEXT, map_url),
                    land_area_tsubo = COALESCE((p_short_property_data->>'land_area_tsubo')::NUMERIC, land_area_tsubo),
                    land_use_zone = COALESCE((p_short_property_data->>'land_use_zone')::TEXT, land_use_zone),
                    building_coverage_ratio = COALESCE((p_short_property_data->>'building_coverage_ratio')::NUMERIC, building_coverage_ratio),
                    floor_area_ratio = COALESCE((p_short_property_data->>'floor_area_ratio')::NUMERIC, floor_area_ratio),
                    road_width = COALESCE((p_short_property_data->>'road_width')::NUMERIC, road_width),
                    road_setback = COALESCE((p_short_property_data->>'road_setback')::NUMERIC, road_setback),
                    floor_plan = COALESCE((p_short_property_data->>'floor_plan')::TEXT, floor_plan),
                    building_floors = COALESCE((p_short_property_data->>'building_floors')::TEXT, building_floors),
                    room_number = COALESCE((p_short_property_data->>'room_number')::TEXT, room_number),
                    consumption_tax_amount = COALESCE((p_short_property_data->>'consumption_tax_amount')::NUMERIC, consumption_tax_amount),
                    down_payment_tax_incl = COALESCE((p_short_property_data->>'down_payment_tax_incl')::NUMERIC, down_payment_tax_incl),
                    down_payment_tax_incl_month = COALESCE((p_short_property_data->>'down_payment_tax_incl_month')::NUMERIC, down_payment_tax_incl_month),
                    balance_payment_tax_incl = COALESCE((p_short_property_data->>'balance_payment_tax_incl')::NUMERIC, balance_payment_tax_incl),
                    balance_payment_tax_incl_month = COALESCE((p_short_property_data->>'balance_payment_tax_incl_month')::NUMERIC, balance_payment_tax_incl_month),
                    brokerage_fee_purchase = COALESCE((p_short_property_data->>'brokerage_fee_purchase')::NUMERIC, brokerage_fee_purchase),
                    brokerage_fee_purchase_month = COALESCE((p_short_property_data->>'brokerage_fee_purchase_month')::NUMERIC, brokerage_fee_purchase_month),
                    registration_fee_purchase = COALESCE((p_short_property_data->>'registration_fee_purchase')::NUMERIC, registration_fee_purchase),
                    registration_fee_purchase_month = COALESCE((p_short_property_data->>'registration_fee_purchase_month')::NUMERIC, registration_fee_purchase_month),
                    real_estate_tax_purchase = COALESCE((p_short_property_data->>'real_estate_tax_purchase')::NUMERIC, real_estate_tax_purchase),
                    real_estate_tax_purchase_month = COALESCE((p_short_property_data->>'real_estate_tax_purchase_month')::NUMERIC, real_estate_tax_purchase_month),
                    stamp_duty_purchase = COALESCE((p_short_property_data->>'stamp_duty_purchase')::NUMERIC, stamp_duty_purchase),
                    stamp_duty_purchase_month = COALESCE((p_short_property_data->>'stamp_duty_purchase_month')::NUMERIC, stamp_duty_purchase_month),
                    loan_fee_purchase = COALESCE((p_short_property_data->>'loan_fee_purchase')::NUMERIC, loan_fee_purchase),
                    loan_fee_purchase_month = COALESCE((p_short_property_data->>'loan_fee_purchase_month')::NUMERIC, loan_fee_purchase_month),
                    property_tax = COALESCE((p_short_property_data->>'property_tax')::NUMERIC, property_tax),
                    property_tax_month = COALESCE((p_short_property_data->>'property_tax_month')::NUMERIC, property_tax_month),
                    fire_insurance = COALESCE((p_short_property_data->>'fire_insurance')::NUMERIC, fire_insurance),
                    fire_insurance_month = COALESCE((p_short_property_data->>'fire_insurance_month')::NUMERIC, fire_insurance_month),
                    survey_cost = COALESCE((p_short_property_data->>'survey_cost')::NUMERIC, survey_cost),
                    survey_cost_month = COALESCE((p_short_property_data->>'survey_cost_month')::NUMERIC, survey_cost_month),
                    demolition_cost = COALESCE((p_short_property_data->>'demolition_cost')::NUMERIC, demolition_cost),
                    demolition_cost_month = COALESCE((p_short_property_data->>'demolition_cost_month')::NUMERIC, demolition_cost_month),
                    renovation_cost = COALESCE((p_short_property_data->>'renovation_cost')::NUMERIC, renovation_cost),
                    renovation_cost_month = COALESCE((p_short_property_data->>'renovation_cost_month')::NUMERIC, renovation_cost_month),
                    management_fee = COALESCE((p_short_property_data->>'management_fee')::NUMERIC, management_fee),
                    management_fee_month = COALESCE((p_short_property_data->>'management_fee_month')::NUMERIC, management_fee_month),
                    other_land_expenses = COALESCE((p_short_property_data->>'other_land_expenses')::NUMERIC, other_land_expenses),
                    other_land_expenses_month = COALESCE((p_short_property_data->>'other_land_expenses_month')::NUMERIC, other_land_expenses_month),
                    other_building_expenses = COALESCE((p_short_property_data->>'other_building_expenses')::NUMERIC, other_building_expenses),
                    other_building_expenses_month = COALESCE((p_short_property_data->>'other_building_expenses_month')::NUMERIC, other_building_expenses_month),
                    other_expenses = COALESCE((p_short_property_data->>'other_expenses')::NUMERIC, other_expenses),
                    other_expenses_month = COALESCE((p_short_property_data->>'other_expenses_month')::NUMERIC, other_expenses_month),
                    brokerage_fee_sale = COALESCE((p_short_property_data->>'brokerage_fee_sale')::NUMERIC, brokerage_fee_sale),
                    brokerage_fee_sale_month = COALESCE((p_short_property_data->>'brokerage_fee_sale_month')::NUMERIC, brokerage_fee_sale_month),
                    advertising_fee_sale = COALESCE((p_short_property_data->>'advertising_fee_sale')::NUMERIC, advertising_fee_sale),
                    advertising_fee_sale_month = COALESCE((p_short_property_data->>'advertising_fee_sale_month')::NUMERIC, advertising_fee_sale_month),
                    bank_expenses_sale = COALESCE((p_short_property_data->>'bank_expenses_sale')::NUMERIC, bank_expenses_sale),
                    bank_expenses_sale_month = COALESCE((p_short_property_data->>'bank_expenses_sale_month')::NUMERIC, bank_expenses_sale_month),
                    mortgage_cancellation_fee = COALESCE((p_short_property_data->>'mortgage_cancellation_fee')::NUMERIC, mortgage_cancellation_fee),
                    mortgage_cancellation_fee_month = COALESCE((p_short_property_data->>'mortgage_cancellation_fee_month')::NUMERIC, mortgage_cancellation_fee_month),
                    registration_fee_sale = COALESCE((p_short_property_data->>'registration_fee_sale')::NUMERIC, registration_fee_sale),
                    registration_fee_sale_month = COALESCE((p_short_property_data->>'registration_fee_sale_month')::NUMERIC, registration_fee_sale_month),
                    total_cost = COALESCE((p_short_property_data->>'total_cost')::NUMERIC, total_cost),
                    employee_commission_sale = COALESCE((p_short_property_data->>'employee_commission_sale')::NUMERIC, employee_commission_sale),
                    responsible_employee = COALESCE((p_short_property_data->>'responsible_employee')::TEXT, responsible_employee),
                    total_units = COALESCE((p_short_property_data->>'total_units')::NUMERIC, total_units),
                    purchase_annual_rent = COALESCE((p_short_property_data->>'purchase_annual_rent')::NUMERIC, purchase_annual_rent),
                    sale_estimated_annual_rent = COALESCE((p_short_property_data->>'sale_estimated_annual_rent')::NUMERIC, sale_estimated_annual_rent),
                    sale_estimated_yield = COALESCE((p_short_property_data->>'sale_estimated_yield')::NUMERIC, sale_estimated_yield),
                    loan_repayment_amount = COALESCE((p_short_property_data->>'loan_repayment_amount')::NUMERIC, loan_repayment_amount),
                    loan_repayment_month = COALESCE((p_short_property_data->>'loan_repayment_month')::NUMERIC, loan_repayment_month),
                    down_payment_sale_tax_incl = COALESCE((p_short_property_data->>'down_payment_sale_tax_incl')::NUMERIC, down_payment_sale_tax_incl),
                    down_payment_sale_tax_incl_month = COALESCE((p_short_property_data->>'down_payment_sale_tax_incl_month')::NUMERIC, down_payment_sale_tax_incl_month),
                    balance_payment_sale_tax_incl = COALESCE((p_short_property_data->>'balance_payment_sale_tax_incl')::NUMERIC, balance_payment_sale_tax_incl),
                    balance_payment_sale_tax_incl_month = COALESCE((p_short_property_data->>'balance_payment_sale_tax_incl_month')::NUMERIC, balance_payment_sale_tax_incl_month),
                    best_sale_amount = COALESCE((p_short_property_data->>'best_sale_amount')::NUMERIC, best_sale_amount),
                    best_sale_yield = COALESCE((p_short_property_data->>'best_sale_yield')::NUMERIC, best_sale_yield),
                    planned_profit_amount = COALESCE((p_short_property_data->>'planned_profit_amount')::NUMERIC, planned_profit_amount),
                    planned_profit_rate = COALESCE((p_short_property_data->>'planned_profit_rate')::NUMERIC, planned_profit_rate),
                    best_profit_amount = COALESCE((p_short_property_data->>'best_profit_amount')::NUMERIC, best_profit_amount),
                    best_profit_rate = COALESCE((p_short_property_data->>'best_profit_rate')::NUMERIC, best_profit_rate),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_short_property_id;
            ELSE
                -- 新規レコード作成
                INSERT INTO public.short_properties (
                    property_id, client_id, sales_method, nearest_station, 
                    progress_status, property_type, station_distance_min, 
                    lot_number, map_url, land_area_tsubo, land_use_zone,
                    building_coverage_ratio, floor_area_ratio, road_width, road_setback,
                    floor_plan, building_floors, room_number, consumption_tax_amount,
                    down_payment_tax_incl, down_payment_tax_incl_month,
                    balance_payment_tax_incl, balance_payment_tax_incl_month,
                    brokerage_fee_purchase, brokerage_fee_purchase_month,
                    registration_fee_purchase, registration_fee_purchase_month,
                    real_estate_tax_purchase, real_estate_tax_purchase_month,
                    stamp_duty_purchase, stamp_duty_purchase_month,
                    loan_fee_purchase, loan_fee_purchase_month,
                    property_tax, property_tax_month, fire_insurance, fire_insurance_month,
                    survey_cost, survey_cost_month, demolition_cost, demolition_cost_month,
                    renovation_cost, renovation_cost_month, management_fee, management_fee_month,
                    other_land_expenses, other_land_expenses_month,
                    other_building_expenses, other_building_expenses_month,
                    other_expenses, other_expenses_month,
                    brokerage_fee_sale, brokerage_fee_sale_month,
                    advertising_fee_sale, advertising_fee_sale_month,
                    bank_expenses_sale, bank_expenses_sale_month,
                    mortgage_cancellation_fee, mortgage_cancellation_fee_month,
                    registration_fee_sale, registration_fee_sale_month,
                    total_cost, employee_commission_sale, responsible_employee,
                    total_units, purchase_annual_rent, sale_estimated_annual_rent,
                    sale_estimated_yield, loan_repayment_amount, loan_repayment_month,
                    down_payment_sale_tax_incl, down_payment_sale_tax_incl_month,
                    balance_payment_sale_tax_incl, balance_payment_sale_tax_incl_month,
                    best_sale_amount, best_sale_yield, planned_profit_amount,
                    planned_profit_rate, best_profit_amount, best_profit_rate
                ) VALUES (
                    p_id, v_client_id,
                    (p_short_property_data->>'sales_method')::TEXT,
                    (p_short_property_data->>'nearest_station')::TEXT,
                    (p_short_property_data->>'progress_status')::TEXT,
                    (p_short_property_data->>'property_type')::TEXT,
                    (p_short_property_data->>'station_distance_min')::NUMERIC,
                    (p_short_property_data->>'lot_number')::TEXT,
                    (p_short_property_data->>'map_url')::TEXT,
                    (p_short_property_data->>'land_area_tsubo')::NUMERIC,
                    (p_short_property_data->>'land_use_zone')::TEXT,
                    (p_short_property_data->>'building_coverage_ratio')::NUMERIC,
                    (p_short_property_data->>'floor_area_ratio')::NUMERIC,
                    (p_short_property_data->>'road_width')::NUMERIC,
                    (p_short_property_data->>'road_setback')::NUMERIC,
                    (p_short_property_data->>'floor_plan')::TEXT,
                    (p_short_property_data->>'building_floors')::TEXT,
                    (p_short_property_data->>'room_number')::TEXT,
                    (p_short_property_data->>'consumption_tax_amount')::NUMERIC,
                    (p_short_property_data->>'down_payment_tax_incl')::NUMERIC,
                    (p_short_property_data->>'down_payment_tax_incl_month')::NUMERIC,
                    (p_short_property_data->>'balance_payment_tax_incl')::NUMERIC,
                    (p_short_property_data->>'balance_payment_tax_incl_month')::NUMERIC,
                    (p_short_property_data->>'brokerage_fee_purchase')::NUMERIC,
                    (p_short_property_data->>'brokerage_fee_purchase_month')::NUMERIC,
                    (p_short_property_data->>'registration_fee_purchase')::NUMERIC,
                    (p_short_property_data->>'registration_fee_purchase_month')::NUMERIC,
                    (p_short_property_data->>'real_estate_tax_purchase')::NUMERIC,
                    (p_short_property_data->>'real_estate_tax_purchase_month')::NUMERIC,
                    (p_short_property_data->>'stamp_duty_purchase')::NUMERIC,
                    (p_short_property_data->>'stamp_duty_purchase_month')::NUMERIC,
                    (p_short_property_data->>'loan_fee_purchase')::NUMERIC,
                    (p_short_property_data->>'loan_fee_purchase_month')::NUMERIC,
                    (p_short_property_data->>'property_tax')::NUMERIC,
                    (p_short_property_data->>'property_tax_month')::NUMERIC,
                    (p_short_property_data->>'fire_insurance')::NUMERIC,
                    (p_short_property_data->>'fire_insurance_month')::NUMERIC,
                    (p_short_property_data->>'survey_cost')::NUMERIC,
                    (p_short_property_data->>'survey_cost_month')::NUMERIC,
                    (p_short_property_data->>'demolition_cost')::NUMERIC,
                    (p_short_property_data->>'demolition_cost_month')::NUMERIC,
                    (p_short_property_data->>'renovation_cost')::NUMERIC,
                    (p_short_property_data->>'renovation_cost_month')::NUMERIC,
                    (p_short_property_data->>'management_fee')::NUMERIC,
                    (p_short_property_data->>'management_fee_month')::NUMERIC,
                    (p_short_property_data->>'other_land_expenses')::NUMERIC,
                    (p_short_property_data->>'other_land_expenses_month')::NUMERIC,
                    (p_short_property_data->>'other_building_expenses')::NUMERIC,
                    (p_short_property_data->>'other_building_expenses_month')::NUMERIC,
                    (p_short_property_data->>'other_expenses')::NUMERIC,
                    (p_short_property_data->>'other_expenses_month')::NUMERIC,
                    (p_short_property_data->>'brokerage_fee_sale')::NUMERIC,
                    (p_short_property_data->>'brokerage_fee_sale_month')::NUMERIC,
                    (p_short_property_data->>'advertising_fee_sale')::NUMERIC,
                    (p_short_property_data->>'advertising_fee_sale_month')::NUMERIC,
                    (p_short_property_data->>'bank_expenses_sale')::NUMERIC,
                    (p_short_property_data->>'bank_expenses_sale_month')::NUMERIC,
                    (p_short_property_data->>'mortgage_cancellation_fee')::NUMERIC,
                    (p_short_property_data->>'mortgage_cancellation_fee_month')::NUMERIC,
                    (p_short_property_data->>'registration_fee_sale')::NUMERIC,
                    (p_short_property_data->>'registration_fee_sale_month')::NUMERIC,
                    (p_short_property_data->>'total_cost')::NUMERIC,
                    (p_short_property_data->>'employee_commission_sale')::NUMERIC,
                    (p_short_property_data->>'responsible_employee')::TEXT,
                    (p_short_property_data->>'total_units')::NUMERIC,
                    (p_short_property_data->>'purchase_annual_rent')::NUMERIC,
                    (p_short_property_data->>'sale_estimated_annual_rent')::NUMERIC,
                    (p_short_property_data->>'sale_estimated_yield')::NUMERIC,
                    (p_short_property_data->>'loan_repayment_amount')::NUMERIC,
                    (p_short_property_data->>'loan_repayment_month')::NUMERIC,
                    (p_short_property_data->>'down_payment_sale_tax_incl')::NUMERIC,
                    (p_short_property_data->>'down_payment_sale_tax_incl_month')::NUMERIC,
                    (p_short_property_data->>'balance_payment_sale_tax_incl')::NUMERIC,
                    (p_short_property_data->>'balance_payment_sale_tax_incl_month')::NUMERIC,
                    (p_short_property_data->>'best_sale_amount')::NUMERIC,
                    (p_short_property_data->>'best_sale_yield')::NUMERIC,
                    (p_short_property_data->>'planned_profit_amount')::NUMERIC,
                    (p_short_property_data->>'planned_profit_rate')::NUMERIC,
                    (p_short_property_data->>'best_profit_amount')::NUMERIC,
                    (p_short_property_data->>'best_profit_rate')::NUMERIC
                );
            END IF;
        END IF;
    END IF;

    -- 長期物件情報の更新（type=2の場合）
    IF p_type = 2 OR (p_type IS NULL AND (SELECT type FROM public.properties WHERE id = p_id) = 2) THEN
        -- 長期物件データがある場合は更新
        IF p_long_property_data IS NOT NULL THEN
            -- 既存レコードの確認
            SELECT id INTO v_long_property_id 
            FROM public.long_properties 
            WHERE property_id = p_id AND is_deleted = false;

            IF v_long_property_id IS NOT NULL THEN
                -- 既存レコードを更新
                UPDATE public.long_properties SET
                    property_name = COALESCE((p_long_property_data->>'property_name')::TEXT, property_name),
                    current_rental_income = COALESCE((p_long_property_data->>'current_rental_income')::NUMERIC, current_rental_income),
                    full_occupancy_income = COALESCE((p_long_property_data->>'full_occupancy_income')::NUMERIC, full_occupancy_income),
                    yield_rate = COALESCE((p_long_property_data->>'yield_rate')::NUMERIC, yield_rate),
                    useful_life = COALESCE((p_long_property_data->>'useful_life')::INT, useful_life),
                    economic_useful_life = COALESCE((p_long_property_data->>'economic_useful_life')::NUMERIC, economic_useful_life),
                    repair_history = COALESCE((p_long_property_data->>'repair_history')::VARCHAR, repair_history),
                    management_fee = COALESCE((p_long_property_data->>'management_fee')::NUMERIC, management_fee),
                    repair_cost = COALESCE((p_long_property_data->>'repair_cost')::NUMERIC, repair_cost),
                    property_tax = COALESCE((p_long_property_data->>'property_tax')::NUMERIC, property_tax),
                    utility_cost = COALESCE((p_long_property_data->>'utility_cost')::NUMERIC, utility_cost),
                    depreciation = COALESCE((p_long_property_data->>'depreciation')::NUMERIC, depreciation),
                    other_expenses = COALESCE((p_long_property_data->>'other_expenses')::NUMERIC, other_expenses),
                    current_balance = COALESCE((p_long_property_data->>'current_balance')::NUMERIC, current_balance),
                    full_occupancy_balance = COALESCE((p_long_property_data->>'full_occupancy_balance')::NUMERIC, full_occupancy_balance),
                    repair_fund_total = COALESCE((p_long_property_data->>'repair_fund_total')::NUMERIC, repair_fund_total),
                    current_loan_balance = COALESCE((p_long_property_data->>'current_loan_balance')::NUMERIC, current_loan_balance),
                    annual_principal_payment = COALESCE((p_long_property_data->>'annual_principal_payment')::NUMERIC, annual_principal_payment),
                    annual_interest_payment = COALESCE((p_long_property_data->>'annual_interest_payment')::NUMERIC, annual_interest_payment),
                    collateral_status = COALESCE((p_long_property_data->>'collateral_status')::VARCHAR, collateral_status),
                    is_deleted = COALESCE((p_long_property_data->>'is_deleted')::BOOLEAN, is_deleted),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_long_property_id;
            ELSE
                -- 新規レコード作成
                INSERT INTO public.long_properties (
                    property_id, client_id, property_name, current_rental_income,
                    full_occupancy_income, yield_rate, useful_life, economic_useful_life,
                    repair_history, management_fee, repair_cost, property_tax,
                    utility_cost, depreciation, other_expenses, current_balance,
                    full_occupancy_balance, repair_fund_total, current_loan_balance,
                    annual_principal_payment, annual_interest_payment, collateral_status,
                    is_deleted
                ) VALUES (
                    p_id, v_client_id,
                    (p_long_property_data->>'property_name')::TEXT,
                    (p_long_property_data->>'current_rental_income')::NUMERIC,
                    (p_long_property_data->>'full_occupancy_income')::NUMERIC,
                    (p_long_property_data->>'yield_rate')::NUMERIC,
                    (p_long_property_data->>'useful_life')::INT,
                    (p_long_property_data->>'economic_useful_life')::NUMERIC,
                    (p_long_property_data->>'repair_history')::VARCHAR,
                    (p_long_property_data->>'management_fee')::NUMERIC,
                    (p_long_property_data->>'repair_cost')::NUMERIC,
                    (p_long_property_data->>'property_tax')::NUMERIC,
                    (p_long_property_data->>'utility_cost')::NUMERIC,
                    (p_long_property_data->>'depreciation')::NUMERIC,
                    (p_long_property_data->>'other_expenses')::NUMERIC,
                    (p_long_property_data->>'current_balance')::NUMERIC,
                    (p_long_property_data->>'full_occupancy_balance')::NUMERIC,
                    (p_long_property_data->>'repair_fund_total')::NUMERIC,
                    (p_long_property_data->>'current_loan_balance')::NUMERIC,
                    (p_long_property_data->>'annual_principal_payment')::NUMERIC,
                    (p_long_property_data->>'annual_interest_payment')::NUMERIC,
                    (p_long_property_data->>'collateral_status')::VARCHAR,
                    COALESCE((p_long_property_data->>'is_deleted')::BOOLEAN, false)
                );
            END IF;
        END IF;
    END IF;

    -- 相談銀行情報の更新
    IF p_consultation_banks IS NOT NULL THEN
        -- 既存の相談銀行情報を論理削除
        UPDATE public.consultation_banks 
        SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE property_id = p_id AND deleted_at IS NULL;

        -- 新しい相談銀行情報を挿入
        FOR v_consultation_bank IN SELECT * FROM jsonb_array_elements(p_consultation_banks)
        LOOP
            INSERT INTO public.consultation_banks (
                property_id, client_id, bank_code, bank_name, 
                bank_name_half_kana, bank_name_full_kana, bank_name_hiragana,
                bank_business_type_code, bank_business_type,
                consultation_date, response_date, response_days, response_amount,
                response_interest_rate, response_loan_ratio, remarks
            ) VALUES (
                p_id, v_client_id,
                (v_consultation_bank->>'bank_code')::TEXT,
                (v_consultation_bank->>'bank_name')::TEXT,
                (v_consultation_bank->>'bank_name_half_kana')::TEXT,
                (v_consultation_bank->>'bank_name_full_kana')::TEXT,
                (v_consultation_bank->>'bank_name_hiragana')::TEXT,
                (v_consultation_bank->>'bank_business_type_code')::TEXT,
                (v_consultation_bank->>'bank_business_type')::TEXT,
                (v_consultation_bank->>'consultation_date')::DATE,
                (v_consultation_bank->>'response_date')::DATE,
                (v_consultation_bank->>'response_days')::INT,
                (v_consultation_bank->>'response_amount')::NUMERIC,
                (v_consultation_bank->>'response_interest_rate')::NUMERIC,
                (v_consultation_bank->>'response_loan_ratio')::NUMERIC,
                (v_consultation_bank->>'remarks')::TEXT
            );
        END LOOP;
    END IF;

    RETURN jsonb_build_object(
        'success', true,
        'property_id', p_id,
        'message', '物件情報が正常に更新されました'
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'データベースエラー: ' || SQLERRM
        );
END;
$$;
