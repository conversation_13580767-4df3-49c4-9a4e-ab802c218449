-- =====================================
-- 長期物件テーブル
-- =====================================
CREATE TABLE public.long_properties (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 長期物件のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE, -- 物件ID（外部キー）

    -- 賃貸運営情報
    property_name            varchar      DEFAULT NULL,                          -- 物件
    current_rental_income    numeric      DEFAULT NULL,                          -- 現状賃貸収入（年間）
    full_occupancy_income    numeric      DEFAULT NULL,                          -- 満室想定賃貸収入（年間）
    yield_rate               numeric      DEFAULT NULL,                          -- 利回り

    -- 建物管理
    useful_life              numeric      DEFAULT NULL,                          -- 耐用年数
    economic_useful_life     numeric      DEFAULT NULL,                          -- 経済的耐用年数
    repair_history           varchar      DEFAULT NULL,                          -- 修繕履歴

    -- 運営経費
    management_fee           numeric      DEFAULT NULL,                          -- 経費（管理費）
    repair_cost              numeric      DEFAULT NULL,                          -- 経費（修繕費）
    property_tax             numeric      DEFAULT NULL,                          -- 経費（固定資産税）
    utility_cost             numeric      DEFAULT NULL,                          -- 経費（水道光熱費）
    depreciation             numeric      DEFAULT NULL,                          -- 経費（減価償却費）
    other_expenses           numeric      DEFAULT NULL,                          -- 経費（その他）

    -- 収支管理
    current_balance          numeric      DEFAULT NULL,                          -- 収支（現状）
    full_occupancy_balance   numeric      DEFAULT NULL,                          -- 収支（満室想定）
    repair_fund_total        numeric      DEFAULT NULL,                          -- 修繕費拠出額累計

    -- 融資管理
    current_loan_balance     numeric      DEFAULT NULL,                          -- 借入残高（現時点）
    annual_principal_payment numeric      DEFAULT NULL,                          -- 元本返済額（年間）
    annual_interest_payment  numeric      DEFAULT NULL,                          -- 支払金利額（年間）
    collateral_status        varchar      DEFAULT NULL,                          -- 担保状況

    -- 削除フラグ
    is_deleted               boolean      DEFAULT FALSE,                         -- 削除

    -- ステータス管理
    deleted_at               timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at               timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at               timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にする
ALTER TABLE public.long_properties ENABLE ROW LEVEL SECURITY;

-- =====================================
-- 長期物件テーブル用 RLS ポリシー
-- =====================================

-- SELECT: ユーザーが自分が所属するクライアントに紐づく長期物件情報を参照できる
CREATE POLICY "Users can view long_properties for their clients." 
ON public.long_properties
FOR SELECT 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- INSERT: ユーザーが自分が所属するクライアントに紐づく長期物件を追加できる
CREATE POLICY "Users can insert long_properties for their clients." 
ON public.long_properties
FOR INSERT 
WITH CHECK (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- UPDATE: ユーザーが自分が所属するクライアントに紐づく長期物件を更新できる
CREATE POLICY "Users can update long_properties for their clients." 
ON public.long_properties
FOR UPDATE 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- DELETE: ユーザーが自分が所属するクライアントに紐づく長期物件を削除できる
CREATE POLICY "Users can delete long_properties for their clients." 
ON public.long_properties
FOR DELETE 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- long_properties更新日トリガー作成
-- long_properties テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
CREATE TRIGGER handle_updated_at_long_properties
BEFORE UPDATE ON long_properties  -- 更新"前"に発火
FOR EACH ROW                       -- 行単位で実行
EXECUTE FUNCTION update_modified_column();  -- 既存の関数を実行

-- 論理削除関数（API呼び出し用）
-- CREATE OR REPLACE FUNCTION soft_delete_long_property(long_property_uuid UUID)
-- RETURNS BOOLEAN AS $$
-- DECLARE
--     affected_rows INTEGER;
-- BEGIN
--     UPDATE public.long_properties 
--     SET deleted_at = NOW(), updated_at = NOW()
--     WHERE id = long_property_uuid 
--         AND deleted_at IS NULL
--         AND client_id IN (
--             SELECT client_id 
--             FROM public.profiles 
--             WHERE id = auth.uid()
--         );
    
--     GET DIAGNOSTICS affected_rows = ROW_COUNT;
--     RETURN affected_rows > 0;
-- END;
-- $$ LANGUAGE plpgsql SECURITY DEFINER;