-- =====================================
-- 短期物件テーブル
-- =====================================
CREATE TABLE public.short_properties (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 短期物件のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE, -- 物件ID（外部キー）

    -- 物件詳細情報
    sales_method             varchar      DEFAULT NULL,                          -- 販売手法
    nearest_station          varchar      DEFAULT NULL,                          -- 最寄り駅
    progress_status          varchar      DEFAULT NULL,                          -- 進捗状況
    property_type            varchar      DEFAULT NULL,                          -- 物件種別
    station_distance_min     numeric      DEFAULT NULL,                          -- 駅距離（分）
    lot_number               varchar      DEFAULT NULL,                          -- 地番
    map_url                  varchar      DEFAULT NULL,                          -- 地図URL
    land_area_tsubo          numeric      DEFAULT NULL,                          -- 土地面積（坪）
    land_use_zone            varchar      DEFAULT NULL,                          -- 用途地域
    building_coverage_ratio  numeric      DEFAULT NULL,                          -- 建蔽率
    floor_area_ratio         numeric      DEFAULT NULL,                          -- 容積率
    road_width               numeric      DEFAULT NULL,                          -- 道路幅員
    road_setback             numeric      DEFAULT NULL,                          -- 道路後退
    floor_plan               varchar      DEFAULT NULL,                          -- 間取り
    building_floors          varchar      DEFAULT NULL,                          -- 階建て
    room_number              varchar      DEFAULT NULL,                          -- 部屋番号

    -- 購入時費用
    consumption_tax_amount   numeric      DEFAULT NULL,                          -- うち消費税額
    down_payment_tax_incl    numeric      DEFAULT NULL,                          -- （うち手付金・税込）
    down_payment_tax_incl_month numeric   DEFAULT NULL,                          -- （うち手付金・税込）月
    balance_payment_tax_incl numeric      DEFAULT NULL,                          -- （うち残代金・税込）
    balance_payment_tax_incl_month numeric DEFAULT NULL,                         -- （うち残代金・税込）月
    brokerage_fee_purchase   numeric      DEFAULT NULL,                          -- 仲介手数料（購入時）
    brokerage_fee_purchase_month numeric  DEFAULT NULL,                          -- 仲介手数料（購入時）月
    registration_fee_purchase numeric     DEFAULT NULL,                          -- 登記費用（購入時）
    registration_fee_purchase_month numeric DEFAULT NULL,                        -- 登記費用（購入時）月
    real_estate_tax_purchase numeric      DEFAULT NULL,                          -- 不動産取得税（購入時）
    real_estate_tax_purchase_month numeric DEFAULT NULL,                         -- 不動産取得税（購入時）月
    stamp_duty_purchase      numeric      DEFAULT NULL,                          -- 印紙代（購入時）
    stamp_duty_purchase_month numeric     DEFAULT NULL,                          -- 印紙代（購入時）月
    loan_fee_purchase        numeric      DEFAULT NULL,                          -- 借入手数料（購入時）
    loan_fee_purchase_month  numeric      DEFAULT NULL,                          -- 借入手数料（購入時）月

    -- 保有時費用
    property_tax             numeric      DEFAULT NULL,                          -- 固定資産税
    property_tax_month       numeric      DEFAULT NULL,                          -- 固定資産税月
    fire_insurance           numeric      DEFAULT NULL,                          -- 火災保険料
    fire_insurance_month     numeric      DEFAULT NULL,                          -- 火災保険料月
    survey_cost              numeric      DEFAULT NULL,                          -- 測量費
    survey_cost_month        numeric      DEFAULT NULL,                          -- 測量費月
    demolition_cost          numeric      DEFAULT NULL,                          -- 解体費
    demolition_cost_month    numeric      DEFAULT NULL,                          -- 解体費月
    renovation_cost          numeric      DEFAULT NULL,                          -- リフォーム費
    renovation_cost_month    numeric      DEFAULT NULL,                          -- リフォーム費月
    management_fee           numeric      DEFAULT NULL,                          -- 業務委託費
    management_fee_month     numeric      DEFAULT NULL,                          -- 業務委託費月
    other_land_expenses      numeric      DEFAULT NULL,                          -- その他土地関連費用
    other_land_expenses_month numeric     DEFAULT NULL,                          -- その他土地関連費用月
    other_building_expenses  numeric      DEFAULT NULL,                          -- その他建物関連費用
    other_building_expenses_month numeric  DEFAULT NULL,                         -- その他建物関連費用月
    other_expenses           numeric      DEFAULT NULL,                          -- その他諸経費
    other_expenses_month     numeric      DEFAULT NULL,                          -- その他諸経費月

    -- 売却時費用
    brokerage_fee_sale       numeric      DEFAULT NULL,                          -- 仲介手数料（売却時）
    brokerage_fee_sale_month numeric      DEFAULT NULL,                          -- 仲介手数料（売却時）月
    advertising_fee_sale     numeric      DEFAULT NULL,                          -- 広告宣伝費（売却時）
    advertising_fee_sale_month numeric    DEFAULT NULL,                          -- 広告宣伝費（売却時）月
    bank_expenses_sale       numeric      DEFAULT NULL,                          -- 銀行諸経費（売却時）
    bank_expenses_sale_month numeric      DEFAULT NULL,                          -- 銀行諸経費（売却時）月
    mortgage_cancellation_fee numeric     DEFAULT NULL,                          -- 抵当権抹消費用（売却時）
    mortgage_cancellation_fee_month numeric DEFAULT NULL,                        -- 抵当権抹消費用（売却時）月
    registration_fee_sale    numeric      DEFAULT NULL,                          -- 登記費用（売却時）
    registration_fee_sale_month numeric   DEFAULT NULL,                          -- 登記費用（売却時）月

    -- 収益・利益分析
    total_cost               numeric      DEFAULT NULL,                          -- 合計原価額
    employee_commission_sale numeric      DEFAULT NULL,                          -- 社員成功報酬（売却時）
    responsible_employee     varchar      DEFAULT NULL,                          -- 担当社員名
    total_units              numeric      DEFAULT NULL,                          -- 総戸数
    purchase_annual_rent     numeric      DEFAULT NULL,                          -- 仕入時年間賃料
    sale_estimated_annual_rent numeric    DEFAULT NULL,                          -- 販売時年間賃料（想定）
    sale_estimated_yield     numeric      DEFAULT NULL,                          -- ❶売却時想定利回り
    loan_repayment_amount    numeric      DEFAULT NULL,                          -- （うち借入返済額）
    loan_repayment_month     numeric      DEFAULT NULL,                          -- （うち借入返済額）月
    down_payment_sale_tax_incl numeric    DEFAULT NULL,                          -- （うち手付金・税込）
    down_payment_sale_tax_incl_month numeric DEFAULT NULL,                       -- （うち手付金・税込）月
    balance_payment_sale_tax_incl numeric DEFAULT NULL,                          -- （うち残代金・税込）
    balance_payment_sale_tax_incl_month numeric DEFAULT NULL,                    -- （うち残代金・税込）月
    best_sale_amount         numeric      DEFAULT NULL,                          -- ②売却ベスト金額
    best_sale_yield          numeric      DEFAULT NULL,                          -- ❷売却時ベスト利回り
    planned_profit_amount    numeric      DEFAULT NULL,                          -- ①予定利益額
    planned_profit_rate      numeric      DEFAULT NULL,                          -- 予定利益率
    best_profit_amount       numeric      DEFAULT NULL,                          -- ②ベスト利益額
    best_profit_rate         numeric      DEFAULT NULL,                          -- 予定利益率

    -- ステータス管理
    deleted_at               timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at               timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at               timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にする
ALTER TABLE public.short_properties ENABLE ROW LEVEL SECURITY;

-- =====================================
-- 短期物件テーブル用 RLS ポリシー
-- =====================================

-- SELECT: ユーザーが自分が所属するクライアントに紐づく短期物件情報を参照できる
CREATE POLICY "Users can view short_properties for their clients." 
ON public.short_properties
FOR SELECT 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- INSERT: ユーザーが自分が所属するクライアントに紐づく短期物件を追加できる
CREATE POLICY "Users can insert short_properties for their clients." 
ON public.short_properties
FOR INSERT 
WITH CHECK (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- UPDATE: ユーザーが自分が所属するクライアントに紐づく短期物件を更新できる
CREATE POLICY "Users can update short_properties for their clients." 
ON public.short_properties
FOR UPDATE 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- DELETE: ユーザーが自分が所属するクライアントに紐づく短期物件を削除できる
CREATE POLICY "Users can delete short_properties for their clients." 
ON public.short_properties
FOR DELETE 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- short_properties更新日トリガー作成
-- short_properties テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
CREATE TRIGGER handle_updated_at_short_properties
BEFORE UPDATE ON short_properties  -- 更新"前"に発火
FOR EACH ROW                             -- 行単位で実行
EXECUTE FUNCTION update_modified_column();  -- 既存の関数を実行

-- 論理削除関数（API呼び出し用）
-- CREATE OR REPLACE FUNCTION soft_delete_short_property(short_property_uuid UUID)
-- RETURNS BOOLEAN AS $$
-- DECLARE
--     affected_rows INTEGER;
-- BEGIN
--     UPDATE public.short_properties 
--     SET deleted_at = NOW(), updated_at = NOW()
--     WHERE id = short_property_uuid 
--         AND deleted_at IS NULL
--         AND client_id IN (
--             SELECT client_id 
--             FROM public.profiles 
--             WHERE id = auth.uid()
--         );
    
--     GET DIAGNOSTICS affected_rows = ROW_COUNT;
--     RETURN affected_rows > 0;
-- END;
-- $$ LANGUAGE plpgsql SECURITY DEFINER; 