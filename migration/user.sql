-- 関数が既に存在する場合は削除（正確なパラメータ型を指定）
DROP FUNCTION IF EXISTS public.get_users_with_profiles(VA<PERSON><PERSON><PERSON>, VARCHAR, SMALLINT);
DROP FUNCTION IF EXISTS public.get_users_with_profiles(VARC<PERSON><PERSON>, VARCHA<PERSON>, SMALLINT, VARCHAR);
DROP FUNCTION IF EXISTS public.get_user_by_profile_id(UUID);

-- ユーザー一覧取得関数（profiles + auth.users + clients結合）
CREATE OR REPLACE FUNCTION public.get_users_with_profiles(
    search_user_name VARCHAR DEFAULT NULL,    -- ユーザー名での部分検索
    search_user_email VARCHAR DEFAULT NULL,   -- メールアドレスでの部分検索
    search_role_filter SMALLINT DEFAULT NULL, -- ロールでのフィルタ
    search_client_name VARCHAR DEFAULT NULL   -- クライアント名での部分検索
)
RETURNS TABLE (
    profile_id UUID,
    user_id UUID,
    client_id UUID,
    client_name VA<PERSON>HAR,
    user_name VA<PERSON>HA<PERSON>,
    user_email VARCHAR,
    role SMALLINT,
    remarks TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id AS profile_id,
        u.id AS user_id,
        p.client_id AS client_id,
        c.name AS client_name,
        p.name AS user_name,
        u.email AS user_email,
        p.role AS role,
        p.remarks AS remarks
    FROM
        public.profiles AS p
    INNER JOIN
        auth.users AS u ON p.id = u.id
    LEFT JOIN
        public.clients AS c ON p.client_id = c.id
    WHERE
        -- ユーザー名での部分検索（profilesテーブルのnameフィールドから）
        (search_user_name IS NULL OR search_user_name = '' OR p.name ILIKE '%' || search_user_name || '%')
        AND
        -- メールアドレスでの部分検索
        (search_user_email IS NULL OR search_user_email = '' OR u.email ILIKE '%' || search_user_email || '%')
        AND
        -- ロールでのフィルタ（profilesテーブルのroleフィールドから）
        (search_role_filter IS NULL OR p.role = search_role_filter)
        AND
        -- クライアント名での部分検索
        (search_client_name IS NULL OR search_client_name = '' OR c.name ILIKE '%' || search_client_name || '%')
    ORDER BY
        p.updated_at DESC;
END;
$$;

-- 特定ユーザー取得関数（profile_idベース）
CREATE OR REPLACE FUNCTION public.get_user_by_profile_id(
    p_profile_id UUID
)
RETURNS TABLE (
    profile_id UUID,
    user_id UUID,
    client_id UUID,
    client_name VARCHAR,
    user_name VARCHAR,
    user_email VARCHAR,
    role SMALLINT,
    remarks TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id AS profile_id,
        u.id AS user_id,
        p.client_id AS client_id,
        c.name AS client_name,
        p.name AS user_name,
        u.email AS user_email,
        p.role AS role,
        p.remarks AS remarks
    FROM
        public.profiles AS p
    INNER JOIN
        auth.users AS u ON p.id = u.id
    LEFT JOIN
        public.clients AS c ON p.client_id = c.id
    WHERE
        p.id = p_profile_id;
END;
$$;

-- この関数を public ロールから実行できるように権限を付与します。
-- service_role キーで呼び出す場合は必須ではありませんが、念のため設定しておくと良いでしょう。
-- GRANT EXECUTE ON FUNCTION public.get_users_with_profiles() TO public;
-- GRANT EXECUTE ON FUNCTION public.get_user_by_profile_id() TO public;