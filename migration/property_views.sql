-- =====================================
-- 物件一覧表示・検索関数
-- =====================================

-- 既存の関数を削除（もし存在する場合）
DROP FUNCTION IF EXISTS public.get_properties_with_details(VARCHAR, VARCHAR, NUMERIC, VARCHAR, BOOLEAN);
DROP FUNCTION IF EXISTS public.get_properties_with_details();
DROP FUNCTION IF EXISTS public.get_property_by_id(UUID);
DROP FUNCTION IF EXISTS public.get_property_with_all_details(UUID);
-- DROP FUNCTION IF EXISTS public.update_property_with_details;
-- DROP FUNCTION IF EXISTS public.create_property_with_details;

-- 物件一覧取得関数（properties + short_properties + long_properties + clients結合）
CREATE OR REPLACE FUNCTION public.get_properties_with_details(
    search_loan_bank VARCHAR DEFAULT NULL,        -- 借入銀行での部分検索
    search_client_name VARCHAR DEFAULT NULL,      -- 会社名での部分検索
    search_loan_ratio NUMERIC DEFAULT NULL,       -- 借入割合での検索
    search_progress_status VARCHAR DEFAULT NULL,  -- 進捗状況での部分検索
    include_deleted BOOLEAN DEFAULT FALSE         -- 削除済み項目を含めるかどうか
)
RETURNS TABLE (
    property_id UUID,
    client_id UUID,
    client_name VARCHAR,
    
    -- 一覧表示項目（新しい順序）
    sales_method VARCHAR,                     -- 1. 販売手法
    property_type VARCHAR,                    -- 2. 物件種別
    address VARCHAR,                          -- 3. 住所
    fixed_asset_tax_land NUMERIC,            -- 4. 固定資産税評価額（土地）
    fixed_asset_tax_building NUMERIC,        -- 5. 固定資産税評価額（建物）
    fixed_asset_tax_total NUMERIC,           -- 6. 固定資産税評価額合計
    land_area_sqm NUMERIC,                    -- 7. 土地面積（㎡）
    building_area_sqm NUMERIC,               -- 8. 建物延べ面積(㎡）
    structure VARCHAR,                        -- 9. 構造
    construction_date DATE,                   -- 10. 築年月日
    building_age NUMERIC,                     -- 11. 築年数
    purchase_price NUMERIC,                  -- 12. 購入金額（税込）
    loan_bank_name VARCHAR,                   -- 13. 借入銀行
    loan_amount NUMERIC,                      -- 14. 借入金額
    loan_period NUMERIC,                      -- 15. 借入期間
    loan_interest_rate NUMERIC,              -- 16. 借入金利
    loan_fee_rate NUMERIC,                   -- 17. 借入手数料率
    bank_collateral_value NUMERIC,           -- 18. 銀行担保評価額
    bank_risk_amount NUMERIC,                -- 19. 銀行リスク額
    loan_ratio NUMERIC,                       -- 20. 借入割合
    progress_status VARCHAR,                  -- 21. 進捗状況
    closing_date DATE,                        -- 22. 決算日
    
    -- システム情報
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id AS property_id,
        p.client_id AS client_id,
        c.name AS client_name,
        
        -- 一覧表示項目（指定された順序）
        sp.sales_method AS sales_method,                    -- 1. 販売手法
        sp.property_type AS property_type,                  -- 2. 物件種別
        p.address AS address,                               -- 3. 住所
        p.fixed_asset_tax_land AS fixed_asset_tax_land,     -- 4. 固定資産税評価額（土地）
        p.fixed_asset_tax_building AS fixed_asset_tax_building, -- 5. 固定資産税評価額（建物）
        p.fixed_asset_tax_total AS fixed_asset_tax_total,   -- 6. 固定資産税評価額合計
        p.land_area_sqm AS land_area_sqm,                   -- 7. 土地面積（㎡）
        p.building_area_sqm AS building_area_sqm,           -- 8. 建物延べ面積(㎡）
        p.structure AS structure,                           -- 9. 構造
        p.construction_date AS construction_date,           -- 10. 築年月日
        p.building_age AS building_age,                     -- 11. 築年数
        p.purchase_price AS purchase_price,                 -- 12. 購入金額（税込）
        cb.bank_name AS loan_bank_name,                     -- 13. 借入銀行
        p.loan_amount AS loan_amount,                       -- 14. 借入金額
        p.loan_period AS loan_period,                       -- 15. 借入期間
        p.loan_interest_rate AS loan_interest_rate,         -- 16. 借入金利
        p.loan_fee_rate AS loan_fee_rate,                   -- 17. 借入手数料率
        p.bank_collateral_value AS bank_collateral_value,   -- 18. 銀行担保評価額
        p.bank_risk_amount AS bank_risk_amount,             -- 19. 銀行リスク額
        p.loan_ratio AS loan_ratio,                         -- 20. 借入割合
        COALESCE(sp.progress_status, p.progress_status) AS progress_status, -- 21. 進捗状況
        p.closing_date AS closing_date,                     -- 22. 決算日
        
        -- システム情報
        p.created_at AS created_at,
        p.updated_at AS updated_at,
        p.deleted_at AS deleted_at
    FROM
        public.properties AS p
    LEFT JOIN
        public.short_properties AS sp ON sp.property_id = p.id
    LEFT JOIN
        public.long_properties AS lp ON lp.property_id = p.id
    LEFT JOIN
        public.clients AS c ON p.client_id = c.id
    LEFT JOIN
        public.working_capital AS cb ON p.working_capital_id = cb.id
    WHERE
        -- ユーザーが所属するクライアントの物件のみ表示
        p.client_id IN (
            SELECT pr.client_id 
            FROM public.profiles pr
            WHERE pr.id = auth.uid()
        )
        AND
        -- 借入銀行での部分検索
        (search_loan_bank IS NULL OR search_loan_bank = '' OR cb.bank_name ILIKE '%' || search_loan_bank || '%')
        AND
        -- 会社名での部分検索
        (search_client_name IS NULL OR search_client_name = '' OR c.name ILIKE '%' || search_client_name || '%')
        AND
        -- 借入割合での検索（完全一致）
        (search_loan_ratio IS NULL OR p.loan_ratio = search_loan_ratio)
        AND
        -- 進捗状況での部分検索
        (search_progress_status IS NULL OR search_progress_status = '' OR 
         p.progress_status ILIKE '%' || search_progress_status || '%' OR
         sp.progress_status ILIKE '%' || search_progress_status || '%')
        AND
        -- 削除フラグでの絞り込み
        (include_deleted = TRUE OR p.deleted_at IS NULL)
    ORDER BY
        p.updated_at DESC;
END;
$$;

-- 特定物件取得関数（property_idベース）
CREATE OR REPLACE FUNCTION public.get_property_by_id(
    p_property_id UUID
)
RETURNS TABLE (
    property_id UUID,
    client_id UUID,
    client_name VARCHAR,
    
    -- 一覧表示項目（新しい順序）
    sales_method VARCHAR,
    property_type VARCHAR,
    address VARCHAR,
    fixed_asset_tax_land NUMERIC,
    fixed_asset_tax_building NUMERIC,
    fixed_asset_tax_total NUMERIC,
    land_area_sqm NUMERIC,
    building_area_sqm NUMERIC,
    structure VARCHAR,
    construction_date DATE,
    building_age NUMERIC,
    purchase_price NUMERIC,
    loan_bank_name VARCHAR,
    loan_amount NUMERIC,
    loan_period NUMERIC,
    loan_interest_rate NUMERIC,
    loan_fee_rate NUMERIC,
    bank_collateral_value NUMERIC,
    bank_risk_amount NUMERIC,
    loan_ratio NUMERIC,
    progress_status VARCHAR,
    closing_date DATE,
    
    -- システム情報
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id AS property_id,
        p.client_id AS client_id,
        c.name AS client_name,
        
        -- 一覧表示項目（指定された順序）
        sp.sales_method AS sales_method,
        sp.property_type AS property_type,
        p.address AS address,
        p.fixed_asset_tax_land AS fixed_asset_tax_land,
        p.fixed_asset_tax_building AS fixed_asset_tax_building,
        p.fixed_asset_tax_total AS fixed_asset_tax_total,
        p.land_area_sqm AS land_area_sqm,
        p.building_area_sqm AS building_area_sqm,
        p.structure AS structure,
        p.construction_date AS construction_date,
        p.building_age AS building_age,
        p.purchase_price AS purchase_price,
        cb.bank_name AS loan_bank_name,
        p.loan_amount AS loan_amount,
        p.loan_period AS loan_period,
        p.loan_interest_rate AS loan_interest_rate,
        p.loan_fee_rate AS loan_fee_rate,
        p.bank_collateral_value AS bank_collateral_value,
        p.bank_risk_amount AS bank_risk_amount,
        p.loan_ratio AS loan_ratio,
        sp.progress_status AS progress_status,
        p.closing_date AS closing_date,
        
        -- システム情報
        p.created_at AS created_at,
        p.updated_at AS updated_at,
        p.deleted_at AS deleted_at
    FROM
        public.properties AS p
    LEFT JOIN
        public.short_properties AS sp ON sp.property_id = p.id
    LEFT JOIN
        public.long_properties AS lp ON lp.property_id = p.id
    LEFT JOIN
        public.clients AS c ON p.client_id = c.id
    LEFT JOIN
        public.working_capital AS cb ON p.working_capital_id = cb.id
    WHERE
        p.id = p_property_id
        AND
        -- ユーザーが所属するクライアントの物件のみ表示
        p.client_id IN (
            SELECT pr.client_id 
            FROM public.profiles pr
            WHERE pr.id = auth.uid()
        );
END;
$$;

-- 物件詳細データ取得関数（短期/長期/相談銀行含む）
CREATE OR REPLACE FUNCTION public.get_property_with_all_details(
    p_property_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    property_data JSON;
    short_property_data JSON;
    long_property_data JSON;
    consultation_banks_data JSON;
    result JSON;
BEGIN
    -- 基本物件情報を取得
    SELECT row_to_json(p) INTO property_data
    FROM (
        SELECT 
            p.*,
            c.name AS client_name
        FROM public.properties p
        LEFT JOIN public.clients c ON p.client_id = c.id
        WHERE p.id = p_property_id
        AND (
            -- 管理者の場合は全ての物件にアクセス可能
            EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
            OR
            -- クライアントの場合は自分のclient_idの物件のみ
            p.client_id IN (
                SELECT pr.client_id 
                FROM public.profiles pr
                WHERE pr.id = auth.uid()
            )
        )
    ) p;
    
    -- 基本物件情報が見つからない場合、詳細なデバッグ情報を返す
    IF property_data IS NULL THEN
        -- 物件が存在するかチェック（RLS無視）
        DECLARE
            exists_check BOOLEAN;
            user_id UUID;
            user_client_id UUID;
            user_role INTEGER;
        BEGIN
            SELECT auth.uid() INTO user_id;
            SELECT client_id, role INTO user_client_id, user_role FROM public.profiles WHERE id = user_id;
            SELECT EXISTS(SELECT 1 FROM public.properties WHERE id = p_property_id) INTO exists_check;
            
            RETURN json_build_object(
                'error', 'Property not found or access denied',
                'property_id', p_property_id,
                'property_exists', exists_check,
                'user_id', user_id,
                'user_client_id', user_client_id,
                'user_role', user_role,
                'property', null,
                'short_properties', null,
                'long_properties', null,
                'consultation_banks', '[]'::json
            );
        END;
    END IF;
    
    -- 短期物件情報を取得
    SELECT row_to_json(sp) INTO short_property_data
    FROM public.short_properties sp
    WHERE sp.property_id = p_property_id
    AND sp.deleted_at IS NULL;
    
    -- 長期物件情報を取得
    SELECT row_to_json(lp) INTO long_property_data
    FROM public.long_properties lp
    WHERE lp.property_id = p_property_id
    AND lp.deleted_at IS NULL;
    
    -- 相談銀行情報を取得
    SELECT json_agg(cb ORDER BY cb.created_at) INTO consultation_banks_data
    FROM public.consultation_banks cb
    WHERE cb.property_id = p_property_id
    AND cb.deleted_at IS NULL;
    
    -- 結果をJSONとして結合
    SELECT json_build_object(
        'property', property_data,
        'short_properties', COALESCE(short_property_data, 'null'::json),
        'long_properties', COALESCE(long_property_data, 'null'::json),
        'consultation_banks', COALESCE(consultation_banks_data, '[]'::json)
    ) INTO result;
    
    RETURN result;
END;
$$;

-- この関数を public ロールから実行できるように権限を付与します。
-- service_role キーで呼び出す場合は必須ではありませんが、念のため設定しておくと良いでしょう。
-- GRANT EXECUTE ON FUNCTION public.get_properties_with_details() TO public;
-- GRANT EXECUTE ON FUNCTION public.get_property_by_id() TO public;
-- GRANT EXECUTE ON FUNCTION public.get_property_with_all_details() TO public; 