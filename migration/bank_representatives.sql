-- =====================================
-- 銀行担当者テーブル
-- =====================================
CREATE TABLE public.bank_representatives (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 銀行担当者のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）

    -- 担当者情報
    representative_name      varchar      NOT NULL,                              -- 担当者名
    lending_attitude         text         DEFAULT NULL,                          -- 融資に対する姿勢

    -- 銀行情報
    bank_code                varchar      DEFAULT NULL,                          -- 銀行コード(API)
    bank_name                varchar      DEFAULT NULL,                          -- 銀行名
    bank_name_half_kana      varchar      DEFAULT NULL,                          -- 銀行名半角カタカナ
    bank_name_full_kana      varchar      DEFAULT NULL,                          -- 銀行名全角カタカナ
    bank_name_hiragana       varchar      DEFAULT NULL,                          -- 銀行名ひらがな
    bank_business_type_code  varchar      DEFAULT NULL,                          -- 銀行事業形態コード
    bank_business_type       varchar      DEFAULT NULL,                          -- 銀行事業形態種別

    -- 支店情報
    branch_code              varchar      DEFAULT NULL,                          -- 支店コード(API)
    branch_name              varchar      DEFAULT NULL,                          -- 支店名
    branch_name_half_kana    varchar      DEFAULT NULL,                          -- 支店名半角カタカナ
    branch_name_full_kana    varchar      DEFAULT NULL,                          -- 支店名全角カタカナ
    branch_name_hiragana     varchar      DEFAULT NULL,                          -- 支店名ひらがな

    -- 連絡先情報
    position                 varchar      DEFAULT NULL,                          -- 役職
    mobile_phone             varchar      DEFAULT NULL,                          -- 携帯
    contact_phone            varchar      DEFAULT NULL,                          -- 連絡先
    email_address            varchar      DEFAULT NULL,                          -- メールアドレス

    -- 備考
    remarks                  text         DEFAULT NULL,                          -- 備考

    -- ステータス管理
    deleted_at               timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at               timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at               timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にする
ALTER TABLE public.bank_representatives ENABLE ROW LEVEL SECURITY;

-- =====================================
-- 銀行担当者テーブル用 RLS ポリシー
-- =====================================

-- SELECT: 管理者は全て参照可能、クライアントは自分が所属するクライアントに紐づく銀行担当者情報のみ参照可能
CREATE POLICY "Admins can view all, users can view bank representatives for their clients." 
ON public.bank_representatives
FOR SELECT 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- INSERT: 管理者は任意のクライアントに銀行担当者を追加可能、クライアントは自分が所属するクライアントにのみ追加可能
CREATE POLICY "Admins can insert for any client, users can insert bank representatives for their clients." 
ON public.bank_representatives
FOR INSERT 
WITH CHECK (
    -- 管理者の場合は任意のclient_idで作成可能
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- UPDATE: 管理者は全ての銀行担当者を更新可能、クライアントは自分が所属するクライアントに紐づく銀行担当者のみ更新可能
CREATE POLICY "Admins can update all, users can update bank representatives for their clients." 
ON public.bank_representatives
FOR UPDATE 
USING (
    -- 管理者の場合は全て許可
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 1)
    OR 
    -- クライアントの場合は自分のclient_idのみ
    client_id IN (SELECT client_id FROM public.profiles WHERE id = auth.uid())
);

-- =====================================
-- 銀行担当者テーブル用更新日時自動更新トリガー
-- =====================================

-- 銀行担当者テーブル更新日トリガー作成
-- bank_representatives テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
-- （update_modified_column関数はft.sqlで既に定義済みのため、ここでは作成しません）
-- CREATE TRIGGER handle_updated_at_bank_representatives
-- BEFORE UPDATE ON bank_representatives           -- 更新"前"に発火
-- FOR EACH ROW                                    -- 行単位で実行
-- EXECUTE FUNCTION update_modified_column();      -- ft.sqlで定義済みの関数を実行

-- =====================================
-- 銀行担当者取得用 RPC 関数
-- =====================================

-- 関数が既に存在する場合は削除（正確なパラメータ型を指定）
DROP FUNCTION IF EXISTS public.get_bank_representatives_with_clients(VARCHAR, VARCHAR, VARCHAR, VARCHAR, UUID);
DROP FUNCTION IF EXISTS public.get_bank_representative_by_id(UUID, UUID); -- 修正: パラメータ型を正しく指定

-- 銀行担当者一覧取得関数（bank_representatives + clients結合）
CREATE OR REPLACE FUNCTION public.get_bank_representatives_with_clients(
    search_representative_name VARCHAR DEFAULT NULL,  -- 担当者名での部分検索
    search_bank_name VARCHAR DEFAULT NULL,            -- 銀行名での部分検索
    search_branch_name VARCHAR DEFAULT NULL,          -- 支店名での部分検索
    search_client_name VARCHAR DEFAULT NULL,          -- クライアント名での部分検索
    filter_client_id UUID DEFAULT NULL               -- クライアントIDでのフィルタ（権限制御用）
)
RETURNS TABLE (
    id UUID,
    client_id UUID,
    client_name VARCHAR,
    representative_name VARCHAR,
    lending_attitude TEXT,
    bank_code VARCHAR,
    bank_name VARCHAR,
    bank_name_half_kana VARCHAR,
    bank_name_full_kana VARCHAR,
    bank_name_hiragana VARCHAR,
    bank_business_type_code VARCHAR,
    bank_business_type VARCHAR,
    branch_code VARCHAR,
    branch_name VARCHAR,
    branch_name_half_kana VARCHAR,
    branch_name_full_kana VARCHAR,
    branch_name_hiragana VARCHAR,
    "position" VARCHAR, -- ここを修正: "position" と二重引用符で囲む
    mobile_phone VARCHAR,
    contact_phone VARCHAR,
    email_address VARCHAR,
    remarks TEXT,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        br.id AS id,
        br.client_id AS client_id,
        c.name AS client_name,
        br.representative_name AS representative_name,
        br.lending_attitude AS lending_attitude,
        br.bank_code AS bank_code,
        br.bank_name AS bank_name,
        br.bank_name_half_kana AS bank_name_half_kana,
        br.bank_name_full_kana AS bank_name_full_kana,
        br.bank_name_hiragana AS bank_name_hiragana,
        br.bank_business_type_code AS bank_business_type_code,
        br.bank_business_type AS bank_business_type,
        br.branch_code AS branch_code,
        br.branch_name AS branch_name,
        br.branch_name_half_kana AS branch_name_half_kana,
        br.branch_name_full_kana AS branch_name_full_kana,
        br.branch_name_hiragana AS branch_name_hiragana,
        br."position" AS "position", -- ここを修正: br."position" と "position" に変更
        br.mobile_phone AS mobile_phone,
        br.contact_phone AS contact_phone,
        br.email_address AS email_address,
        br.remarks AS remarks,
        br.deleted_at AS deleted_at,
        br.created_at AS created_at,
        br.updated_at AS updated_at
    FROM
        public.bank_representatives AS br
    LEFT JOIN
        public.clients AS c ON br.client_id = c.id
    WHERE
        -- 担当者名での部分検索
        (search_representative_name IS NULL OR search_representative_name = '' OR br.representative_name ILIKE '%' || search_representative_name || '%')
        AND
        -- 銀行名での部分検索
        (search_bank_name IS NULL OR search_bank_name = '' OR br.bank_name ILIKE '%' || search_bank_name || '%')
        AND
        -- 支店名での部分検索
        (search_branch_name IS NULL OR search_branch_name = '' OR br.branch_name ILIKE '%' || search_branch_name || '%')
        AND
        -- クライアント名での部分検索
        (search_client_name IS NULL OR search_client_name = '' OR c.name ILIKE '%' || search_client_name || '%')
        AND
        -- クライアントIDでのフィルタ（権限制御用）
        (filter_client_id IS NULL OR br.client_id = filter_client_id)
        AND
        -- 削除されていないレコードのみ
        br.deleted_at IS NULL
    ORDER BY
        br.updated_at DESC;
END;
$$;

-- 特定銀行担当者取得関数（IDベース）
CREATE OR REPLACE FUNCTION public.get_bank_representative_by_id(
    p_representative_id UUID,
    filter_client_id UUID DEFAULT NULL  -- クライアントIDでのフィルタ（権限制御用）
)
RETURNS TABLE (
    id UUID,
    client_id UUID,
    client_name VARCHAR,
    representative_name VARCHAR,
    lending_attitude TEXT,
    bank_code VARCHAR,
    bank_name VARCHAR,
    bank_name_half_kana VARCHAR,
    bank_name_full_kana VARCHAR,
    bank_name_hiragana VARCHAR,
    bank_business_type_code VARCHAR,
    bank_business_type VARCHAR,
    branch_code VARCHAR,
    branch_name VARCHAR,
    branch_name_half_kana VARCHAR,
    branch_name_full_kana VARCHAR,
    branch_name_hiragana VARCHAR,
    "position" VARCHAR, -- ここを修正: "position" と二重引用符で囲む
    mobile_phone VARCHAR,
    contact_phone VARCHAR,
    email_address VARCHAR,
    remarks TEXT,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER -- RLSポリシーをバイパス
AS $$
BEGIN
    RETURN QUERY
    SELECT
        br.id AS id,
        br.client_id AS client_id,
        c.name AS client_name,
        br.representative_name AS representative_name,
        br.lending_attitude AS lending_attitude,
        br.bank_code AS bank_code,
        br.bank_name AS bank_name,
        br.bank_name_half_kana AS bank_name_half_kana,
        br.bank_name_full_kana AS bank_name_full_kana,
        br.bank_name_hiragana AS bank_name_hiragana,
        br.bank_business_type_code AS bank_business_type_code,
        br.bank_business_type AS bank_business_type,
        br.branch_code AS branch_code,
        br.branch_name AS branch_name,
        br.branch_name_half_kana AS branch_name_half_kana,
        br.branch_name_full_kana AS branch_name_full_kana,
        br.branch_name_hiragana AS branch_name_hiragana,
        br."position" AS "position", -- ここを修正: br."position" と "position" に変更
        br.mobile_phone AS mobile_phone,
        br.contact_phone AS contact_phone,
        br.email_address AS email_address,
        br.remarks AS remarks,
        br.deleted_at AS deleted_at,
        br.created_at AS created_at,
        br.updated_at AS updated_at
    FROM
        public.bank_representatives AS br
    LEFT JOIN
        public.clients AS c ON br.client_id = c.id
    WHERE
        br.id = p_representative_id
        AND
        -- クライアントIDでのフィルタ（権限制御用）
        (filter_client_id IS NULL OR br.client_id = filter_client_id)
        AND
        -- 削除されていないレコードのみ
        br.deleted_at IS NULL;
END;
$$;

-- この関数を public ロールから実行できるように権限を付与します。
-- service_role キーで呼び出す場合は必須ではありませんが、念のため設定しておくと良いでしょう。
-- GRANT EXECUTE ON FUNCTION public.get_bank_representatives_with_clients(VARCHAR, VARCHAR, VARCHAR, VARCHAR, UUID) TO public; -- 引数型を正確に記述
-- GRANT EXECUTE ON FUNCTION public.get_bank_representative_by_id(UUID, UUID) TO public; -- 引数型を正確に記述