-- =====================================
-- 物件テーブル
-- =====================================
CREATE TABLE public.properties (
    -- 基本情報
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),                               -- 物件のユニークID
    client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,     -- クライアントID（外部キー）
    working_capital_id       uuid         DEFAULT NULL REFERENCES public.working_capital(id) ON DELETE SET NULL, -- 運転資金ID（外部キー）
    
    type                     smallint      DEFAULT 1,                            -- 種別(1:短期物件, 2:長期物件)
    -- 物件基本情報
    purchase_date            date         DEFAULT NULL,                          -- 購入日
    address                  varchar      DEFAULT NULL,                          -- 住所
    land_area_sqm            numeric      DEFAULT NULL,                          -- 土地面積（㎡）
    fixed_asset_tax_land     numeric      DEFAULT NULL,                          -- 固定資産税評価額（土地）
    building_area_sqm        numeric      DEFAULT NULL,                          -- 建物延べ面積(㎡）
    structure                varchar      DEFAULT NULL,                          -- 構造
    construction_date        date         DEFAULT NULL,                          -- 築年月日
    building_age             numeric      DEFAULT NULL,                          -- 築年数
    fixed_asset_tax_building numeric      DEFAULT NULL,                          -- 固定資産税評価額（建物）
    purchase_price           numeric      DEFAULT NULL,                          -- 購入金額（税込）
    
    -- 融資情報
    loan_amount              numeric      DEFAULT NULL,                          -- 借入金額
    loan_period              numeric      DEFAULT NULL,                          -- 借入期間
    loan_interest_rate       numeric      DEFAULT NULL,                          -- 借入金利
    loan_fee_rate            numeric      DEFAULT NULL,                          -- 借入手数料率
    loan_ratio               numeric      DEFAULT NULL,                          -- 借入割合
    
    -- 売却予定情報
    planned_sale_amount      numeric      DEFAULT NULL,                          -- ①売却予定金額（金額）
    planned_sale_month       numeric      DEFAULT NULL,                          -- ①売却予定金額（月）
    
    -- 銀行評価情報
    bank_collateral_value    numeric      DEFAULT NULL,                          -- 銀行担保評価額
    bank_risk_amount         numeric      DEFAULT NULL,                          -- 銀行リスク額
    
    
    -- その他情報
    remarks                  text         DEFAULT NULL,                          -- 備考
    fixed_asset_tax_total    numeric      DEFAULT NULL,                          -- 固定資産税評価額合計
    progress_status          varchar      DEFAULT NULL,                          -- 進捗状況
    closing_date             date         DEFAULT NULL,                          -- 決算日

    -- ステータス管理
    deleted_at               timestamp with time zone DEFAULT NULL,              -- 削除日時

    -- システム情報
    created_at               timestamp with time zone DEFAULT now(),             -- 登録日時
    updated_at               timestamp with time zone DEFAULT now()              -- 最終更新日時
);

-- RLS (Row Level Security) を有効にするのを忘れないでください！
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
-- =====================================
-- 物件テーブル用 RLS ポリシー
-- =====================================

-- SELECT: ユーザーが自分が所属するクライアントに紐づく物件情報を参照できる
CREATE POLICY "Users can view properties for their clients." 
ON public.properties
FOR SELECT 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- INSERT: ユーザーが自分が所属するクライアントに紐づく物件を追加できる
CREATE POLICY "Users can insert properties for their clients." 
ON public.properties
FOR INSERT 
WITH CHECK (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- UPDATE: ユーザーが自分が所属するクライアントに紐づく物件を更新できる
CREATE POLICY "Users can update properties for their clients." 
ON public.properties
FOR UPDATE 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- DELETE: ユーザーが自分が所属するクライアントに紐づく物件を削除できる
CREATE POLICY "Users can delete properties for their clients." 
ON public.properties
FOR DELETE 
USING (
    client_id IN (
        SELECT client_id 
        FROM public.profiles 
        WHERE id = auth.uid()
    )
);

-- properties更新日関数作成
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    -- レコードの updated_at カラムを現在のタイムスタンプで更新
    -- 注意: updated_at が TIMESTAMP WITHOUT TIME ZONE の場合、データベースセッションのタイムゾーンに依存します。
    -- TIMESTAMPTZ (TIMESTAMP WITH TIME ZONE) の方がタイムゾーン問題を避けるため推奨されます。
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql; -- <-- ここが重要！関数の終わりを示す閉じタグと言語指定

-- properties更新日トリガー作成
-- properties テーブルの更新"前"に発火し、update_modified_column 関数を実行します。
-- これにより、updated_at カラムが自動的に更新されます。
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON properties        -- 更新"前"に発火
FOR EACH ROW                         -- 行単位で実行
EXECUTE FUNCTION update_modified_column();  -- 上記関数を実行

-- 論理削除関数（API呼び出し用）
CREATE OR REPLACE FUNCTION soft_delete_property(property_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE public.properties 
    SET deleted_at = NOW(), updated_at = NOW()
    WHERE id = property_uuid 
        AND deleted_at IS NULL
        AND client_id IN (
            SELECT client_id 
            FROM public.profiles 
            WHERE id = auth.uid()
        );
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 
